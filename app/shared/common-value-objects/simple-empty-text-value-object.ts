import { Result } from '../core/Result';
import { ValueObject } from '../domain/ValueObject';

interface ISimpleEmptyTextProps {
  value: string;
}
export class SimpleEmptyTextValueObject extends ValueObject<ISimpleEmptyTextProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: ISimpleEmptyTextProps) {
    super(props);
  }

  public static create(text: string): Result<SimpleEmptyTextValueObject> {
    const data = text ? text : '-';

    return Result.ok<SimpleEmptyTextValueObject>(
      new SimpleEmptyTextValueObject({ value: data })
    );
  }
}
