import { Result } from '../core/Result';
import { ValueObject } from '../domain/ValueObject';

export type CountryOptions =
  | 'COSTA_RICA'
  | 'EL_SALVADOR'
  | 'PANAMA'
  | 'GUATEMALA'
  | 'HONDURAS';
interface ICountryProps {
  value: CountryOptions;
}
export class Country extends ValueObject<ICountryProps> {
  get value(): CountryOptions {
    return this.props.value;
  }

  private constructor(props: ICountryProps) {
    super(props);
  }

  private static isCountryValid(country: string) {
    if (
      !country ||
      (country !== 'COSTA_RICA' &&
        country !== 'EL_SALVADOR' &&
        country !== 'PANAMA' &&
        country !== 'GUATEMALA' &&
        country !== 'HONDURAS')
    ) {
      return false;
    }

    return true;
  }

  public static create(applicationType: string): Result<Country> {
    if (!this.isCountryValid(applicationType)) {
      return Result.fail<Country>('Invalid Country');
    }

    return Result.ok<Country>(
      new Country({ value: applicationType as CountryOptions })
    );
  }
}
