import { Guard } from '../core/Guard';
import { Result } from '../core/Result';
import { ValueObject } from '../domain/ValueObject';

interface ISimpleTextProps {
  value: string;
}
export class SimpleTextValueObject extends ValueObject<ISimpleTextProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: ISimpleTextProps) {
    super(props);
  }

  public static create(
    argumentName: string,
    text: string
  ): Result<SimpleTextValueObject> {
    const textValidation = Guard.againstNullOrUndefined(text, argumentName);
    if (textValidation.isFailure) {
      return Result.fail<SimpleTextValueObject>(textValidation.getErrorValue());
    }

    return Result.ok<SimpleTextValueObject>(
      new SimpleTextValueObject({ value: text.toString() })
    );
  }
}
