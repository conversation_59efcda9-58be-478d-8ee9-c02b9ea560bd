import * as AWS from 'aws-sdk';

/**
 * S3 Repository for storing and reading files from AWS S3
 * @jira
 * - Created at: https://techbitecr.atlassian.net/browse/FPS-14
 */
export class StoreService {
  readonly s3: AWS.S3;

  constructor(s3: AWS.S3) {
    this.s3 = s3;
  }

  /**
   * Stores a file in AWS S3
   * @param file The file to store
   * @param bucketName The name of the S3 bucket to store the file in
   * @param key The key (filename) to assign to the file in S3
   * @returns The stored file's S3 URL
   */
  public async storeFile(
    file: Buffer,
    bucketName: string,
    key: string
  ): Promise<string> {
    try {
      const params: AWS.S3.PutObjectRequest = {
        Bucket: bucketName,
        Key: key,
        Body: file,
        ACL: 'public-read',
      };
      await this.s3.upload(params).promise();
      const url = `https://${bucketName}.s3.amazonaws.com/${key}`;

      return url;
    } catch (error) {
      // eslint-disable-next-line
      console.log("[S3Repository][storeFile] error:", error);
      throw error;
    }
  }

  /**
   * Retrieves a file from AWS S3
   * @param bucketName The name of the S3 bucket where the file is stored
   * @param key The key (filename) of the file in S3
   * @returns The file as a Buffer
   */
  public async getFile(bucketName: string, key: string): Promise<Buffer> {
    try {
      const params: AWS.S3.GetObjectRequest = {
        Bucket: bucketName,
        Key: key,
      };
      const response = await this.s3.getObject(params).promise();

      return response.Body as Buffer;
    } catch (error) {
      // eslint-disable-next-line
      console.log("[S3Repository][getFile] error:", error);
      throw error;
    }
  }
}
