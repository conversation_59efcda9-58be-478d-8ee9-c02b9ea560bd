import * as AWS from 'aws-sdk';
import { DynamoDB } from 'aws-sdk';
import { ItemList, QueryInput, ScanInput } from 'aws-sdk/clients/dynamodb';

export class DynamoServices {
  public static async queryAllElements(params: QueryInput) {
    const dynamoDb = new DynamoDB();
    let response: ItemList = [];
    do {
      const result = await dynamoDb.query(params).promise();
      if (result.Items) {
        response = [...response, ...result.Items];
      }
      params.ExclusiveStartKey = result.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);
    const unmarshallResponse = (response || []).map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );
    // eslint-disable-next-line no-console
    console.log('Repository result', unmarshallResponse);

    return unmarshallResponse;
  }

  public static async scanAllElements(params: ScanInput) {
    const dynamoDb = new DynamoDB();
    let response: ItemList = [];
    do {
      const result = await dynamoDb.scan(params).promise();
      if (result.Items) {
        response = [...response, ...result.Items];
      }
      params.ExclusiveStartKey = result.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);
    const unmarshallResponse = (response || []).map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );
    // eslint-disable-next-line no-console
    console.log('Repository result', unmarshallResponse);

    return unmarshallResponse;
  }
}
