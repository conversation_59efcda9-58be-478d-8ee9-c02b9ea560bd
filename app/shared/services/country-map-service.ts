import { Country, CountryOptions } from '../common-value-objects/country';

export class CountryMapService {
  public static toHuman(countryString: string) {
    const countryOrError = Country.create(countryString as CountryOptions);
    if (countryOrError.isFailure) {
      return '-';
    }
    const countryLibrary = {
      COSTA_RICA: 'Costa Rica',
      EL_SALVADOR: 'El Salvador',
      PANAMA: 'Panama',
      GUATEMALA: 'Guatemala',
      HONDURAS: 'Honduras',
    };

    return countryLibrary[countryOrError.getValue().value] || '-';
  }
}
