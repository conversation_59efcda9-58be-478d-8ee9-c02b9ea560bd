/* eslint-disable no-console */
import * as AWS from 'aws-sdk';

interface ISNSEventServiceProps {
  message: string;
}
export class SNSEventService {
  readonly sns: AWS.SNS;

  readonly topicArn: string;

  constructor(sns: AWS.SNS, topicArn: string) {
    this.sns = sns;
    this.topicArn = topicArn;
  }

  public async publishEventToTopic({ message }: ISNSEventServiceProps) {
    const params = {
      Message: message,
      TopicArn: this.topicArn,
    };
    try {
      const result = await this.sns.publish(params).promise();
      console.log(`Message id: ${result.MessageId}`);
      console.log(
        `Message published to SNS topic ${params.TopicArn}: ${result.MessageId}`
      );
    } catch (err) {
      console.log('Error', err);
    }
  }
}
