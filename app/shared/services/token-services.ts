// import * as jwt from 'jsonwebtoken';

import { CognitoJwtVerifier } from 'aws-jwt-verify';
import { decomposeUnverifiedJwt, validateJwtFields } from 'aws-jwt-verify/jwt';
import { CognitoJwtPayload, JwtPayload } from 'aws-jwt-verify/jwt-model';

interface ITokenValidateProps {
  token: string;
  userPoolId: string;
  clientId: string;
  tokenUse: 'access' | 'id' | null | undefined;
}
export class TokenServices {
  public async getTokenData(token: string) {
    const payload = await this.decodeToken(token);

    return payload;
  }

  public async decodeToken(token: string) {
    return decomposeUnverifiedJwt(token);
  }

  public async isTokenValid(payload: JwtPayload) {
    try {
      validateJwtFields(payload, {});

      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('validateToken error:', error);

      return false;
    }
  }

  public async cognitoTokenVerifier({
    token,
    userPoolId,
    clientId,
    tokenUse,
  }: ITokenValidateProps): Promise<CognitoJwtPayload> {
    try {
      const verifier = CognitoJwtVerifier.create({
        userPoolId: userPoolId,
        tokenUse: tokenUse ?? 'id',
        clientId: clientId,
      });

      return await verifier.verify(token);
    } catch (error) {
      throw new Error('Invalid Cognito JWT Token');
    }
  }
}
