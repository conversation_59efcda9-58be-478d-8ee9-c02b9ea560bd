export class DateServices {
  public static calculatePorcentualDateProgress(
    startDateISO: string,
    endDateISO: string
  ): number {
    const startDate = new Date(startDateISO);
    const endDate = new Date(endDateISO);
    const today = new Date();
    // Ensure that the dates are valid
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Invalid date format');
    }
    if (today < startDate) {
      // Today is earlier than the start date, so progress is 0%
      return 0;
    } else if (today >= endDate) {
      // Today is after or equal to the end date, so progress is 100%
      return 100;
    } else {
      // Calculate the progress based on the time elapsed
      const totalDuration = endDate.getTime() - startDate.getTime();
      const elapsedDuration = today.getTime() - startDate.getTime();
      const progress = (elapsedDuration / totalDuration) * 100;

      // Round the progress to two decimal places
      return Math.round(progress * 100) / 100;
    }
  }

  public static mapISODateToCustomFormat = (isoDateString: string) => {
    const date = new Date(isoDateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12;
    const formattedDate = `${day}/${month}/${year} ${formattedHours}:${minutes} ${ampm}`;

    return formattedDate;
  };

  public static mapISODateToDateWithoutTime = (isoDateString: string) => {
    const date = new Date(isoDateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const formattedDate = `${day}/${month}/${year} `;

    return formattedDate;
  };

  public static calculateAgeBasedOnDate(birthDateIso: string): number {
    const birthDate = new Date(birthDateIso);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();
    // Check if the birthday hasn't occurred yet this year
    if (
      monthDifference < 0 ||
      (monthDifference === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }
}
