/* eslint-disable no-console */
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';

const sqsClient = new SQSClient({ region: 'sa-east-1' });
interface IEventServiceProps {
  body: string;
  author?: string;
  url?: string;
  delay?: number;
}
export class EventService {
  private url: string;

  private author: string;

  constructor(url: string, author: string) {
    this.url = url;
    this.author = author;
  }

  public async publishEvent({ body, author, url, delay }: IEventServiceProps) {
    const params = {
      DelaySeconds: delay || 0,
      MessageAttributes: {
        Author: {
          DataType: 'String',
          StringValue: author || this.author,
        },
      },
      MessageBody: body,
      QueueUrl: url || this.url,
    };
    try {
      const data = await sqsClient.send(new SendMessageCommand(params));
      if (data) {
        // eslint-disable-next-line no-console
        console.log('Success, message sent. MessageID:', data.MessageId);
        console.log(
          'Message Send to SQS- Here is MessageId: ' + data.MessageId
        );
      } else {
        console.log('Some error occured !!');
      }
    } catch (err) {
      console.log('Error', err);
    }
  }
}
