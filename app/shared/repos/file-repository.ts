import { PutObjectOutput } from 'aws-sdk/clients/s3';

interface IStoreFileResponse {
  status: 'ok' | 'error';
  result?: PutObjectOutput;
  url?: string;
  error?: Error;
}
export class S3Service {
  readonly s3: AWS.S3;

  readonly bucketName: string;

  constructor(s3: AWS.S3, bucket: string) {
    this.s3 = s3;
    this.bucketName = bucket;
  }

  public async storePublicBase64File({
    base64File,
    bucket,
    key,
  }: {
    base64File: string;
    bucket: string;
    key: string;
  }): Promise<IStoreFileResponse> {
    try {
      const params = {
        Key: key,
        Bucket: `${this.bucketName}/${bucket}`,
        Body: this.decodeBase64FileToBuffer(base64File),
        ACL: 'public-read',
      };
      const result = await this.s3.putObject(params).promise();
      // eslint-disable-next-line no-console
      console.log(
        'storePublicBase64File',
        `Successful ${this.getFileURL(params)} stored`
      );

      return {
        status: 'ok',
        result,
        url: this.getFileURL(params),
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);

      return {
        status: 'error',
        error: error as Error,
      };
    }
  }

  public async storePublicJSONFile({
    body,
    key,
  }: {
    body: string;
    key: string;
  }): Promise<IStoreFileResponse> {
    try {
      const params = {
        Key: key,
        Bucket: `${this.bucketName}`,
        Body: body,
        ContentType: 'application/json',
      };
      const result = await this.s3.putObject(params).promise();
      // eslint-disable-next-line no-console
      console.log(
        'storePublicBase64File',
        `Successful ${this.getFileURL(params)} stored`
      );

      return {
        status: 'ok',
        result,
        url: this.getFileURL(params),
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);

      return {
        status: 'error',
        error: error as Error,
      };
    }
  }

  private decodeBase64FileToBuffer(base64: string): Buffer {
    const binaryString = atob(base64.split(',')[1]);
    const binaryArray = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      binaryArray[i] = binaryString.charCodeAt(i);
    }

    return Buffer.from(binaryArray.buffer);
  }

  public async getFileFromS3Bucket({
    bucket,
    key,
  }: {
    bucket: string;
    key: string;
  }) {
    try {
      const result = await this.s3
        .getObject({ Bucket: bucket, Key: key })
        .promise();
      if (!result.Body) throw 'Error while getting the file';

      return {
        status: 'ok',
        file: result.Body,
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);

      return {
        status: 'fail',
        error,
      };
    }
  }

  // eslint-disable-next-line @typescript-eslint/naming-convention
  private getFileURL({ Bucket, Key }: { Bucket: string; Key: string }) {
    // eslint-disable-next-line
    const [bucket, path] = Bucket.split('/');
    const subFolder = path && path.length > 0 ? path + '/' : '';

    return `https://${this.bucketName}.s3.sa-east-1.amazonaws.com/${subFolder}${Key}`;
  }
}
