export class CognitoRepository {
  private readonly congnito: AWS.CognitoIdentityServiceProvider;

  private readonly cognitoPoolId: string;

  constructor(cognito: AWS.CognitoIdentityServiceProvider, poolId: string) {
    this.cognitoPoolId = poolId;
    this.congnito = cognito;
  }

  public async getUerById(id: string) {
    const params = {
      UserPoolId: this.cognitoPoolId,
      Username: id,
    };
    const result = await this.congnito.adminGetUser(params).promise();

    return result;
  }

  public async getUerByAccessToken(token: string) {
    const result = await this.congnito
      .getUser({ AccessToken: token })
      .promise();

    return result;
  }

  public async createUser({
    username,
    temporaryPassword,
    email,
  }: {
    username: string;
    temporaryPassword: string;
    email: string;
  }) {
    const params = {
      UserPoolId: this.cognitoPoolId,
      Username: username,
      TemporaryPassword: temporaryPassword,
      UserAttributes: [
        {
          Name: 'email',
          Value: email,
        },
        {
          Name: 'email_verified',
          Value: 'true',
        },
      ],
    };
    const result = await this.congnito.adminCreateUser(params).promise();

    return result;
  }

  public async changeUserPassword({
    username,
    password,
    isPermanent = false,
  }: {
    username: string;
    password: string;
    isPermanent?: boolean;
  }) {
    const params = {
      UserPoolId: this.cognitoPoolId,
      Username: username,
      Password: password,
      Permanent: isPermanent,
    };
    const result = await this.congnito.adminSetUserPassword(params).promise();

    return result;
  }

  public async disabledUser(username: string) {
    const params = {
      UserPoolId: this.cognitoPoolId,
      Username: username,
    };
    const result = await this.congnito.adminDisableUser(params).promise();

    return result;
  }

  public async enableUser(username: string) {
    const params = {
      UserPoolId: this.cognitoPoolId,
      Username: username,
    };
    const result = await this.congnito.adminEnableUser(params).promise();

    return result;
  }
}
