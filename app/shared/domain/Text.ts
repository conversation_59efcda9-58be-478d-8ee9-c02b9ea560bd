import { Result } from '../core/Result';
import { ValueObject } from './ValueObject';

interface ITextProps {
  value: string;
}
/**
 * @jira
 * - Created at: https://techbitecr.atlassian.net/browse/FPS-9
 */
export class Text extends ValueObject<ITextProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: ITextProps) {
    super(props);
  }

  private static isValidUUID(text?: string) {
    return typeof text === 'string' && text !== undefined;
  }

  public static create(cta?: string): Result<Text> {
    if (cta === undefined) {
      return Result.ok<Text>(new Text({ value: '' }));
    }
    if (!this.isValidUUID(cta)) {
      return Result.fail<Text>('Invalid Text');
    }

    return Result.ok<Text>(new Text({ value: cta ?? '' }));
  }

  public updateValue(update: Partial<ITextProps>): void {
    if (update.value) {
      this.props.value = update.value;
    }
  }
}
