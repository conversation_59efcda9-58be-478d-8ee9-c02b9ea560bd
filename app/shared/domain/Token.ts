import { Result } from '../core/Result';
import { ValueObject } from './ValueObject';

interface ITokenProps {
  value: string;
}
/**
 * @jira
 * - Created at: https://techbitecr.atlassian.net/browse/FPS-9
 */
export class Token extends ValueObject<ITokenProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: ITokenProps) {
    super(props);
  }

  private static isTokenValid(token: string) {
    const parts = token.split('.');

    return parts.length === 3;
  }

  public static create(jwt: string): Result<Token> {
    if (!this.isTokenValid(jwt)) {
      return Result.fail<Token>('Invalid JWT Structure.');
    }

    return Result.ok<Token>(new Token({ value: jwt }));
  }
}
