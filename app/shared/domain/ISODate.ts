import { Result } from '../core/Result';
import { ValueObject } from './ValueObject';

interface IISODateProps {
  value: string;
}
/**
 * @jira
 * - Created at: https://techbitecr.atlassian.net/browse/FPS-9
 */
export class ISODate extends ValueObject<IISODateProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IISODateProps) {
    super(props);
  }

  private static isValid(date?: string) {
    return typeof date === 'string';
  }

  public static create(date?: string): Result<ISODate> {
    const dateProp = date ? date : new Date().toISOString();
    if (!this.isValid(dateProp)) {
      return Result.fail<ISODate>('Invalid ISO Date');
    }

    return Result.ok<ISODate>(new ISODate({ value: dateProp }));
  }
}
