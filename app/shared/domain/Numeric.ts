import { Result } from '../core/Result';
import { ValueObject } from './ValueObject';

interface ITextProps {
  value: number;
}
/**
 * @jira
 * - Created at: https://techbitecr.atlassian.net/browse/FPS-9
 */
export class Numeric extends ValueObject<ITextProps> {
  get value(): number {
    return this.props.value;
  }

  private constructor(props: ITextProps) {
    super(props);
  }

  private static isValid(num?: number) {
    return typeof num === 'number' && num !== undefined;
  }

  public static create(num?: number): Result<Numeric> {
    if (!this.isValid(num)) {
      return Result.fail<Numeric>('Invalid Number');
    }

    return Result.ok<Numeric>(new Numeric({ value: num ?? 0 }));
  }
}
