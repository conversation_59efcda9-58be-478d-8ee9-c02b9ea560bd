import { Result } from '../core/Result';
import { ValueObject } from './ValueObject';

interface IBooleanProps {
  value: boolean;
}
/**
 * @jira
 * - Created at: https://techbitecr.atlassian.net/browse/FPS-9
 */
export class BoolT extends ValueObject<IBooleanProps> {
  get value(): boolean {
    return this.props.value;
  }

  private constructor(props: IBooleanProps) {
    super(props);
  }

  private static isValid(bool?: boolean) {
    return typeof bool === 'boolean' && bool !== undefined;
  }

  public static create(bool?: boolean): Result<BoolT> {
    if (!this.isValid(bool)) {
      return Result.fail<BoolT>('Invalid Bool');
    }

    return Result.ok<BoolT>(new BoolT({ value: bool ?? false }));
  }
}
