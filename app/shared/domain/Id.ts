import { Result } from '../core/Result';
import { ValueObject } from './ValueObject';
import { v4 as uuidv4 } from 'uuid';

interface IIDProps {
  value: string;
}
/**
 * @jira
 * - Created at: https://techbitecr.atlassian.net/browse/FPS-9
 */
export class ID extends ValueObject<IIDProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IIDProps) {
    super(props);
  }

  private static isValid(id: string) {
    if (typeof id !== 'string' || id === undefined) {
      return false;
    }
    const pattern =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;

    return pattern.test(id);
  }

  public static create(id?: string): Result<ID> {
    const idProp = id ? id : uuidv4();
    if (!this.isValid(idProp)) {
      return Result.fail<ID>(`Invalid ID ${JSON.stringify(idProp)}`);
    }

    return Result.ok<ID>(new ID({ value: idProp }));
  }
}
