export abstract class BaseHandler {
  ok<T>(result: T) {
    // eslint-disable-next-line no-console
    console.log('Endpoint Response: ', result);

    return {
      statusCode: 200,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      headers: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'Content-Type': 'text/json',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(result),
    };
  }

  fail(error: Error | string) {
    // eslint-disable-next-line no-console
    console.log('Endpoint Error: ', error);
    const errorOutput: Error =
      typeof error === 'string' ? new Error(error) : error;

    return {
      statusCode: 400,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      headers: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'Content-Type': 'text/json',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'Access-Control-Allow-Origin': '*',
      },
      body: errorOutput.toString(),
    };
  }
}
