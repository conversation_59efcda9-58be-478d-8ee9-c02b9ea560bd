import { TokenServices } from '../services/token-services';

export class TokenMiddleware {
  constructor(
    private tokenServices: TokenServices,
    private readonly userPoolId?: string,
    private readonly clientId?: string
  ) {}

  public async getUserIdFromToken(token: string) {
    const { payload } = await this.tokenServices.getTokenData(token);
    const isValid = this.tokenServices.isTokenValid(payload);
    if (!isValid) throw 'Invalid Token';
    if (!payload['cognito:username']) throw 'Error Getting User Id From Token';

    return payload['cognito:username'] as string;
  }

  public async getUserIdFromCognitoToken(token: string): Promise<string> {
    if (!this.userPoolId || !this.clientId) {
      throw 'Missing Cognito Data';
    }
    const payload = await this.tokenServices.cognitoTokenVerifier({
      token,
      userPoolId: this.userPoolId,
      clientId: this.clientId,
      tokenUse: 'id',
    });
    if (!payload['cognito:username']) throw 'Error Getting User Id From Token';

    return payload['cognito:username'] as string;
  }
}
