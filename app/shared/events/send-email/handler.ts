import { Context, SQSEvent } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import AWS from 'aws-sdk';
import { SendEmailDTO } from './dto';
import { SendEmailUseCase } from './use-case';
import { <PERSON><PERSON>andler } from '../../infra/handler';
import { EmailServices } from '../../services/email-services';

class Lambda extends BaseHandler implements LambdaInterface {
  private sendEmailUseCase: SendEmailUseCase;

  private init() {
    const url = process.env.PLATFORM_URL || '';
    const awsRegion = process.env.SES_REGION || '';
    const emailServices = new EmailServices(
      new AWS.SES({ region: awsRegion }),
      process.env.SOURCE_EMAIL || '',
      url
    );
    this.sendEmailUseCase = new SendEmailUseCase(emailServices);
  }

  public async handler(event: SQSEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.sendEmailUseCase.execute(dto);

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: SQSEvent): SendEmailDTO[] {
    // eslint-disable-next-line no-console
    console.log('event: ', JSON.stringify(event));
    const messages: SendEmailDTO[] = event.Records.map((record) => {
      const body = JSON.parse(record.body);

      return body;
    });

    return messages;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
