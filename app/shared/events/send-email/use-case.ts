import { Guard } from '../../core/Guard';
import { Result } from '../../core/Result';
import { UseCase } from '../../core/use-case';
import { EmailServices } from '../../services/email-services';
import { SendEmailDTO } from './dto';
import { LogUseCaseDTO } from '../../../shared/infra/decorators/use-case';

export class SendEmailUseCase
  implements UseCase<SendEmailDTO[], SendEmailDTO[]>
{
  private areEmailNotificationsEnabled: boolean =
    process.env.EMAIL_NOTIFICATION_ENABLED === 'ON' ? true : false;

  constructor(private emailServices: EmailServices) {}

  @LogUseCaseDTO
  public async execute(dto: SendEmailDTO[]) {
    // eslint-disable-next-line no-console
    console.log(
      'Are Email Notifications Enabled: ',
      this.areEmailNotificationsEnabled
    );
    const result = await this.sendEmailNotification(dto);

    return result;
  }

  private async sendEmailNotification(dto: SendEmailDTO[]) {
    for (const data of dto) {
      await this.sendEmail(data);
    }

    return dto;
  }

  private async sendEmail(dto: SendEmailDTO) {
    const email = Guard.againstNullOrUndefined(dto.email, 'email');
    const subject = Guard.againstNullOrUndefined(dto.subject, 'subject');
    const body = Guard.againstNullOrUndefined(dto.body, 'body');
    const dtoResult = Result.combine([email, subject, body]);
    if (dtoResult.isFailure) {
      throw dtoResult.getErrorValue();
    }
    if (this.areEmailNotificationsEnabled === true) {
      await this.emailServices.sendSimpleHTMLMessage(
        dto.email,
        dto.subject,
        dto.body,
        dto.callToAction
      );
    }

    return dto;
  }
}
