import { Context, SQSEvent } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import AWS from 'aws-sdk';
import { SendNotificationDTO } from './dto';
import { SendNotificationUseCase } from './use-case';
import { <PERSON>Handler } from '../../infra/handler';
import { EventService } from '../../services/publish-sqs-event-service';
import { UserRepository } from '../../../modules/user/repo/user-repository';
import { LogHandlerEvent } from '../../infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private sendNotificationUseCase: SendNotificationUseCase;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.USER_TABLE_NAME || ''
    );
    const emailEventService = new EventService(
      process.env.EMAIL_SQS_URL || '',
      'create-auth-user'
    );
    const inAppEventService = new EventService(
      process.env.IN_APP_SQS_URL || '',
      'create-auth-user'
    );
    this.sendNotificationUseCase = new SendNotificationUseCase(
      userRepository,
      emailEventService,
      inAppEventService
    );
  }

  @LogHandlerEvent
  public async handler(event: SQSEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      for (const message of dto) {
        try {
          await this.sendNotificationUseCase.execute(message);
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('Error sending notification: ', error);
        }
      }

      return this.ok('DONE');
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: SQSEvent): SendNotificationDTO[] {
    // eslint-disable-next-line no-console
    console.log('event: ', JSON.stringify(event));
    const messages: SendNotificationDTO[] = event.Records.map((record) => {
      const body = JSON.parse(record.body);

      return body;
    });

    return messages;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
