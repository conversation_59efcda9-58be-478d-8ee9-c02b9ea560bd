import { UseCase } from '../../core/use-case';
import { SendNotificationDTO } from './dto';
import { LogUseCaseDTO } from '../../../shared/infra/decorators/use-case';
import { UserRepository } from '../../../modules/user/repo/user-repository';
import { UUIDValueObject } from '../../common-value-objects/uuid-value-object';
import { EmailNotificationDTO, InAppNotificationDTO } from './types';
import { EventService } from '../../services/publish-sqs-event-service';

export class SendNotificationUseCase
  implements UseCase<SendNotificationDTO, Promise<void>>
{
  constructor(
    private userRepository: UserRepository,
    private emailEventService: EventService,
    private inAppEventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute(dto: SendNotificationDTO) {
    const id = this.mapDTOToQueryParams(dto);
    const email = await this.getUserEmail(id);
    await this.sendInAppNotification(this.getInAppNotificationDTO(dto));
    await this.sendEmailNotification(this.getEmailNotificationDTO(email, dto));

    return;
  }

  private mapDTOToQueryParams(dto: SendNotificationDTO) {
    const id = UUIDValueObject.create('User ID', dto.userId);
    if (id.isFailure) {
      throw new Error(id.getErrorValue().value);
    }

    return id.getValue();
  }

  private async getUserEmail(userId: UUIDValueObject) {
    const user = await this.userRepository.getUserById(userId);
    if (!user || !user.email.value) {
      throw new Error('User not found');
    }

    return user.email.value;
  }

  private async sendInAppNotification(dto: InAppNotificationDTO) {
    try {
      await this.inAppEventService.publishEvent({ body: JSON.stringify(dto) });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error sending in app notification', error);
    }
  }

  private async sendEmailNotification(dto: EmailNotificationDTO) {
    try {
      await this.emailEventService.publishEvent({ body: JSON.stringify(dto) });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error sending email notification', error);
    }
  }

  private getInAppNotificationDTO(
    dto: SendNotificationDTO
  ): InAppNotificationDTO {
    return {
      userId: dto.userId,
      title: dto.title,
      message: dto.message,
      notificationType: dto.notificationType,
      callToAction: dto.callToAction,
      priority: dto.priority,
    };
  }

  private getEmailNotificationDTO(
    email: string,
    dto: SendNotificationDTO
  ): EmailNotificationDTO {
    return {
      email,
      subject: dto.title,
      body: dto.message,
      callToAction: dto.callToAction,
    };
  }
}
