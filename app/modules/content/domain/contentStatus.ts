import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

export type ContentStatusOptions = 'ACTIVE' | 'ARCHIVED';
interface IContentStatusProps {
  value: string;
}
export class ContentStatus extends ValueObject<IContentStatusProps> {
  private constructor(props: IContentStatusProps) {
    super(props);
  }

  get value(): ContentStatusOptions {
    return this.props.value as ContentStatusOptions;
  }

  updateValue(update: Partial<IContentStatusProps>): void {
    if (update.value) {
      this.props.value = update.value;
    }
  }

  public static create(props: IContentStatusProps) {
    const data = new ContentStatus({
      ...props,
    });

    return Result.ok<ContentStatus>(data);
  }
}
