import { Result } from '../../../shared/core/Result';
import { ID } from '../../../shared/domain/Id';
import { Text } from '../../../shared/domain/Text';
import { ValueObject } from '../../../shared/domain/ValueObject';

export interface IContentUserProps {
  userId: ID;
  userName: Text;
}
export class ContentUser extends ValueObject<IContentUserProps> {
  private constructor(props: IContentUserProps) {
    super(props);
  }

  getUserId(): ID {
    return this.props.userId;
  }

  getUserName(): Text {
    return this.props.userName;
  }

  updateContentUser(update: Partial<IContentUserProps>): void {
    if (update.userId) {
      this.props.userId = update.userId;
    }
    if (update.userName) {
      this.props.userName = update.userName;
    }
  }

  public static create(props: IContentUserProps) {
    const data = new ContentUser({
      ...props,
    });

    return Result.ok<ContentUser>(data);
  }
}
