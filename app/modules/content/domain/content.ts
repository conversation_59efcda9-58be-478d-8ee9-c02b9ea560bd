import { Result } from '../../../shared/core/Result';
import { ISODate } from '../../../shared/domain/ISODate';
import { ID } from '../../../shared/domain/Id';
import { Text } from '../../../shared/domain/Text';
import { ValueObject } from '../../../shared/domain/ValueObject';
import { ContentStatus } from './contentStatus';
import { ContentUser } from './contentUser';

export interface IContentProps {
  id?: ID;
  userId: ID;
  userName: Text;
  content: Text;
  imagesURL: Array<Text>;
  externalLinks: Array<Text>;
  contentStatus: ContentStatus;
  likes: Array<ContentUser>;
  createdAt?: ISODate;
  updatedAt?: ISODate;
}
export class Content extends ValueObject<IContentProps> {
  private constructor(props: IContentProps) {
    super(props);
  }

  get id(): ID | undefined {
    return this.props.id;
  }

  get userId(): ID {
    return this.props.userId;
  }

  get userName(): Text {
    return this.props.userName;
  }

  get content(): Text {
    return this.props.content;
  }

  get imagesURL(): Array<Text> {
    return this.props.imagesURL;
  }

  get externalLinks(): Array<Text> {
    return this.props.externalLinks;
  }

  get contentStatus(): ContentStatus {
    return this.props.contentStatus;
  }

  get likes(): Array<ContentUser> {
    return this.props.likes;
  }

  get createdAt(): ISODate | undefined {
    return this.props.createdAt;
  }

  get updatedAt(): ISODate | undefined {
    return this.props.updatedAt;
  }

  public updateContentData(update: Partial<IContentProps>): void {
    if (update.id) {
      const id = ID.create(update.id.value);
      if (id.isFailure) throw id.getErrorValue();
      this.props.id = id.getValue();
    }
    if (update.userId) {
      const userId = ID.create(update.userId.value);
      if (userId.isFailure) throw userId.getErrorValue();
      this.props.userId = userId.getValue();
    }
    if (update.userName) {
      const userName = Text.create(update.userName.value);
      if (userName.isFailure) throw userName.getErrorValue();
      this.props.userName = userName.getValue();
    }
    if (update.content) {
      const content = Text.create(update.content.value);
      if (content.isFailure) throw content.getErrorValue();
      this.props.content = content.getValue();
    }
    if (update.imagesURL) {
      const dbCombines = Result.combine([
        ...update.imagesURL.map((url) => Text.create(url.value)),
      ]);
      if (dbCombines.isFailure) throw dbCombines.getErrorValue();
      this.props.imagesURL = update.imagesURL.map((url) =>
        Text.create(url.value).getValue()
      );
    }
    if (update.externalLinks) {
      const dbCombines = Result.combine([
        ...update.externalLinks.map((link) => Text.create(link.value)),
      ]);
      if (dbCombines.isFailure) throw dbCombines.getErrorValue();
      this.props.externalLinks = update.externalLinks.map((link) =>
        Text.create(link.value).getValue()
      );
    }
    if (update.likes) {
      this.props.likes = update.likes;
    }
    this.props.updatedAt = ISODate.create().getValue();
  }

  public updateLikeCount(update: Partial<IContentProps>): void {
    if (update.likes) {
      this.props.likes = update.likes;
    }
    this.props.updatedAt = ISODate.create().getValue();
  }

  public static create(props: IContentProps) {
    const data = new Content({
      ...props,
    });

    return Result.ok<Content>(data);
  }
}
