import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { GetContentByIdUseCase } from './use-case';
import { ContentMap } from '../../mappers/content-map';
import { ContentRepository } from '../../repo/content-repo';
import { GetContentByIdDTO } from './dto';
import { GetContentByIdSchemea } from './schema';

class Lambda extends BaseHandler implements LambdaInterface {
  private getLatestContentUseCase: GetContentByIdUseCase;

  private init() {
    const repo = new ContentRepository(
      new AWS.DynamoDB(),
      process.env.CONTENT_TABLE_NAME || ''
    );
    this.getLatestContentUseCase = new GetContentByIdUseCase(repo);
  }

  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result = await this.getLatestContentUseCase.execute(dto);
      if (!result) {
        throw new Error('Content does not exists');
      }

      return this.ok(ContentMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<GetContentByIdDTO> {
    const dto = await GetContentByIdSchemea.validate({
      id: event.queryStringParameters?.id,
    });

    return dto;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
