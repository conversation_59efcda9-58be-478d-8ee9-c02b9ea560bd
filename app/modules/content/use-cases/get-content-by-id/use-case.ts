import { UseCase } from '../../../../shared/core/use-case';
import { ID } from '../../../../shared/domain/Id';
import { Content } from '../../domain/content';
import { ContentRepository } from '../../repo/content-repo';
import { GetContentByIdDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class GetContentByIdUseCase
  implements UseCase<GetContentByIdDTO, Promise<Content | null>>
{
  constructor(private repository: ContentRepository) {}

  @LogUseCaseDTO
  public async execute(dto: GetContentByIdDTO) {
    const id = ID.create(dto.id);
    if (id.isFailure) {
      throw new Error('Invalid ID');
    }
    const result = await this.repository.getById(id.getValue());

    return result;
  }
}
