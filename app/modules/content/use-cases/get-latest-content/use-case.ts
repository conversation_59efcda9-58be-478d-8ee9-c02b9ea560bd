import { UseCase } from '../../../../shared/core/use-case';
import { Content } from '../../domain/content';
import { ContentRepository } from '../../repo/content-repo';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class GetLatestContentUseCase
  implements UseCase<void, Promise<Content[]>>
{
  constructor(private repository: ContentRepository) {}

  @LogUseCaseDTO
  public async execute() {
    const result = await this.repository.getLatestContent();

    return result;
  }
}
