import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { GetLatestContentUseCase } from './use-case';
import { ContentMap } from '../../mappers/content-map';
import { ContentRepository } from '../../repo/content-repo';

class Lambda extends BaseHandler implements LambdaInterface {
  private getLatestContentUseCase: GetLatestContentUseCase;

  private init() {
    const repo = new ContentRepository(
      new AWS.DynamoDB(),
      process.env.CONTENT_TABLE_NAME || ''
    );
    this.getLatestContentUseCase = new GetLatestContentUseCase(repo);
  }

  public async handler(_event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const result = await this.getLatestContentUseCase.execute();

      return this.ok(result.map((content) => ContentMap.toDTO(content)));
    } catch (error) {
      return this.fail(error as Error);
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
