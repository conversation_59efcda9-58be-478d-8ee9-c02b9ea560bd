import { UseCase } from '../../../../shared/core/use-case';
import { ID } from '../../../../shared/domain/Id';
import { Content } from '../../domain/content';
import { ContentRepository } from '../../repo/content-repo';
import { ArchiveContentDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class CreateContentUseCase
  implements UseCase<ArchiveContentDTO, Promise<Content>>
{
  constructor(private repository: ContentRepository) {}

  @LogUseCaseDTO
  public async execute(dto: ArchiveContentDTO) {
    const id = this.mapDTOtoId(dto);
    const content = await this.getContent(id);

    return await this.updateContent(content);
  }

  private mapDTOtoId(dto: ArchiveContentDTO) {
    const id = ID.create(dto.contentId);
    if (id.isFailure) throw new Error('Invalid ID');

    return id.getValue();
  }

  private async getContent(id: ID) {
    const content = await this.repository.getById(id);
    if (!content) {
      throw new Error('Content does not exists');
    }

    return content;
  }

  private async updateContent(content: Content) {
    content.contentStatus.updateValue({
      value: 'ARCHIVED',
    });
    await this.repository.update(content);

    return content;
  }
}
