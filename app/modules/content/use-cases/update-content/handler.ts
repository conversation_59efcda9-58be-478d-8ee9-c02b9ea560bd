import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { CreateContentUseCase } from './use-case';
import { ContentMap } from '../../mappers/content-map';
import { ContentRepository } from '../../repo/content-repo';
import { UpdateContentSchema } from './schema';
import { UpdateContentDTO } from './dto';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private createContentUseCase: CreateContentUseCase;

  private init() {
    const repo = new ContentRepository(
      new AWS.DynamoDB(),
      process.env.CONTENT_TABLE_NAME || ''
    );
    this.createContentUseCase = new CreateContentUseCase(repo);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result = await this.createContentUseCase.execute(dto);

      return this.ok(ContentMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<UpdateContentDTO> {
    if (!event.body) {
      throw new Error('Missing body');
    }
    const body = JSON.parse(event.body);
    const dto = await UpdateContentSchema.validate(body);
    const result: UpdateContentDTO = {
      id: dto.id,
      userId: dto.userId,
      userName: dto.userName,
      content: dto.content,
      contentStatus: dto.contentStatus,
      externalLinks: dto.externalLinks ?? [],
      imagesURL: dto.imagesURL ?? [],
    };

    return result;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
