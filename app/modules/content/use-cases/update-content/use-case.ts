import { UseCase } from '../../../../shared/core/use-case';
import { ISODate } from '../../../../shared/domain/ISODate';
import { ID } from '../../../../shared/domain/Id';
import { Text } from '../../../../shared/domain/Text';
import { Content } from '../../domain/content';
import { ContentStatus } from '../../domain/contentStatus';
import { ContentRepository } from '../../repo/content-repo';
import { UpdateContentDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class CreateContentUseCase
  implements UseCase<UpdateContentDTO, Promise<Content>>
{
  constructor(private repository: ContentRepository) {}

  @LogUseCaseDTO
  public async execute(dto: UpdateContentDTO) {
    const id = this.mapDTOtoId(dto);
    const content = await this.getContent(id);
    const updatedContent = await this.updateContent(content, dto);

    return updatedContent;
  }

  private mapDTOtoId(dto: UpdateContentDTO) {
    const id = ID.create(dto.id);
    if (id.isFailure) throw new Error('Invalid ID');

    return id.getValue();
  }

  private async getContent(id: ID) {
    const content = await this.repository.getById(id);
    if (!content) {
      throw new Error('Content does not exists');
    }

    return content;
  }

  private async updateContent(
    content: Content,
    dto: UpdateContentDTO
  ): Promise<Content> {
    content.updateContentData({
      content: Text.create(dto.content).getValue(),
      userId: Text.create(dto.userId).getValue(),
      userName: Text.create(dto.userName).getValue(),
      contentStatus: ContentStatus.create({
        value: dto.contentStatus,
      }).getValue(),
      externalLinks: dto.externalLinks.map((val) =>
        Text.create(val).getValue()
      ),
      imagesURL: dto.imagesURL.map((val) => Text.create(val).getValue()),
      updatedAt: ISODate.create(new Date().toISOString()).getValue(),
    });
    await this.repository.update(content);

    return content;
  }
}
