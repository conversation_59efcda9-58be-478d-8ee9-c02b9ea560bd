import { UseCase } from '../../../../shared/core/use-case';
import { Content } from '../../domain/content';
import { ContentMap } from '../../mappers/content-map';
import { ContentRepository } from '../../repo/content-repo';
import { v4 as uuidv4 } from 'uuid';
import * as AWS from 'aws-sdk';
import { CreateContentDTO } from './dto';
import { StoreService } from '../../../../shared/services/store-service';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class CreateContentUseCase
  implements UseCase<CreateContentDTO, Promise<Content>>
{
  constructor(
    private repository: ContentRepository,
    private bucketName: string
  ) {}

  @LogUseCaseDTO
  public async execute(dto: CreateContentDTO) {
    const contentImageURLs = await this.storeFiles(dto);
    const content = this.mapDTOtoDomain(dto, contentImageURLs);
    const result = await this.repository.create(content);

    return result;
  }

  private mapDTOtoDomain(dto: CreateContentDTO, contentImageURLs: string[]) {
    const content = ContentMap.toDomain({
      ...dto,
      id: uuidv4(),
      imagesURL: contentImageURLs,
    });

    return content;
  }

  private async storeFiles(data: CreateContentDTO): Promise<string[]> {
    const imagesURL: string[] = data.imagesURL;
    const newURLs: string[] = [];
    const storeService = new StoreService(new AWS.S3());
    for (let i = 0; i < imagesURL.length; i++) {
      const base64file = imagesURL[i];
      const mimeType = base64file.split(';')[0];
      const fileType = mimeType.split('/')[1].replace('/', '-');
      const base64Data = base64file.split(',')[1];
      const fileBuffer = Buffer.from(base64Data, 'base64');
      const currentDate = new Date().toISOString().replace(/[:.]/g, '-');
      const randomVariable = Math.random().toString(36).substring(2, 8);
      const key = `${currentDate}_${randomVariable}.${fileType}`;
      const storedUrl = await storeService.storeFile(
        fileBuffer,
        this.bucketName,
        key
      );
      newURLs.push(storedUrl);
    }

    return newURLs;
  }
}
