import { array, object, string } from 'yup';

export const CreateContentUserSchemea = object({
  userId: string().required('User Id for likes are required.'),
  userName: string().required('User Name for likes are required.'),
});
export const CreateContentSchema = object({
  userId: string().required('User Id for likes are required.'),
  userName: string().required('User Name for likes are required.'),
  content: string().required('Content is required'),
  imagesURL: array(string().required()).min(0).notRequired(),
  externalLinks: array(string().required()).min(0).notRequired(),
  contentStatus: string()
    .matches(/ACTIVE|ARCHIVED/)
    .required('Content Status must be ACTIVE or ARCHIVED'),
  likes: array(CreateContentUserSchemea.required()).min(0).notRequired(),
});
