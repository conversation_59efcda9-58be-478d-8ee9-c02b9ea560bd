import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { UpdateLikeCountUseCase } from './use-case';
import { ContentMap } from '../../mappers/content-map';
import { ContentRepository } from '../../repo/content-repo';
import { UpdateLikeCountDTO } from './dto';
import { HTTPRequestBodyValidator } from '../../../../shared/infra/http-request-body-validator';

class Lambda extends BaseHandler implements LambdaInterface {
  private updateLikeCountUseCase: UpdateLikeCountUseCase;

  private init() {
    const repo = new ContentRepository(
      new AWS.DynamoDB(),
      process.env.CONTENT_TABLE_NAME || ''
    );
    this.updateLikeCountUseCase = new UpdateLikeCountUseCase(repo);
  }

  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result = await this.updateLikeCountUseCase.execute(dto);

      return this.ok(ContentMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<UpdateLikeCountDTO> {
    const BODY_SCHEMA = ['id', 'likes'];

    return HTTPRequestBodyValidator<UpdateLikeCountDTO>(
      BODY_SCHEMA,
      event.body
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
