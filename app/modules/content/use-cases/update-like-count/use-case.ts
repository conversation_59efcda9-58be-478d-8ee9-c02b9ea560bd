import { UseCase } from '../../../../shared/core/use-case';
import { ISODate } from '../../../../shared/domain/ISODate';
import { ID } from '../../../../shared/domain/Id';
import { Text } from '../../../../shared/domain/Text';
import { Content } from '../../domain/content';
import { ContentUser } from '../../domain/contentUser';
import { ContentRepository } from '../../repo/content-repo';
import { UpdateLikeCountDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { Result } from '../../../../shared/core/Result';

export class UpdateLikeCountUseCase
  implements UseCase<UpdateLikeCountDTO, Promise<Content>>
{
  constructor(private repository: ContentRepository) {}

  @LogUseCaseDTO
  public async execute(dto: UpdateLikeCountDTO) {
    const id = this.mapDTOtoId(dto);
    const content = await this.getContent(id);
    const updatedContent = await this.updateContent(content, dto);

    return updatedContent;
  }

  private mapDTOtoId(dto: UpdateLikeCountDTO) {
    const id = ID.create(dto.id);
    if (id.isFailure) throw new Error('Invalid ID');

    return id.getValue();
  }

  private async getContent(id: ID) {
    const content = await this.repository.getById(id);
    if (!content) {
      throw new Error('Content does not exists');
    }

    return content;
  }

  private async updateContent(
    content: Content,
    dto: UpdateLikeCountDTO
  ): Promise<Content> {
    const updatedAt = ISODate.create();
    const userIds = dto.likes.map((val) => ID.create(val.userId));
    const userNames = dto.likes.map((val) => Text.create(val.userName));
    const dbCombines = Result.combine([...userIds, ...userNames, updatedAt]);
    if (dbCombines.isFailure) throw new Error(dbCombines.getErrorValue());
    const likes = dto.likes.map((val) =>
      ContentUser.create({
        userId: ID.create(val.userId).getValue(),
        userName: Text.create(val.userName).getValue(),
      }).getValue()
    );
    content.updateLikeCount({
      likes,
      updatedAt: updatedAt.getValue(),
    });
    await this.repository.update(content);

    return content;
  }
}
