import { Result } from '../../../shared/core/Result';
import { ID } from '../../../shared/domain/Id';
import { Text } from '../../../shared/domain/Text';
import { ISODate } from '../../../shared/domain/ISODate';
import { ContentDTO } from '../dto/content-dto';
import { Content } from '../domain/content';
import { ContentStatus } from '../domain/contentStatus';
import { ContentUser } from '../domain/contentUser';

export class ContentMap {
  public static toDTO(content: Content): ContentDTO {
    return {
      id: content.id?.value,
      userId: content.userId.value,
      userName: content.userName.value,
      content: content.content.value,
      imagesURL: content.imagesURL.map((imageURL) => imageURL.value),
      externalLinks: content.externalLinks.map(
        (externalLink) => externalLink.value
      ),
      contentStatus: content.contentStatus.value,
      likes: content.likes.map((like) => ({
        userId: like.getUserId().value,
        userName: like.getUserName().value,
      })),
      createdAt: content.createdAt?.value,
      updatedAt: content.updatedAt?.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): Content {
    const id = ID.create(raw.id);
    const userId = ID.create(raw.userId);
    const userName = Text.create(raw.userName);
    const content = Text.create(raw.content);
    const contentStatus = ContentStatus.create({
      value: raw.contentStatus,
    });
    const createdAt = ISODate.create(raw.createdAt);
    const updatedAt = ISODate.create(raw.updatedAt);
    const dtoCombine = Result.combine([
      id,
      userId,
      userName,
      content,
      contentStatus,
      createdAt,
      updatedAt,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const externalLinks: Array<Text> = [];
    const imagesURL: Array<Text> = [];
    const likes: Array<ContentUser> = [];
    const extractItems = (
      // eslint-disable-next-line
      source: any,
      // eslint-disable-next-line
      target: Array<any>,
      property: string
    ) => {
      if (source[property] && Array.isArray(source[property])) {
        for (let i = 0; i < source[property].length; i++) {
          target.push(Text.create(source[property][i]).getValue());
        }
      }
    };
    extractItems(raw, externalLinks, 'externalLinks');
    extractItems(raw, imagesURL, 'imagesURL');
    if (raw.likes && Array.isArray(raw.likes)) {
      for (let i = 0; i < raw.likes.length; i++) {
        const like = ContentUser.create({
          userId: ID.create(raw.likes[i].userId).getValue(),
          userName: Text.create(raw.likes[i].userName).getValue(),
        });
        if (like.isFailure) throw new Error('Invalid Like');
        likes.push(like.getValue());
      }
    }
    const contentDomain = Content.create({
      id: id?.getValue(),
      content: content.getValue(),
      contentStatus: contentStatus.getValue(),
      externalLinks: externalLinks,
      imagesURL: imagesURL,
      likes: likes,
      userId: userId.getValue(),
      userName: userName.getValue(),
      createdAt: createdAt?.getValue(),
      updatedAt: updatedAt.getValue(),
    });
    if (contentDomain.isFailure) throw contentDomain.getErrorValue();

    return contentDomain.getValue();
  }

  // eslint-disable-next-line
  public static toPersistence(content: Content): any {
    return {
      id: content.id?.value,
      userId: content.userId.value,
      userName: content.userName.value,
      content: content.content.value,
      imagesURL: content.imagesURL.map((imageURL) => imageURL.value),
      externalLinks: content.externalLinks.map(
        (externalLink) => externalLink.value
      ),
      contentStatus: content.contentStatus.value,
      likes: content.likes.map((like) => ({
        userId: like.getUserId().value,
        userName: like.getUserName().value,
      })),
      createdAt: content.createdAt?.value,
      updatedAt: content.updatedAt?.value,
    };
  }
}
