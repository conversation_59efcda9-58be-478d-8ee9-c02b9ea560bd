import * as AWS from 'aws-sdk';
import {
  PutItemInput,
  GetItemInput,
  DeleteItemInput,
  AttributeMap,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { ID } from '../../../shared/domain/Id';
import { Content } from '../domain/content';
import { ContentMap } from '../mappers/content-map';
import { DynamoServices } from '../../../shared/services/dynamo-services';

export class ContentRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async create(content: Content): Promise<Content> {
    const persistenceObject = ContentMap.toPersistence(content);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(persistenceObject),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', result);

    return content;
  }

  public async getById(contentId: ID): Promise<Content | null> {
    const params: GetItemInput = {
      TableName: this.tableName,
      Key: {
        id: { S: contentId.value },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', result);
    if (!result.Item) return null;

    return ContentMap.toDomain(this.unmarshallElement(result.Item));
  }

  public async getLatestContent(): Promise<Content[]> {
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'contentStatus-createdAt-index',
      KeyConditionExpression: 'contentStatus = :contentStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':contentStatus': { S: 'ACTIVE' },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ContentMap.toDomain(item));
  }

  public async update(content: Content): Promise<Content | null> {
    const id = content.id?.value || '';
    const data = ContentMap.toPersistence(content);
    delete data.id;
    const params = {
      TableName: this.tableName,
      Key: this.marshallElement({
        id,
      }),
      ReturnValues: 'ALL_NEW',
      UpdateExpression:
        'set ' +
        Object.keys(data)
          .map((k) => `#${k} = :${k}`)
          .join(', '),
      ExpressionAttributeNames: Object.entries(data).reduce(
        (acc, cur) => ({ ...acc, [`#${cur[0]}`]: cur[0] }),
        {}
      ),
      ExpressionAttributeValues: this.marshallElement(
        Object.entries(data).reduce(
          (acc, cur) => ({ ...acc, [`:${cur[0]}`]: cur[1] }),
          {}
        )
      ),
    };
    const result = await this.dynamodb.updateItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', result);
    if (!result.Attributes) throw 'Query return values error';

    return ContentMap.toDomain(this.unmarshallElement(result.Attributes));
  }

  public async delete(contentId: ID): Promise<void> {
    const params: DeleteItemInput = {
      TableName: this.tableName,
      Key: {
        id: { S: contentId.value },
      },
    };
    await this.dynamodb.deleteItem(params).promise();
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
