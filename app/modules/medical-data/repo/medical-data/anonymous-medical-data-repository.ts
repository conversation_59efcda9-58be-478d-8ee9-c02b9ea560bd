import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  ScanInput,
} from 'aws-sdk/clients/dynamodb';
import { AnonymousMedicalData } from '../../domain/medical-data/AnonymousMedicalData';
import { AnonymousMedicalDataMap } from '../../mappers/medical-data/anonymous-medical-data-map';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { DynamoServices } from '../../../../shared/services/dynamo-services';

export class AnonymousMedicalDataRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createAnonymousMedicalDataRecord(
    medicalData: AnonymousMedicalData
  ): Promise<AnonymousMedicalData> {
    const raw = AnonymousMedicalDataMap.toPersistence(medicalData);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(raw),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return medicalData;
  }

  public async getMedicalDataById(
    medicalDataId: UUIDValueObject
  ): Promise<AnonymousMedicalData | null> {
    const id = medicalDataId.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return AnonymousMedicalDataMap.toDomain(
      this.unmarshallElement(result.Item)
    );
  }

  public async getAllMedicalData(): Promise<AnonymousMedicalData[]> {
    const params: ScanInput = {
      TableName: this.tableName,
    };
    const result = await DynamoServices.scanAllElements(params);

    return result.map((item) => AnonymousMedicalDataMap.toDomain(item));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
