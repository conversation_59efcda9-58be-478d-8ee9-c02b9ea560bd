import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { AnonymousMedicalData } from '../../domain/medical-data/AnonymousMedicalData';
import { AnonymousMedicalDataMap } from '../../mappers/medical-data/anonymous-medical-data-map';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { UserMedicalData } from '../../domain/medical-data/UserMedicalData';
import { UserMedicalDataMap } from '../../mappers/medical-data/user-medical-data-map';
import { User } from '../../../user/domain/User';
import { DynamoServices } from '../../../../shared/services/dynamo-services';

export class UserMedicalDataRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createUserMedicalDataRecord(
    medicalData: UserMedicalData
  ): Promise<UserMedicalData> {
    const raw = UserMedicalDataMap.toPersistence(medicalData);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(raw),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return medicalData;
  }

  public async getMedicalDataById(
    medicalDataId: UUIDValueObject
  ): Promise<AnonymousMedicalData | null> {
    const id = medicalDataId.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return AnonymousMedicalDataMap.toDomain(
      this.unmarshallElement(result.Item)
    );
  }

  public async getUserMedicalDataByDocumentId(
    user: User
  ): Promise<UserMedicalData[]> {
    const id = user.identification.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'documentId-createdAt-index',
      KeyConditionExpression: 'documentId = :documentId',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':documentId': { S: id },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => UserMedicalDataMap.toDomain(item));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
