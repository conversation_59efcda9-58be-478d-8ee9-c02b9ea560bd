import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { UserLabExamResult } from '../../domain/user-lab-exam-result/UserLabExamResult';
import { UserLabExamResultMap } from '../../mappers/user-lab-exam-result/user-lab-exam-result-map';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { UserLabExamResultStatus } from '../../domain/user-lab-exam-result/user-lab-exam-result-status';
import { DynamoServices } from '../../../../shared/services/dynamo-services';

export class UserLabExamResultRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createUserLabExamResult(
    userLabExamResult: UserLabExamResult
  ): Promise<UserLabExamResult> {
    const rawUserLabExamResult =
      UserLabExamResultMap.toPersistence(userLabExamResult);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(rawUserLabExamResult),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return userLabExamResult;
  }

  public async getUserLabExamResultById(
    userLabExamResultId: UUIDValueObject
  ): Promise<UserLabExamResult | null> {
    const id = userLabExamResultId.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return UserLabExamResultMap.toDomain(this.unmarshallElement(result.Item));
  }

  public async getUserLabExamResultsByStatus(
    userId: UUIDValueObject,
    userLabExamResultStatus: UserLabExamResultStatus
  ): Promise<UserLabExamResult[]> {
    const id = userId.value;
    const status = userLabExamResultStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'userId-userLabExamnResultStatus-index',
      KeyConditionExpression:
        'userId = :userId AND userLabExamnResultStatus = :userLabExamnResultStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userId': { S: id },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userLabExamnResultStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => UserLabExamResultMap.toDomain(item));
  }

  public async updateUserLabExamResult(
    userLabExamnResultId: UserLabExamResult
  ): Promise<UserLabExamResult | null> {
    const id = userLabExamnResultId.id.value;
    const data = UserLabExamResultMap.toPersistence(userLabExamnResultId);
    delete data.id;
    const params = {
      TableName: this.tableName,
      Key: this.marshallElement({
        id,
      }),
      ReturnValues: 'ALL_NEW',
      UpdateExpression:
        'set ' +
        Object.keys(data)
          .map((k) => `#${k} = :${k}`)
          .join(', '),
      ExpressionAttributeNames: Object.entries(data).reduce(
        (acc, cur) => ({ ...acc, [`#${cur[0]}`]: cur[0] }),
        {}
      ),
      ExpressionAttributeValues: this.marshallElement(
        Object.entries(data).reduce(
          (acc, cur) => ({ ...acc, [`:${cur[0]}`]: cur[1] }),
          {}
        )
      ),
    };
    const result = await this.dynamodb.updateItem(params).promise();
    if (!result.Attributes) throw 'Query return values error';
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return UserLabExamResultMap.toDomain(
      this.unmarshallElement(result.Attributes)
    );
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
