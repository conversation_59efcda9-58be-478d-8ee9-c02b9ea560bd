import { User } from '../../user/domain/User';
import { UserMap } from '../../user/mappers/user-map';
import { MedicalRecord } from '../dto/medical-data/medical-record-dto';
import { UserHealthDTO } from '../dto/medical-data/user-health-dto';
import { CountryMapService } from '../../../shared/services/country-map-service';
import { DateServices } from '../../../shared/services/date-services';

export class MedicalRecordService {
  public static mapUserAndHealthDataMedicalReport({
    user,
    userHealthReport,
  }: {
    user: User;
    userHealthReport: UserHealthDTO;
  }): MedicalRecord {
    const userDTO = UserMap.toDTO(user);

    return {
      basicInfo: {
        name: userDTO.name,
        documentId: userDTO.identification,
        birthDate: DateServices.mapISODateToDateWithoutTime(userDTO.birthDate),
        country: CountryMapService.toHuman(userDTO.country),
        age: userDTO.age,
        height: userDTO.height,
        weight: userDTO.weight,
        BMI: parseFloat(userDTO.bmi.toFixed(2)),
      },
      contactInfo: {
        phone: userDTO.phone,
        email: userDTO.email,
      },
      labReport: userHealthReport,
    };
  }
}
