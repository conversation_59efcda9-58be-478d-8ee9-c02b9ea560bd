import { UserLabExamResult } from '../domain/user-lab-exam-result/UserLabExamResult';
import { UserMedicalData } from '../domain/medical-data/UserMedicalData';
import { UserMedicalDataMap } from '../mappers/medical-data/user-medical-data-map';
import { MedicalDataService } from './medical-data-services';
import {
  UserHealthData,
  UserHealthDataValues,
  UserHealthDTO,
} from '../dto/medical-data/user-health-dto';
import { DateServices } from '../../../shared/services/date-services';

export class LabExamService {
  public static MergeUserLabExamWithUserHealthData({
    userIdentification,
    userLabExamResult,
    userMedicalData,
    includeDiagnosisResults,
  }: {
    userIdentification: string;
    userLabExamResult: UserLabExamResult[];
    userMedicalData: UserMedicalData[];
    includeDiagnosisResults?: boolean;
  }): UserHealthDTO {
    const medicalDataDTOList = userMedicalData.map((data) =>
      UserMedicalDataMap.toDto(data)
    );
    const userHealth = UserMedicalDataMap.toUserHealthDTO(
      medicalDataDTOList,
      userIdentification
    );
    const labExamCatalog = Object.entries(
      MedicalDataService.GetLabExamCatalog(includeDiagnosisResults)
    );
    const data: UserHealthData[] = labExamCatalog
      .map((key) => {
        const customResult: UserHealthDataValues[] = userLabExamResult
          .filter((result) => result.labExamnId.value === key[0])
          .map((e) => {
            return {
              date: DateServices.mapISODateToCustomFormat(e.createdAt.value),
              year: new Date(e.createdAt.value).getFullYear().toString(),
              isoDate: e.createdAt.value,
              examnResult: e.labExamnValue.value.toString() || '-',
            };
          });
        const medicalSupplierResult = userHealth.data.find(
          (e) => e.id === key[0]
        );
        if (
          customResult.length > 0 ||
          (medicalSupplierResult && medicalSupplierResult.values.length > 0)
        ) {
          const combineValues = [
            ...(medicalSupplierResult?.values || []),
            ...customResult,
          ]
            .filter((e) => e.examnResult !== '-')
            .sort(
              (a: UserHealthDataValues, b: UserHealthDataValues) =>
                new Date(a.isoDate).getTime() - new Date(b.isoDate).getTime()
            );

          return {
            id: key[0],
            title: key[1],
            lastValue: combineValues[0].examnResult,
            values: combineValues,
          };
        }

        return;
      })
      .filter(
        (e) => e !== undefined && e.lastValue !== '-'
      ) as UserHealthData[];

    return { ...userHealth, data };
  }
}
