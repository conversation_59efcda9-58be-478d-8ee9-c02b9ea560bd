/* eslint-disable @typescript-eslint/naming-convention */
import { AnonymousMedicalData } from '../domain/medical-data/AnonymousMedicalData';
import { UserMedicalData } from '..//domain/medical-data/UserMedicalData';
import { ExcelDate } from '..//domain/medical-data/excel-date';
import { AnonymousMedicalDataMap } from '..//mappers/medical-data/anonymous-medical-data-map';
import { UserMedicalDataMap } from '..//mappers/medical-data/user-medical-data-map';
import { HealthProviderMedicalFile, LabExamDictionary } from './type';
import { Result } from '../../../shared/core/Result';

const LAB_EXAM_METADATA = {
  date: 'fecha',
  patientName: 'Nombre ',
  patientLastName: 'Apellidos',
  documentId: 'Cedula (sin símbolos)',
  patientGender: 'genero',
  dateOfBirth: '<PERSON><PERSON> (MM/AAAA)',
};
const LAB_EXAM_DICTIONARY: LabExamDictionary = {
  whiteBloodCells: 'G Blancos (4000-10000/uL)',
  redBloodCells: 'G. Rojos (4000-5500 u/L)',
  hemoglobin: 'Hemoglobina (12-15 g/dL)',
  platelets: 'Plaquetas (150-400 u/L)',
  glycemia:
    'Glicemia (<100 mg/dL normal, 100-126 mg/dL intolernacia a CHO, >126 mg/dL DM)',
  colTotal: 'Col total (<200 mg/dL)',
  HDL: 'HDL (40-100 mg/dL)',
  LDL: 'LDL (<130 mg/dL)',
  TG: 'TG (<150 mg/dL)',
  relationshipCholesterolHDL: 'Rel colesterol/HDL (0-4.6)',
  CR: 'Cr (0.60-1.10 mg/dL)',
  BUN: 'BUN (6-24mg/dL)',
  uricAcid: 'A.úrico (3.40-7 mg/dL)',
  GGT: 'GGT (5-38 U/L)',
  AST: 'AST (10.35 U/L)',
  ALT: 'ALT /10-45 U/L)',
  FA: 'FA (53-198  U/L)',
  totalBilirubin: 'Bilirrunina total (0.20-1.20 mg/dL)',
  directBilirubin: 'Bilirubina directa (0-0-30 mg/dL)',
  indirectBilirubin: 'Bilirrubina indirecta (0-0.70 mg/dL)',
  totalProteins: 'Proteínas totales (6.4-8.3 g/dL)',
  TSH: 'TSH (0.400-4000 uUI/mL)',
  freeT3: 'T3 libre (1.71-3.71 uUI/mL)',
  totalT4: 'T4 total (4.87-11.72 ug/dL)',
  freeT4: 'T4 libre (0.70-1.70 ng/dL)',
  EGO: 'EGO',
  totalPSA: 'PSA total (0-3.000 ng/mL)',
  HbA1C: 'HbA1C (<5.7% normal)',
  electrocardiogram: 'Electrocardiograma',
  conventionalPapSmear: 'Papanicolau Convencional',
  abdomenUltrasound: 'Ultrasonido de Abdomen',
  breastUltrasound: 'Ultrasonido de Mamas',
  diagnosis: 'diagnosticos',
  referredSpecialty: 'especialidadReferida',
  folicAcid: 'Ácido fólico',
  bnp: 'BNP',
  calcium: 'Calcio',
  totalCholesterol: 'Colesterol Total',
  alkalinePhosphatase: 'Fosfatasa Alcalina',
  follicleStimulatingHormone: 'Hormona Foliculo Estimulante (FSH)',
  luteinizingHormone: 'Hormona Luteinizante (LH)',
  fastingInsulin: 'Insulina en ayunas',
  magnesium: 'Magnesio',
  proBnpNt: 'ProBNP-NT',
  progesterone: 'Progesterona',
  cReactiveProtein: 'Proteína C Reactiva',
  sodium: 'Sodio',
  transferrin: 'Transferrina',
  activeVitaminB12: 'Vitamina B12 activa',
  vitaminD: 'Vitamina D (D3)',
  potassium: 'Potasio',
  totalTestosterone: 'Testosterona Total',
};
export class MedicalDataService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static filterJsonDataByFormatSchema(jsonData: any[]) {
    const validHealthProviderMedicalFile: HealthProviderMedicalFile[] = [];
    for (const data of jsonData) {
      let isValid = true;
      for (const values of Object.values(LAB_EXAM_METADATA)) {
        if (
          !Object.keys(data).find(
            (key) =>
              key.trim().toLocaleLowerCase() ===
              values.trim().toLocaleLowerCase()
          )
        ) {
          // eslint-disable-next-line no-console
          console.log(
            `Object does not have metadata property '${values}'`,
            'object: ',
            data
          );
          isValid = false;
          break;
        }
      }
      if (isValid) {
        for (const values of Object.values(LAB_EXAM_DICTIONARY)) {
          if (
            !Object.keys(data).find(
              (key) =>
                key.trim().toLocaleLowerCase() ===
                values.trim().toLocaleLowerCase()
            )
          ) {
            data[values] = '-';
          }
        }
        validHealthProviderMedicalFile.push(data);
      }
    }

    return validHealthProviderMedicalFile;
  }

  public static mapJSONToAnonymousMedicalDataDomain(
    country: string,
    healthProviderMedicalFile: HealthProviderMedicalFile[]
  ): AnonymousMedicalData[] {
    const anonymousMedicalDataList: AnonymousMedicalData[] = [];
    for (const row of healthProviderMedicalFile) {
      try {
        const date = this.getDateFromCellValue('fecha', row['fecha']);
        const year = this.getYearFromDate(date);
        const dateOfBirth = this.getDateFromCellValue(
          'Fecha Nacimiento (MM/AAAA)',
          row['Fecha Nacimiento (MM/AAAA)']
        );
        const dto = {
          date: row['fecha'],
          patientGender: row['genero'],
          patientAge:
            dateOfBirth.getValue().value !== '-'
              ? this.getAgeBasedOnDateOfBirth(
                  dateOfBirth.getValue().value,
                  year
                )
              : 0,
          country,
          year,
          createdAt: date.getValue().value,
        };
        const cleanDTO = this.removeSpecialCharacters(
          this.addLabExamDataToMedicalData(dto, row)
        );
        const domain = AnonymousMedicalDataMap.toDomain(cleanDTO);
        anonymousMedicalDataList.push(domain);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(
          'Error mapping healthProviderMedicalFile to anonymous domain',
          error,
          'row:',
          row
        );
      }
    }

    return anonymousMedicalDataList;
  }

  public static mapJSONToUserMedicalDataDomain(
    country: string,
    healthProviderMedicalFile: HealthProviderMedicalFile[]
  ): UserMedicalData[] {
    const userMedicalDataList: UserMedicalData[] = [];
    for (const row of healthProviderMedicalFile) {
      try {
        const date = this.getDateFromCellValue('fecha', row['fecha']);
        const year = this.getYearFromDate(date);
        const dateOfBirth = this.getDateFromCellValue(
          'Fecha Nacimiento (MM/AAAA)',
          row['Fecha Nacimiento (MM/AAAA)']
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const dto: any = {
          date: row['fecha'],
          patientName: row['Nombre '] + ' ' + row['Apellidos'],
          documentId: row['Cedula (sin símbolos)'],
          patientGender: row['genero'],
          patientAge:
            dateOfBirth.getValue().value !== '-'
              ? this.getAgeBasedOnDateOfBirth(
                  dateOfBirth.getValue().value,
                  year
                )
              : 0,
          dateOfBirth: row['Fecha Nacimiento (MM/AAAA)'],
          country,
          year,
          createdAt: date.getValue().value,
        };
        const cleanDTO = this.removeSpecialCharacters(
          this.addLabExamDataToMedicalData(dto, row)
        );
        const domain = UserMedicalDataMap.toDomain(cleanDTO);
        userMedicalDataList.push(domain);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(
          'Error mapping healthProviderMedicalFile to user medical data domain',
          error,
          'row:',
          row
        );
      }
    }

    return userMedicalDataList;
  }

  private static getDateFromCellValue(
    argumentName: string,
    date: string | number
  ): Result<ExcelDate> {
    const value = typeof date === 'string' ? parseInt(date) : date;

    return ExcelDate.create(argumentName, value);
  }

  private static getYearFromDate(date: Result<ExcelDate>): number {
    return date.isSuccess
      ? new Date(date.getValue().value).getFullYear()
      : new Date().getFullYear();
  }

  private static getAgeBasedOnDateOfBirth(dateOfBirth: string, year: number) {
    try {
      return year - new Date(dateOfBirth).getFullYear();
    } catch (error) {
      return 0;
    }
  }

  private static addLabExamDataToMedicalData(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    dto: any,
    row: HealthProviderMedicalFile
  ) {
    return Object.entries(LAB_EXAM_DICTIONARY).reduce((acc, [key, value]) => {
      return {
        ...acc,
        [key]: row[value as keyof HealthProviderMedicalFile],
      };
    }, dto);
  }

  public static GetLabExamCatalog(includeDiagnosisResults?: boolean) {
    const LabExamCatalog: Partial<LabExamDictionary> = {
      redBloodCells: 'Glóbulos Rojos',
      whiteBloodCells: 'Glóbulos Blancos',
      hemoglobin: 'Hemoglobina',
      platelets: 'Plaquetas',
      glycemia: 'Glicemia',
      uricAcid: 'Ácido Úrico',
      HDL: 'HDL',
      LDL: 'LDL',
      TG: 'TG',
      relationshipCholesterolHDL: 'Relación Colesterol/HDL',
      CR: 'CR',
      BUN: 'BUN',
      GGT: 'GGT',
      AST: 'AST',
      ALT: 'ALT',
      FA: 'FA',
      totalBilirubin: 'Bilirrubina Total',
      directBilirubin: 'Bilirrubina Directa',
      indirectBilirubin: 'Bilirrubina Indirecta',
      totalProteins: 'Proteínas Totales',
      TSH: 'TSH',
      freeT3: 'T3 Libre',
      totalT4: 'T4 Total',
      freeT4: 'T4 libre',
      totalPSA: 'PSA Total',
      HbA1C: 'HbA1C',
      folicAcid: 'Ácido fólico',
      bnp: 'BNP',
      calcium: 'Calcio',
      totalCholesterol: 'Colesterol Total',
      alkalinePhosphatase: 'Fosfatasa Alcalina',
      follicleStimulatingHormone: 'Hormona Foliculo Estimulante (FSH)',
      luteinizingHormone: 'Hormona Luteinizante (LH)',
      fastingInsulin: 'Insulina en ayunas',
      magnesium: 'Magnesio',
      proBnpNt: 'ProBNP-NT',
      progesterone: 'Progesterona',
      cReactiveProtein: 'Proteína C Reactiva',
      sodium: 'Sodio',
      transferrin: 'Transferrina',
      activeVitaminB12: 'Vitamina B12 activa',
      vitaminD: 'Vitamina D (D3)',
      potassium: 'Potasio',
      totalTestosterone: 'Testosterona Total',
    };
    const CompleteCatalog: Partial<LabExamDictionary> = {
      ...LabExamCatalog,
      abdomenUltrasound: 'Ultrasonido de Abdomen',
      breastUltrasound: 'Ultrasonido de Mamas',
      conventionalPapSmear: 'Papanicolau Convencional',
      diagnosis: 'Diagnósticos',
      electrocardiogram: 'Electrocardiograma',
      referredSpecialty: 'Especialidad Referida',
    };

    return includeDiagnosisResults === true ? CompleteCatalog : LabExamCatalog;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static removeSpecialCharacters(obj: any) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const formattedObject: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const formattedValue =
        typeof value === 'string'
          ? this.mapFromAccentCodeToAccentLetter(value)
          : value;
      formattedObject[key] = formattedValue;
    }

    return formattedObject;
  }

  public static mapFromAccentCodeToAccentLetter = (value: string): string => {
    const accentsMap: { [key: string]: string } = {
      'Ã¡': 'á',
      'Ã©': 'é',
      'Ã­': 'í',
      'Ã³': 'ó',
      Ãº: 'ú',
      'Ã¼': 'ü',
      'Ã±': 'ñ',
      'Ã': 'Á',
      'Ã‰': 'É',
      'Ã': 'Í',
      'Ã“': 'Ó',
      Ãš: 'Ú',
      Ãœ: 'Ü',
      'Ã‘': 'Ñ',
    };

    return value.replace(/Ã[¡©­³º¼½¾]/g, (match) => accentsMap[match]);
  };
}
