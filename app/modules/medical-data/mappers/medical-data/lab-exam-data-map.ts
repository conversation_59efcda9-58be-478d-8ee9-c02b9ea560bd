import { MedicalOutput } from '../../domain/medical-data/medical-output';
import { Result } from '../../../../shared/core/Result';
import { LabExamDataDTO } from '../../dto/medical-data/lab-exam-data-dto';
import { LabExamData } from '../../domain/medical-data/LabExamData';

export class LabExamDataMap {
  public static toDto(medicalData: LabExamData): LabExamDataDTO {
    return {
      redBloodCells: medicalData.redBloodCells.value,
      whiteBloodCells: medicalData.whiteBloodCells.value,
      hemoglobin: medicalData.hemoglobin.value,
      platelets: medicalData.platelets.value,
      glycemia: medicalData.glycemia.value,
      colTotal: medicalData.colTotal.value,
      HDL: medicalData.HDL.value,
      LDL: medicalData.LDL.value,
      TG: medicalData.TG.value,
      relationshipCholesterolHDL: medicalData.relationshipCholesterolHDL.value,
      CR: medicalData.CR.value,
      BUN: medicalData.BUN.value,
      uricAcid: medicalData.uricAcid.value,
      GGT: medicalData.GGT.value,
      AST: medicalData.AST.value,
      ALT: medicalData.ALT.value,
      FA: medicalData.FA.value,
      totalBilirubin: medicalData.totalBilirubin.value,
      directBilirubin: medicalData.directBilirubin.value,
      indirectBilirubin: medicalData.indirectBilirubin.value,
      totalProteins: medicalData.totalProteins.value,
      TSH: medicalData.TSH.value,
      freeT3: medicalData.freeT3.value,
      totalT4: medicalData.totalT4.value,
      freeT4: medicalData.freeT4.value,
      EGO: medicalData.EGO.value,
      totalPSA: medicalData.totalPSA.value,
      HbA1C: medicalData.HbA1C.value,
      electrocardiogram: medicalData.electrocardiogram.value,
      conventionalPapSmear: medicalData.conventionalPapSmear.value,
      abdomenUltrasound: medicalData.abdomenUltrasound.value,
      breastUltrasound: medicalData.breastUltrasound.value,
      diagnosis: medicalData.diagnosis.value,
      referredSpecialty: medicalData.referredSpecialty.value,
      folicAcid: medicalData.folicAcid.value,
      bnp: medicalData.bnp.value,
      calcium: medicalData.calcium.value,
      totalCholesterol: medicalData.totalCholesterol.value,
      alkalinePhosphatase: medicalData.alkalinePhosphatase.value,
      follicleStimulatingHormone: medicalData.follicleStimulatingHormone.value,
      luteinizingHormone: medicalData.luteinizingHormone.value,
      fastingInsulin: medicalData.fastingInsulin.value,
      magnesium: medicalData.magnesium.value,
      proBnpNt: medicalData.proBnpNt.value,
      progesterone: medicalData.progesterone.value,
      cReactiveProtein: medicalData.cReactiveProtein.value,
      sodium: medicalData.sodium.value,
      transferrin: medicalData.transferrin.value,
      activeVitaminB12: medicalData.activeVitaminB12.value,
      vitaminD: medicalData.vitaminD.value,
      potassium: medicalData.potassium.value,
      totalTestosterone: medicalData.totalTestosterone.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): LabExamData {
    const medicalDataValues = LabExamDataMap.toValueObject(raw);
    const labExamData = LabExamData.create({ ...medicalDataValues });
    if (labExamData.isFailure) {
      throw labExamData.getErrorValue();
    }

    return labExamData.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toValueObject(raw: any) {
    const redBloodCells = MedicalOutput.create(raw.redBloodCells);
    const whiteBloodCells = MedicalOutput.create(raw.whiteBloodCells);
    const hemoglobin = MedicalOutput.create(raw.hemoglobin);
    const platelets = MedicalOutput.create(raw.platelets);
    const glycemia = MedicalOutput.create(raw.glycemia);
    const colTotal = MedicalOutput.create(raw.colTotal);
    const HDL = MedicalOutput.create(raw.HDL);
    const LDL = MedicalOutput.create(raw.LDL);
    const TG = MedicalOutput.create(raw.TG);
    const relationshipCholesterolHDL = MedicalOutput.create(
      raw.relationshipCholesterolHDL
    );
    const CR = MedicalOutput.create(raw.CR);
    const BUN = MedicalOutput.create(raw.BUN);
    const uricAcid = MedicalOutput.create(raw.uricAcid);
    const GGT = MedicalOutput.create(raw.GGT);
    const AST = MedicalOutput.create(raw.AST);
    const ALT = MedicalOutput.create(raw.ALT);
    const FA = MedicalOutput.create(raw.FA);
    const totalBilirubin = MedicalOutput.create(raw.totalBilirubin);
    const directBilirubin = MedicalOutput.create(raw.directBilirubin);
    const indirectBilirubin = MedicalOutput.create(raw.indirectBilirubin);
    const totalProteins = MedicalOutput.create(raw.totalProteins);
    const TSH = MedicalOutput.create(raw.TSH);
    const freeT3 = MedicalOutput.create(raw.freeT3);
    const totalT4 = MedicalOutput.create(raw.totalT4);
    const freeT4 = MedicalOutput.create(raw.freeT4);
    const EGO = MedicalOutput.create(raw.EGO);
    const totalPSA = MedicalOutput.create(raw.totalPSA);
    const HbA1C = MedicalOutput.create(raw.HbA1C);
    const electrocardiogram = MedicalOutput.create(raw.electrocardiogram);
    const conventionalPapSmear = MedicalOutput.create(raw.conventionalPapSmear);
    const abdomenUltrasound = MedicalOutput.create(raw.abdomenUltrasound);
    const breastUltrasound = MedicalOutput.create(raw.breastUltrasound);
    const diagnosis = MedicalOutput.create(raw.diagnosis);
    const referredSpecialty = MedicalOutput.create(raw.referredSpecialty);
    const folicAcid = MedicalOutput.create(raw.folicAcid);
    const bnp = MedicalOutput.create(raw.bnp);
    const calcium = MedicalOutput.create(raw.calcium);
    const totalCholesterol = MedicalOutput.create(raw.totalCholesterol);
    const alkalinePhosphatase = MedicalOutput.create(raw.alkalinePhosphatase);
    const follicleStimulatingHormone = MedicalOutput.create(
      raw.follicleStimulatingHormone
    );
    const luteinizingHormone = MedicalOutput.create(raw.luteinizingHormone);
    const fastingInsulin = MedicalOutput.create(raw.fastingInsulin);
    const magnesium = MedicalOutput.create(raw.magnesium);
    const proBnpNt = MedicalOutput.create(raw.proBnpNt);
    const progesterone = MedicalOutput.create(raw.progesterone);
    const cReactiveProtein = MedicalOutput.create(raw.cReactiveProtein);
    const sodium = MedicalOutput.create(raw.sodium);
    const transferrin = MedicalOutput.create(raw.transferrin);
    const activeVitaminB12 = MedicalOutput.create(raw.activeVitaminB12);
    const vitaminD = MedicalOutput.create(raw.vitaminD);
    const potassium = MedicalOutput.create(raw.potassium);
    const totalTestosterone = MedicalOutput.create(raw.totalTestosterone);
    const dtoCombine = Result.combine([
      redBloodCells,
      whiteBloodCells,
      hemoglobin,
      platelets,
      glycemia,
      colTotal,
      HDL,
      LDL,
      TG,
      relationshipCholesterolHDL,
      CR,
      BUN,
      uricAcid,
      GGT,
      AST,
      ALT,
      FA,
      totalBilirubin,
      directBilirubin,
      indirectBilirubin,
      totalProteins,
      TSH,
      freeT3,
      totalT4,
      freeT4,
      EGO,
      totalPSA,
      HbA1C,
      electrocardiogram,
      conventionalPapSmear,
      abdomenUltrasound,
      breastUltrasound,
      diagnosis,
      referredSpecialty,
      folicAcid,
      bnp,
      calcium,
      totalCholesterol,
      alkalinePhosphatase,
      follicleStimulatingHormone,
      luteinizingHormone,
      fastingInsulin,
      magnesium,
      proBnpNt,
      progesterone,
      cReactiveProtein,
      sodium,
      transferrin,
      activeVitaminB12,
      vitaminD,
      potassium,
      totalTestosterone,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }

    return {
      redBloodCells: redBloodCells.getValue(),
      whiteBloodCells: whiteBloodCells.getValue(),
      hemoglobin: hemoglobin.getValue(),
      platelets: platelets.getValue(),
      glycemia: glycemia.getValue(),
      colTotal: colTotal.getValue(),
      HDL: HDL.getValue(),
      LDL: LDL.getValue(),
      TG: TG.getValue(),
      relationshipCholesterolHDL: relationshipCholesterolHDL.getValue(),
      CR: CR.getValue(),
      BUN: BUN.getValue(),
      uricAcid: uricAcid.getValue(),
      GGT: GGT.getValue(),
      AST: AST.getValue(),
      ALT: ALT.getValue(),
      FA: FA.getValue(),
      totalBilirubin: totalBilirubin.getValue(),
      directBilirubin: directBilirubin.getValue(),
      indirectBilirubin: indirectBilirubin.getValue(),
      totalProteins: totalProteins.getValue(),
      TSH: TSH.getValue(),
      freeT3: freeT3.getValue(),
      totalT4: totalT4.getValue(),
      freeT4: freeT4.getValue(),
      EGO: EGO.getValue(),
      totalPSA: totalPSA.getValue(),
      HbA1C: HbA1C.getValue(),
      electrocardiogram: electrocardiogram.getValue(),
      conventionalPapSmear: conventionalPapSmear.getValue(),
      abdomenUltrasound: abdomenUltrasound.getValue(),
      breastUltrasound: breastUltrasound.getValue(),
      diagnosis: diagnosis.getValue(),
      referredSpecialty: referredSpecialty.getValue(),
      folicAcid: folicAcid.getValue(),
      bnp: bnp.getValue(),
      calcium: calcium.getValue(),
      totalCholesterol: totalCholesterol.getValue(),
      alkalinePhosphatase: alkalinePhosphatase.getValue(),
      follicleStimulatingHormone: follicleStimulatingHormone.getValue(),
      luteinizingHormone: luteinizingHormone.getValue(),
      fastingInsulin: fastingInsulin.getValue(),
      magnesium: magnesium.getValue(),
      proBnpNt: proBnpNt.getValue(),
      progesterone: progesterone.getValue(),
      cReactiveProtein: cReactiveProtein.getValue(),
      sodium: sodium.getValue(),
      transferrin: transferrin.getValue(),
      activeVitaminB12: activeVitaminB12.getValue(),
      vitaminD: vitaminD.getValue(),
      potassium: potassium.getValue(),
      totalTestosterone: totalTestosterone.getValue(),
    };
  }

  public static toPersistence(medicalData: LabExamData) {
    return {
      redBloodCells: medicalData.redBloodCells.value,
      whiteBloodCells: medicalData.whiteBloodCells.value,
      hemoglobin: medicalData.hemoglobin.value,
      platelets: medicalData.platelets.value,
      glycemia: medicalData.glycemia.value,
      colTotal: medicalData.colTotal.value,
      HDL: medicalData.HDL.value,
      LDL: medicalData.LDL.value,
      TG: medicalData.TG.value,
      relationshipCholesterolHDL: medicalData.relationshipCholesterolHDL.value,
      CR: medicalData.CR.value,
      BUN: medicalData.BUN.value,
      uricAcid: medicalData.uricAcid.value,
      GGT: medicalData.GGT.value,
      AST: medicalData.AST.value,
      ALT: medicalData.ALT.value,
      FA: medicalData.FA.value,
      totalBilirubin: medicalData.totalBilirubin.value,
      directBilirubin: medicalData.directBilirubin.value,
      indirectBilirubin: medicalData.indirectBilirubin.value,
      totalProteins: medicalData.totalProteins.value,
      TSH: medicalData.TSH.value,
      freeT3: medicalData.freeT3.value,
      totalT4: medicalData.totalT4.value,
      freeT4: medicalData.freeT4.value,
      EGO: medicalData.EGO.value,
      totalPSA: medicalData.totalPSA.value,
      HbA1C: medicalData.HbA1C.value,
      electrocardiogram: medicalData.electrocardiogram.value,
      conventionalPapSmear: medicalData.conventionalPapSmear.value,
      abdomenUltrasound: medicalData.abdomenUltrasound.value,
      breastUltrasound: medicalData.breastUltrasound.value,
      diagnosis: medicalData.diagnosis.value,
      referredSpecialty: medicalData.referredSpecialty.value,
      folicAcid: medicalData.folicAcid.value,
      bnp: medicalData.bnp.value,
      calcium: medicalData.calcium.value,
      totalCholesterol: medicalData.totalCholesterol.value,
      alkalinePhosphatase: medicalData.alkalinePhosphatase.value,
      follicleStimulatingHormone: medicalData.follicleStimulatingHormone.value,
      luteinizingHormone: medicalData.luteinizingHormone.value,
      fastingInsulin: medicalData.fastingInsulin.value,
      magnesium: medicalData.magnesium.value,
      proBnpNt: medicalData.proBnpNt.value,
      progesterone: medicalData.progesterone.value,
      cReactiveProtein: medicalData.cReactiveProtein.value,
      sodium: medicalData.sodium.value,
      transferrin: medicalData.transferrin.value,
      activeVitaminB12: medicalData.activeVitaminB12.value,
      vitaminD: medicalData.vitaminD.value,
      potassium: medicalData.potassium.value,
      totalTestosterone: medicalData.totalTestosterone.value,
    };
  }
}
