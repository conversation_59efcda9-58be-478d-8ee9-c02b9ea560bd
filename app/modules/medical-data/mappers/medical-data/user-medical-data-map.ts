import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { Country } from '../../../../shared/common-value-objects/country';
import { Year } from '../../domain/medical-data/year';
import { UserMedicalData } from '../../domain/medical-data/UserMedicalData';
import { ExcelDate } from '../../domain/medical-data/excel-date';
import {
  UserHealthDTO,
  UserHealthData,
  UserHealthDataValues,
} from '../../dto/medical-data/user-health-dto';
import { UserMedicalDataDTO } from '../../dto/medical-data/user-medical-data-dto';
import { DateServices } from '../../../../shared/services/date-services';
import { MedicalDataService } from '../../services/medical-data-services';
import { LabExamDataMap } from './lab-exam-data-map';
import { SimpleTextValueObject } from '../../../../shared/common-value-objects/simple-text-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { LabExamDictionary } from '../../services/type';
import { ExcelGender } from '../../domain/medical-data/excel-gender';

export class UserMedicalDataMap {
  public static toDto(medicalData: UserMedicalData): UserMedicalDataDTO {
    return {
      id: medicalData.id.value,
      date: medicalData.date.value,
      patientName: medicalData.patientName.value,
      documentId: medicalData.documentId.value,
      patientGender: medicalData.patientGender.value,
      patientAge: medicalData.patientAge.value,
      dateOfBirth: medicalData.dateOfBirth.value,
      country: medicalData.country.value,
      year: medicalData.year.value,
      createdAt: medicalData.createdAt.value,
      updatedAt: medicalData.updatedAt.value,
      ...LabExamDataMap.toDto(medicalData),
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): UserMedicalData {
    const id = UUIDValueObject.create(raw.id);
    const date = ExcelDate.create('date', raw.date);
    const patientName = SimpleTextValueObject.create(
      'patientName',
      raw.patientName
    );
    const documentId = SimpleTextValueObject.create(
      'documentId',
      raw.documentId
    );
    const patientGender = ExcelGender.create(raw.patientGender);
    const patientAge = NumberValueObject.create('patientAge', raw.patientAge);
    const dateOfBirth = ExcelDate.create('dateOfBirth', raw.dateOfBirth);
    const country = Country.create(raw.country);
    const year = Year.create(raw.year);
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const updatedAt = DateValueObject.create('updatedAt', raw.createdAt);
    const dtoCombine = Result.combine([
      id,
      date,
      patientName,
      documentId,
      patientGender,
      patientAge,
      dateOfBirth,
      country,
      year,
      createdAt,
      updatedAt,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const medicalData = UserMedicalData.create({
      id: id.getValue(),
      date: date.getValue(),
      patientName: patientName.getValue(),
      documentId: documentId.getValue(),
      patientGender: patientGender.getValue(),
      patientAge: patientAge.getValue(),
      dateOfBirth: dateOfBirth.getValue(),
      country: country.getValue(),
      year: year.getValue(),
      createdAt: createdAt.getValue(),
      updatedAt: updatedAt.getValue(),
      ...LabExamDataMap.toValueObject(raw),
    });
    if (medicalData.isFailure) {
      throw medicalData.getErrorValue();
    }

    return medicalData.getValue();
  }

  public static toPersistence(medicalData: UserMedicalData) {
    return {
      id: medicalData.id.value,
      date: medicalData.date.value,
      patientName: medicalData.patientName.value,
      documentId: medicalData.documentId.value,
      patientGender: medicalData.patientGender.value,
      patientAge: medicalData.patientAge.value,
      dateOfBirth: medicalData.dateOfBirth.value,
      country: medicalData.country.value,
      year: medicalData.year.value,
      createdAt: medicalData.createdAt.value,
      updatedAt: medicalData.updatedAt.value,
      ...LabExamDataMap.toPersistence(medicalData),
    };
  }

  public static toUserHealthDTO(
    medicalData: UserMedicalDataDTO[],
    identification: string
  ): UserHealthDTO {
    const mapExamIdToTitle = (name: keyof LabExamDictionary) => {
      const EXAM_NAME_LIBRARY = MedicalDataService.GetLabExamCatalog();

      return EXAM_NAME_LIBRARY[name] || name;
    };
    const excludedProperties: string[] = [
      'id',
      'date',
      'patientName',
      'patientLastName',
      'documentId',
      'patientGender',
      'dateOfBirth',
      'createdAt',
      'updatedAt',
    ];
    const groupedData: { [key: string]: UserHealthDataValues[] } = {};
    medicalData.forEach((item) => {
      Object.entries(item).forEach(([key, value]) => {
        if (!excludedProperties.includes(key) && value && value !== '-') {
          if (!groupedData[key]) {
            groupedData[key] = [];
          }
          groupedData[key].push({
            isoDate: item.createdAt,
            year: item.year,
            date: DateServices.mapISODateToCustomFormat(item.createdAt),
            examnResult: value,
          });
        }
      });
    });
    const result: UserHealthData[] = Object.entries(groupedData).map(
      ([key, values]) => ({
        id: key,
        lastValue: '',
        title: mapExamIdToTitle(key as keyof LabExamDictionary),
        values,
      })
    );

    return {
      identification,
      data: result.map((e) => {
        const orderedList = e.values.sort(
          (a: UserHealthDataValues, b: UserHealthDataValues) =>
            new Date(a.isoDate).getTime() - new Date(b.isoDate).getTime()
        );

        return {
          ...e,
          lastValue: orderedList[0].examnResult,
          values: orderedList,
        };
      }),
    };
  }
}
