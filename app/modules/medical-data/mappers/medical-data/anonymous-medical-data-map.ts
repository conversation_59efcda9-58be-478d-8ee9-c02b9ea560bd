/* eslint-disable @typescript-eslint/naming-convention */
import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { AnonymousMedicalData } from '../../domain/medical-data/AnonymousMedicalData';
import { Result } from '../../../../shared/core/Result';
import { Country } from '../../../../shared/common-value-objects/country';
import { Year } from '../../domain/medical-data/year';
import { ExcelDate } from '../../domain/medical-data/excel-date';
import { AnonymousMedicalDataDTO } from '../../dto/medical-data/anonymous-medical-data-dto';
import { LabExamDataMap } from './lab-exam-data-map';
import { ExcelGender } from '../../domain/medical-data/excel-gender';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { CountryMapService } from '../../../../shared/services/country-map-service';

export class AnonymousMedicalDataMap {
  public static toDto(
    medicalData: AnonymousMedicalData
  ): AnonymousMedicalDataDTO {
    return {
      id: medicalData.id.value,
      date: medicalData.date.value,
      patientGender: medicalData.patientGender.value,
      patientAge: medicalData.patientAge.value,
      country: medicalData.country.value,
      year: medicalData.year.value,
      createdAt: medicalData.createdAt.value,
      updatedAt: medicalData.updatedAt.value,
      ...LabExamDataMap.toDto(medicalData),
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): AnonymousMedicalData {
    const id = UUIDValueObject.create(raw.id);
    const date = ExcelDate.create('date', raw.date);
    const patientGender = ExcelGender.create(raw.patientGender);
    const patientAge = NumberValueObject.create('patientAge', raw.patientAge);
    const country = Country.create(raw.country);
    const year = Year.create(raw.year);
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const updatedAt = DateValueObject.create('updatedAt', raw.createdAt);
    const dtoCombine = Result.combine([
      id,
      patientGender,
      patientAge,
      date,
      country,
      year,
      createdAt,
      updatedAt,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const medicalData = AnonymousMedicalData.create({
      id: id.getValue(),
      date: date.getValue(),
      patientGender: patientGender.getValue(),
      patientAge: patientAge.getValue(),
      country: country.getValue(),
      year: year.getValue(),
      createdAt: createdAt.getValue(),
      updatedAt: updatedAt.getValue(),
      ...LabExamDataMap.toValueObject(raw),
    });
    if (medicalData.isFailure) {
      throw medicalData.getErrorValue();
    }

    return medicalData.getValue();
  }

  public static toPersistence(medicalData: AnonymousMedicalData) {
    return {
      id: medicalData.id.value,
      date: medicalData.date.value,
      patientGender: medicalData.patientGender.value,
      patientAge: medicalData.patientAge.value,
      country: medicalData.country.value,
      year: medicalData.year.value,
      createdAt: medicalData.createdAt.value,
      updatedAt: medicalData.updatedAt.value,
      ...LabExamDataMap.toPersistence(medicalData),
    };
  }

  public static toQuicksight(medicalData: AnonymousMedicalData) {
    const mapStringToNumber = (value: string | number): string => {
      try {
        switch (typeof value) {
          case 'undefined':
            throw new Error('Undefined value');
          case 'string':
            return value === '-'
              ? '-99999999'
              : parseFloat(value || '0').toFixed(2);
          case 'number':
            return parseFloat(value.toString()).toFixed(2);
          default:
            return value;
        }
      } catch (error) {
        return '-99999999';
      }
    };
    const removeSpecialCharacters = (
      dto: AnonymousMedicalDataDTO
    ): AnonymousMedicalDataDTO => {
      const numberValues = [
        'redBloodCells',
        'whiteBloodCells',
        'hemoglobin',
        'platelets',
        'glycemia',
        'colTotal',
        'HDL',
        'LDL',
        'TG',
        'relationshipCholesterolHDL',
        'CR',
        'BUN',
        'uricAcid',
        'GGT',
        'AST',
        'ALT',
        'FA',
        'totalBilirubin',
        'directBilirubin',
        'indirectBilirubin',
        'totalProteins',
        'TSH',
        'freeT3',
        'totalT4',
        'freeT4',
        'totalPSA',
        'HbA1C',
        'folicAcid',
        'bnp',
        'calcium',
        'totalCholesterol',
        'alkalinePhosphatase',
        'follicleStimulatingHormone',
        'luteinizingHormone',
        'fastingInsulin',
        'magnesium',
        'proBnpNt',
        'progesterone',
        'cReactiveProtein',
        'sodium',
        'transferrin',
        'activeVitaminB12',
        'vitaminD',
        'potassium',
        'totalTestosterone',
      ];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const formattedDTO: any = {};
      for (const [key, value] of Object.entries(dto)) {
        if (numberValues.includes(key) && value) {
          const formattedValue = mapStringToNumber(value);
          formattedDTO[key] = formattedValue;
        } else {
          formattedDTO[key] = value;
        }
      }

      return formattedDTO;
    };
    const data = this.toDto(medicalData);

    return {
      ...removeSpecialCharacters(data),
      id: medicalData.id.value,
      date: medicalData.date.value,
      patientGender: medicalData.patientGender.value,
      patientAge: medicalData.patientAge.value,
      country: CountryMapService.toHuman(medicalData.country.value),
      year: medicalData.year.value,
      createdAt: medicalData.createdAt.value,
      updatedAt: medicalData.updatedAt.value,
    };
  }
}
