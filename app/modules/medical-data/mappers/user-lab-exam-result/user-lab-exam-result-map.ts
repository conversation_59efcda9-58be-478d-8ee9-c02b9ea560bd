import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { SimpleEmptyTextValueObject } from '../../../../shared/common-value-objects/simple-empty-text-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { Identification } from '../../../user/domain/identification';
import { UserLabExamResult } from '../../domain/user-lab-exam-result/UserLabExamResult';
import { UserLabExamResultStatus } from '../../domain/user-lab-exam-result/user-lab-exam-result-status';
import { UserLabExamResultDTO } from '../../dto/user-lab-exam-result/user-lab-exam-result-dto';

export class UserLabExamResultMap {
  public static toDTO(
    userLabExamResult: UserLabExamResult
  ): UserLabExamResultDTO {
    return {
      id: userLabExamResult.id.value,
      userId: userLabExamResult.userId.value,
      identification: userLabExamResult.identification.value,
      labExamnId: userLabExamResult.labExamnId.value,
      labExamnValue: userLabExamResult.labExamnValue.value,
      userLabExamnResultStatus:
        userLabExamResult.userLabExamnResultStatus.value,
      updatedAt: userLabExamResult.updatedAt.value,
      createdAt: userLabExamResult.createdAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any) {
    const id = UUIDValueObject.create('id', raw.id);
    const userId = UUIDValueObject.create('userId', raw.userId);
    const identification = Identification.create(raw.identification);
    const labExamnId = SimpleEmptyTextValueObject.create(raw.labExamnId);
    const labExamnValue = NumberValueObject.create(
      'labExamnValue',
      raw.labExamnValue
    );
    const userLabExamnResultStatus = UserLabExamResultStatus.create(
      raw.userLabExamnResultStatus
    );
    const updatedAt = DateValueObject.create('updatedAt', raw.updatedAt);
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const dbCombined = Result.combine([
      id,
      userId,
      identification,
      labExamnId,
      labExamnValue,
      userLabExamnResultStatus,
      updatedAt,
      createdAt,
    ]);
    if (dbCombined.isFailure) throw dbCombined.getErrorValue();
    const userLabExamnResult = UserLabExamResult.create({
      id: id.getValue(),
      userId: userId.getValue(),
      identification: identification.getValue(),
      labExamnId: labExamnId.getValue(),
      labExamnValue: labExamnValue.getValue(),
      userLabExamnResultStatus: userLabExamnResultStatus.getValue(),
      updatedAt: updatedAt.getValue(),
      createdAt: createdAt.getValue(),
    });
    if (userLabExamnResult.isFailure) throw userLabExamnResult.getErrorValue();

    return userLabExamnResult.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toPersistence(userLabExamnResult: UserLabExamResult): any {
    return {
      id: userLabExamnResult.id.value,
      userId: userLabExamnResult.userId.value,
      identification: userLabExamnResult.identification.value,
      labExamnId: userLabExamnResult.labExamnId.value,
      labExamnValue: userLabExamnResult.labExamnValue.value,
      userLabExamnResultStatus:
        userLabExamnResult.userLabExamnResultStatus.value,
      updatedAt: userLabExamnResult.updatedAt.value,
      createdAt: userLabExamnResult.createdAt.value,
    };
  }
}
