// eslint-disable-next-line @typescript-eslint/naming-convention
export interface LabExamDataDTO {
  redBloodCells: string;
  whiteBloodCells: string;
  hemoglobin: string;
  platelets: string;
  glycemia: string;
  colTotal: string;
  HDL: string;
  LDL: string;
  TG: string;
  relationshipCholesterolHDL: string;
  CR: string;
  BUN: string;
  uricAcid: string;
  GGT: string;
  AST: string;
  ALT: string;
  FA: string;
  totalBilirubin: string;
  directBilirubin: string;
  indirectBilirubin: string;
  totalProteins: string;
  TSH: string;
  freeT3: string;
  totalT4: string;
  freeT4: string;
  totalPSA: string;
  HbA1C: string;
  EGO: string;
  electrocardiogram: string;
  conventionalPapSmear: string;
  abdomenUltrasound: string;
  breastUltrasound: string;
  diagnosis: string;
  referredSpecialty: string;
  folicAcid: string;
  bnp: string;
  calcium: string;
  totalCholesterol: string;
  alkalinePhosphatase: string;
  follicleStimulatingHormone: string;
  luteinizingHormone: string;
  fastingInsulin: string;
  magnesium: string;
  proBnpNt: string;
  progesterone: string;
  cReactiveProtein: string;
  sodium: string;
  transferrin: string;
  activeVitaminB12: string;
  vitaminD: string;
  potassium: string;
  totalTestosterone: string;
}
