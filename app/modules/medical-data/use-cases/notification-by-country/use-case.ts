import { UseCase } from '../../../../shared/core/use-case';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';
import { User } from '../../../user/domain/User';
import { GetUserListByCountryUseCase } from '../../../user/use-cases/get-users-by-country/use-case';
import { NotificationByCountryDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class NotificationByCountryUseCase
  implements UseCase<NotificationByCountryDTO, Promise<void>>
{
  constructor(
    private getUserListByCountryUseCase: GetUserListByCountryUseCase,
    private eventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute(dto: NotificationByCountryDTO) {
    const userList = await this.getUserList(dto);
    await this.notifyUsers(userList);

    return;
  }

  private async getUserList(dto: NotificationByCountryDTO) {
    return await this.getUserListByCountryUseCase.execute({
      country: dto.country,
    });
  }

  private async notifyUsers(userList: User[]) {
    for (const user of userList) {
      await this.sendNotification(user);
    }
  }

  private async sendNotification(user: User) {
    const dto = {
      userId: user.id.value,
      title: 'Nuevos Examens de Laboratorio 🧪',
      message: `Te informamos que el proveedor de salud de tu país ha ingresado nuevos resultados de exámenes de laboratorio.`,
      notificationType: 'CTA',
      callToAction: '../health/?view=LAB_EXAMS',
      priority: 'LOW',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }
}
