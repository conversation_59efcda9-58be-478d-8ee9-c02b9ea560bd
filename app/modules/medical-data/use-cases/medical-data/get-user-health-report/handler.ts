import AWS from 'aws-sdk';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { GetUserHeathReportUseCase } from './use-case';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UserMedicalDataRepository } from '../../../repo/medical-data/user-medical-data-repository';
import { UserRepository } from '../../../../user/repo/user-repository';
import { GetUserHealthReportDTO } from './dto';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { TokenServices } from '../../../../../shared/services/token-services';
import { TokenMiddleware } from '../../../../../shared/middlewares/token-middleware';

class Lambda extends BaseHandler implements LambdaInterface {
  private tokenMiddleware: TokenMiddleware;

  private getUserHeathReportUseCase: GetUserHeathReportUseCase;

  private init() {
    const tokenServices = new TokenServices();
    this.tokenMiddleware = new TokenMiddleware(tokenServices);
    const userMedicalDataRepository = new UserMedicalDataRepository(
      new AWS.DynamoDB(),
      process.env.USER_MEDICAL_TABLE_NAME || ''
    );
    const userLabExamResultRepository = new UserLabExamResultRepository(
      new AWS.DynamoDB(),
      process.env.USER_LAB_EXAM_RESULT_TABLE_NAME || ''
    );
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.USER_TABLE_NAME || ''
    );
    this.getUserHeathReportUseCase = new GetUserHeathReportUseCase(
      userRepository,
      userMedicalDataRepository,
      userLabExamResultRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result = await this.getUserHeathReportUseCase.execute(dto);

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<GetUserHealthReportDTO> {
    const auth = event.headers['Authorization'];
    if (!auth) throw 'Invalid Request Headers';
    const id = await this.tokenMiddleware.getUserIdFromToken(auth);

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
