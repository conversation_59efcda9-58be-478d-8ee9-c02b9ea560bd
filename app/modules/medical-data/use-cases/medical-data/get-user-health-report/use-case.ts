import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { User } from '../../../../user/domain/User';
import { UserRepository } from '../../../../user/repo/user-repository';
import { UserMedicalData } from '../../../domain/medical-data/UserMedicalData';
import { UserLabExamResult as UserLabExamResult } from '../../../domain/user-lab-exam-result/UserLabExamResult';
import { UserLabExamResultStatus } from '../../../domain/user-lab-exam-result/user-lab-exam-result-status';
import { UserHealthDTO } from '../../../dto/medical-data/user-health-dto';
import { UserMedicalDataRepository } from '../../../repo/medical-data/user-medical-data-repository';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { GetUserHealthReportDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';
import { LabExamService } from '../../../services/lab-exam-services';

export class GetUserHeathReportUseCase
  implements UseCase<GetUserHealthReportDTO, Promise<UserHealthDTO>>
{
  constructor(
    private userRepository: UserRepository,
    private userMedicalDataRepository: UserMedicalDataRepository,
    private userLabExamResultRepository: UserLabExamResultRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserHealthReportDTO) {
    const id = this.mapDTOtoUserID(dto);
    const user = await this.getExistingUser(id);
    const userLabExamResult = await this.getUserLabExamResult(id);
    const userMedicalData = await this.getUserMedicalDataByDocumentID(user);
    const userHealthDTO = LabExamService.MergeUserLabExamWithUserHealthData({
      userIdentification: user.identification.value,
      userLabExamResult,
      userMedicalData,
      includeDiagnosisResults: dto.includeDiagnosisResults || false,
    });

    return userHealthDTO;
  }

  private mapDTOtoUserID(dto: GetUserHealthReportDTO) {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getExistingUser(id: UUIDValueObject): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private async getUserLabExamResult(
    id: UUIDValueObject
  ): Promise<UserLabExamResult[]> {
    const status = this.getLabExamResultsStatus();
    const result =
      await this.userLabExamResultRepository.getUserLabExamResultsByStatus(
        id,
        status
      );

    return result || [];
  }

  private getLabExamResultsStatus() {
    const userLabExamResultStatus = UserLabExamResultStatus.create('ACTIVE');
    if (userLabExamResultStatus.isFailure)
      throw userLabExamResultStatus.getErrorValue();

    return userLabExamResultStatus.getValue();
  }

  private async getUserMedicalDataByDocumentID(
    user: User
  ): Promise<UserMedicalData[]> {
    const userMedicalData =
      await this.userMedicalDataRepository.getUserMedicalDataByDocumentId(user);

    return userMedicalData;
  }
}
