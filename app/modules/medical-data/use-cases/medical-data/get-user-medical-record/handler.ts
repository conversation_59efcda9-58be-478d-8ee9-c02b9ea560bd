import AWS from 'aws-sdk';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UserMedicalDataRepository } from '../../../repo/medical-data/user-medical-data-repository';
import { UserRepository } from '../../../../user/repo/user-repository';
import { GetUserMedicalReportDTO } from './dto';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { TokenServices } from '../../../../../shared/services/token-services';
import { TokenMiddleware } from '../../../../../shared/middlewares/token-middleware';
import { GetUserHeathReportUseCase } from '../get-user-health-report/use-case';
import { MedicalRecordService } from '../../../services/medical-record-service';
import { GetUserByIdUseCase } from '../../../../user/use-cases/get-users-by-id/use-case';

class Lambda extends BaseHandler implements LambdaInterface {
  private tokenMiddleware: TokenMiddleware;

  private getUserHeathReportUseCase: GetUserHeathReportUseCase;

  private getUserByIdUseCase: GetUserByIdUseCase;

  private init() {
    const tokenServices = new TokenServices();
    this.tokenMiddleware = new TokenMiddleware(tokenServices);
    const userMedicalDataRepository = new UserMedicalDataRepository(
      new AWS.DynamoDB(),
      process.env.USER_MEDICAL_TABLE_NAME || ''
    );
    const userLabExamResultRepository = new UserLabExamResultRepository(
      new AWS.DynamoDB(),
      process.env.USER_LAB_EXAM_RESULT_TABLE_NAME || ''
    );
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.USER_TABLE_NAME || ''
    );
    this.getUserHeathReportUseCase = new GetUserHeathReportUseCase(
      userRepository,
      userMedicalDataRepository,
      userLabExamResultRepository
    );
    this.getUserByIdUseCase = new GetUserByIdUseCase(userRepository);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const user = await this.getUserByIdUseCase.execute({ id: dto.id });
      const userHealthReport = await this.getUserHeathReportUseCase.execute({
        id: dto.id,
        includeDiagnosisResults: true,
      });
      const medicalRecord =
        MedicalRecordService.mapUserAndHealthDataMedicalReport({
          user,
          userHealthReport,
        });

      return this.ok(medicalRecord);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<GetUserMedicalReportDTO> {
    const auth = event.headers['Authorization'];
    if (!auth) throw 'Invalid Request Headers';
    const id = await this.tokenMiddleware.getUserIdFromToken(auth);

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
