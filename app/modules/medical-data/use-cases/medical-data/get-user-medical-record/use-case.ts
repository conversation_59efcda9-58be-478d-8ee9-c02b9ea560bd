import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { GetUserMedicalReportDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';
import { GetUserHeathReportUseCase } from '../get-user-health-report/use-case';
import { UserRepository } from '../../../../user/repo/user-repository';
import { UseCaseResponse } from './types';

export class GetUserMedicalReportUseCase
  implements UseCase<GetUserMedicalReportDTO, Promise<UseCaseResponse>>
{
  constructor(
    private getUserHeathReportUseCase: GetUserHeathReportUseCase,
    private userRepository: UserRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserMedicalReportDTO) {
    const id = this.mapDTOtoUserID(dto);
    const userHealthReport = await this.getUserHeathReport(id);
    const user = await this.getUser(id);

    return { user, userHealthReport };
  }

  private mapDTOtoUserID(dto: GetUserMedicalReportDTO) {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getUserHeathReport(id: UUIDValueObject) {
    const result = await this.getUserHeathReportUseCase.execute({
      id: id.value,
    });

    return result;
  }

  private async getUser(id: UUIDValueObject) {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'USER_NOT_FOUND';

    return user;
  }
}
