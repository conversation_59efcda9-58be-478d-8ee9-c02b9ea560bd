/* eslint-disable no-console */
import { UseCase } from '../../../../../shared/core/use-case';
import { QuickSight } from 'aws-sdk';
import { GetQuicksightEmbedURLDTO } from './dto';
import { CognitoRepository } from '../../../../../shared/repos/cognito-repository';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

const EMAIL = '<EMAIL>';
export class GetQuicksightEmbedURLUseCase
  implements UseCase<GetQuicksightEmbedURLDTO, Promise<string>>
{
  private readonly accountId = process.env.ACCOUNT_ID;

  private readonly sessionLifetimeInMinutes = 30;

  constructor(
    private quicksight: QuickSight,
    private quicksightBrazil: QuickSight,
    private cognitoRepository: CognitoRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetQuicksightEmbedURLDTO) {
    const userEmail = await this.getEmailFromAuthUserById(dto.id);
    const quicksightUser = await this.getActiveQuickSightUser(userEmail);
    console.log('quicksightUser: ', quicksightUser);
    const embedURL = this.generateEmbedURL();

    return embedURL;
  }

  private async getEmailFromAuthUserById(id: string) {
    const userData = await this.cognitoRepository.getUerById(id);
    if (!userData || !userData.UserAttributes) throw 'Missing Auth User';
    const email = userData.UserAttributes.filter(
      (user) => user.Name === 'email'
    ).map((user) => user.Value)[0];
    if (!email) {
      throw 'Invalid Email';
    }

    return email;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async getActiveQuickSightUser(email: string) {
    const listUsersResponse = await this.quicksight
      .listUsers({ AwsAccountId: this.accountId || '', Namespace: 'default' })
      .promise();
    const users = listUsersResponse.UserList;
    const userInformation = users
      ?.map((user) => {
        return {
          UserName: user.UserName,
          Email: user.Email,
          Role: user.Role,
          Active: user.Active,
          Arn: user.Arn,
        };
      })
      // TODO: validate incoming email
      .find((user) => user.Active === true && user.Email === EMAIL);
    if (!userInformation || !userInformation.Arn) throw 'Missing QS User';

    return userInformation;
  }

  private async generateEmbedURL() {
    const params = {
      AwsAccountId: this.accountId || '',
      SessionLifetimeInMinutes: this.sessionLifetimeInMinutes,
      ExperienceConfiguration: {
        Dashboard: {
          InitialDashboardId: process.env.DASHBOARD_ID || '',
        },
      },
      UserArn: `arn:aws:quicksight:us-east-1:${this.accountId}:user/default/${this.accountId}`,
    };
    const response = await this.quicksightBrazil
      .generateEmbedUrlForRegisteredUser(params)
      .promise();
    if (!response) throw 'Error Accessing QuickSight';
    const embedUrl = response.EmbedUrl;
    if (!embedUrl) throw 'Error Creating URL';

    return embedUrl;
  }
}
