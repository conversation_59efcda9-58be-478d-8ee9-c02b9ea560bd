import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { GetQuicksightEmbedURLUseCase } from './use-case';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { CognitoRepository } from '../../../../../shared/repos/cognito-repository';
import AWS from 'aws-sdk';
import { TokenMiddleware } from '../../../../../shared/middlewares/token-middleware';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { TokenServices } from '../../../../../shared/services/token-services';
import { GetQuicksightEmbedURLDTO } from './dto';

class <PERSON>da extends BaseHandler implements LambdaInterface {
  private tokenMiddleware: TokenMiddleware;

  private getQuicksightEmbedURLUseCase: GetQuicksightEmbedURLUseCase;

  private init() {
    const userPoolId = process.env.COGNITO_POOL || '';
    const clientId = process.env.CLIENT_ID || '';
    const tokenServices = new TokenServices();
    this.tokenMiddleware = new TokenMiddleware(
      tokenServices,
      userPoolId,
      clientId
    );
    const quicksight = new AWS.QuickSight({ region: 'us-east-1' });
    const quicksightBrazil = new AWS.QuickSight({ region: 'sa-east-1' });
    const cognitoService = new CognitoRepository(
      new AWS.CognitoIdentityServiceProvider(),
      process.env.COGNITO_POOL || ''
    );
    this.getQuicksightEmbedURLUseCase = new GetQuicksightEmbedURLUseCase(
      quicksight,
      quicksightBrazil,
      cognitoService
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result = await this.getQuicksightEmbedURLUseCase.execute(dto);

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<GetQuicksightEmbedURLDTO> {
    const auth = event.headers['Authorization'];
    if (!auth) throw 'Invalid Request Headers';
    const id = await this.tokenMiddleware.getUserIdFromCognitoToken(auth);

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
