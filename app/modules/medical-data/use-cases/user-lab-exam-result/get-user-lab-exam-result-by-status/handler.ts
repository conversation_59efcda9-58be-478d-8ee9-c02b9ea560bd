import AWS from 'aws-sdk';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { UserLabExamResultMap } from '../../../mappers/user-lab-exam-result/user-lab-exam-result-map';
import { UserLabExamResult } from '../../../domain/user-lab-exam-result/UserLabExamResult';
import { GetUserLabExamResultListByStatusDTO } from './dto';
import { GetUserLabExamResultListByStatusUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { TokenServices } from '../../../../../shared/services/token-services';
import { TokenMiddleware } from '../../../../../shared/middlewares/token-middleware';

class Lambda extends BaseHandler implements LambdaInterface {
  private tokenMiddleware: TokenMiddleware;

  private getUserLabExamResultListByStatusUseCase: GetUserLabExamResultListByStatusUseCase;

  private init() {
    const tokenServices = new TokenServices();
    this.tokenMiddleware = new TokenMiddleware(tokenServices);
    const userLabExamResultRepository = new UserLabExamResultRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.getUserLabExamResultListByStatusUseCase =
      new GetUserLabExamResultListByStatusUseCase(userLabExamResultRepository);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result = await this.getUserLabExamResultListByStatusUseCase.execute(
        dto
      );

      return this.ok(
        result.map((user: UserLabExamResult) =>
          UserLabExamResultMap.toDTO(user)
        )
      );
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<GetUserLabExamResultListByStatusDTO> {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const auth = event.headers['Authorization'];
    if (!auth) throw 'Invalid Request Headers';
    const id = await this.tokenMiddleware.getUserIdFromToken(auth);
    const userData = JSON.parse(
      event.body
    ) as GetUserLabExamResultListByStatusDTO;

    return {
      userId: id,
      status: userData.status,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
