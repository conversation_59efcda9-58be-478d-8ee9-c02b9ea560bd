import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';
import { UserLabExamResult as UserLabExamResult } from '../../../domain/user-lab-exam-result/UserLabExamResult';
import { UserLabExamResultStatus } from '../../../domain/user-lab-exam-result/user-lab-exam-result-status';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';

import { GetUserLabExamResultListByStatusDTO as GetUserLabExamResultListByStatusDTO } from './dto';

export class GetUserLabExamResultListByStatusUseCase
  implements
    UseCase<GetUserLabExamResultListByStatusDTO, Promise<UserLabExamResult[]>>
{
  constructor(
    private userLabExamResultRepository: UserLabExamResultRepository
  ) {}

  @LogUseCaseDTO
  public execute(dto: GetUserLabExamResultListByStatusDTO) {
    const userId = this.mapDTOtUserId(dto);
    const userLabExamResultStatus = this.mapDTOtUserLabExamResultStatus(dto);

    return this.userLabExamResultRepository.getUserLabExamResultsByStatus(
      userId,
      userLabExamResultStatus
    );
  }

  private mapDTOtUserId(dto: GetUserLabExamResultListByStatusDTO) {
    const userId = UUIDValueObject.create('userId', dto.userId);
    if (userId.isFailure) throw userId.getErrorValue();

    return userId.getValue();
  }

  private mapDTOtUserLabExamResultStatus(
    dto: GetUserLabExamResultListByStatusDTO
  ) {
    const userLabExamResultStatus = UserLabExamResultStatus.create(dto.status);
    if (userLabExamResultStatus.isFailure)
      throw userLabExamResultStatus.getErrorValue();

    return userLabExamResultStatus.getValue();
  }
}
