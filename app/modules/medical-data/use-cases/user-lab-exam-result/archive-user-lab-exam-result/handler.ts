import AWS from 'aws-sdk';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { UserLabExamResultMap } from '../../../mappers/user-lab-exam-result/user-lab-exam-result-map';
import { ArchiveUserLabExamResultUseCase } from './use-case';
import { ArchiveUserLabExamResultDTO } from './dto';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private archiveUserLabExamResultUseCase: ArchiveUserLabExamResultUseCase;

  private init() {
    const userRepository = new UserLabExamResultRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.archiveUserLabExamResultUseCase = new ArchiveUserLabExamResultUseCase(
      userRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.archiveUserLabExamResultUseCase.execute(dto);

      return this.ok(UserLabExamResultMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): ArchiveUserLabExamResultDTO {
    const id = event.queryStringParameters?.id;
    if (!id) {
      throw 'Failed to parse request body';
    }

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
