import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { UserLabExamResult } from '../../../domain/user-lab-exam-result/UserLabExamResult';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { ArchiveUserLabExamResultDTO as ArchiveUserLabExamResultDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class ArchiveUserLabExamResultUseCase
  implements UseCase<ArchiveUserLabExamResultDTO, Promise<UserLabExamResult>>
{
  constructor(
    private userLabExamResultRepository: UserLabExamResultRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: ArchiveUserLabExamResultDTO) {
    const id = this.mapDTOToUserLabExamResultId(dto);
    const existingUserLabExamResult = await this.getExistingUserLabExamResult(
      id
    );
    const updatedUserLabExamResult = this.changeUserLabExamResultStatus(
      existingUserLabExamResult
    );
    const result = await this.updateUserLabExamResult(updatedUserLabExamResult);

    return result;
  }

  private mapDTOToUserLabExamResultId(
    dto: ArchiveUserLabExamResultDTO
  ): UUIDValueObject {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingUserLabExamResult(
    id: UUIDValueObject
  ): Promise<UserLabExamResult> {
    const userLabExamResult =
      await this.userLabExamResultRepository.getUserLabExamResultById(id);
    if (!userLabExamResult) throw 'User Lab Exam Result Not Found';

    return userLabExamResult;
  }

  private changeUserLabExamResultStatus(
    userLabExamResult: UserLabExamResult
  ): UserLabExamResult {
    userLabExamResult.archive();

    return userLabExamResult;
  }

  private async updateUserLabExamResult(
    userLabExamResult: UserLabExamResult
  ): Promise<UserLabExamResult> {
    const result =
      await this.userLabExamResultRepository.updateUserLabExamResult(
        userLabExamResult
      );
    if (!result) throw 'Error Updating User Lab Exam Result';

    return result;
  }
}
