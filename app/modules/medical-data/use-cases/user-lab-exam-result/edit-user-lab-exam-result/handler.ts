import AWS from 'aws-sdk';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { UserLabExamResultMap } from '../../../mappers/user-lab-exam-result/user-lab-exam-result-map';
import { EditUserLabExamResultDataUseCase } from './use-case';
import { EditUserLabExamResultDTO } from './dto';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private editUserLabExamResultDataUseCase: EditUserLabExamResultDataUseCase;

  private init() {
    const userRepository = new UserLabExamResultRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.editUserLabExamResultDataUseCase =
      new EditUserLabExamResultDataUseCase(userRepository);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.editUserLabExamResultDataUseCase.execute(dto);

      return this.ok(UserLabExamResultMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): EditUserLabExamResultDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const userData = JSON.parse(event.body) as EditUserLabExamResultDTO;

    return {
      ...userData,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
