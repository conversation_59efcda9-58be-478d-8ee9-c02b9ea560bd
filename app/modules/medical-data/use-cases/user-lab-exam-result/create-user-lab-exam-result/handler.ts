import AWS from 'aws-sdk';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { UserLabExamResultMap } from '../../../mappers/user-lab-exam-result/user-lab-exam-result-map';
import { CreateUserLabExamResultUseCase } from './use-case';
import { CreateUserLabExamResultDTO } from './dto';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { TokenMiddleware } from '../../../../../shared/middlewares/token-middleware';
import { TokenServices } from '../../../../../shared/services/token-services';
import { UserRepository } from '../../../../user/repo/user-repository';
import { HTTPRequestBodyValidator } from '../../../../../shared/infra/http-request-body-validator';

class Lambda extends BaseHandler implements LambdaInterface {
  private tokenMiddleware: TokenMiddleware;

  private createUserLabExamResultUseCase: CreateUserLabExamResultUseCase;

  private init() {
    const tokenServices = new TokenServices();
    this.tokenMiddleware = new TokenMiddleware(tokenServices);
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.USER_TABLE_NAME || ''
    );
    const userLabExamResultRepository = new UserLabExamResultRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.createUserLabExamResultUseCase = new CreateUserLabExamResultUseCase(
      userLabExamResultRepository,
      userRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result = await this.createUserLabExamResultUseCase.execute(dto);

      return this.ok(UserLabExamResultMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<CreateUserLabExamResultDTO> {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const auth = event.headers['Authorization'];
    if (!auth) throw 'Invalid Request Headers';
    const id = await this.tokenMiddleware.getUserIdFromToken(auth);
    const BODY_SCHEMA = ['labExamnId', 'labExamnValue', 'date'];
    const payload = HTTPRequestBodyValidator<CreateUserLabExamResultDTO>(
      BODY_SCHEMA,
      event.body
    );

    return {
      ...payload,
      userId: id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
