import { UseCase } from '../../../../../shared/core/use-case';
import { UserLabExamResult } from '../../../domain/user-lab-exam-result/UserLabExamResult';
import { UserLabExamResultMap } from '../../../mappers/user-lab-exam-result/user-lab-exam-result-map';
import { UserLabExamResultRepository } from '../../../repo/user-lab-exam-result/user-lab-exam-result-repository';
import { CreateUserLabExamResultDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';
import { UserRepository } from '../../../../user/repo/user-repository';
import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { User } from '../../../../user/domain/User';

export class CreateUserLabExamResultUseCase
  implements UseCase<CreateUserLabExamResultDTO, Promise<UserLabExamResult>>
{
  constructor(
    private userLabExamResultRepository: UserLabExamResultRepository,
    private userRepository: UserRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: CreateUserLabExamResultDTO) {
    const id = this.mapDTOtoUserID(dto);
    const user = await this.getExistingUser(id);
    const userLabExamResult = this.mapDTOtoUserLabExamResult(dto, user);
    await this.getExistingUserLabExamResult(userLabExamResult);
    const userLabExamResultCreated = await this.createUserLabExamResults(
      userLabExamResult
    );

    return userLabExamResultCreated;
  }

  private mapDTOtoUserLabExamResult(
    dto: CreateUserLabExamResultDTO,
    user: User
  ) {
    const userLabExamResult = UserLabExamResultMap.toDomain({
      ...dto,
      identification: user.identification.value,
      userLabExamnResultStatus: 'ACTIVE',
      createdAt: dto.date,
      updatedAt: dto.date,
    });

    return userLabExamResult;
  }

  private mapDTOtoUserID(dto: CreateUserLabExamResultDTO) {
    const id = UUIDValueObject.create('id', dto.userId);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getExistingUser(id: UUIDValueObject): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private async getExistingUserLabExamResult(
    userLabExamResult: UserLabExamResult
  ) {
    const existingUserLabExamResult =
      await this.userLabExamResultRepository.getUserLabExamResultById(
        userLabExamResult.id
      );
    if (existingUserLabExamResult) throw 'Existing user lab examn result';
  }

  private async createUserLabExamResults(
    userLabExamResult: UserLabExamResult
  ): Promise<UserLabExamResult> {
    const userLabExamResultCreated =
      await this.userLabExamResultRepository.createUserLabExamResult(
        userLabExamResult
      );
    if (!userLabExamResultCreated) throw 'Error Updating User Lab Exam Result';

    return userLabExamResultCreated;
  }
}
