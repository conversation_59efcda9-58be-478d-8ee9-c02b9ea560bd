import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../../shared/common-value-objects/simple-text-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { Identification } from '../../../user/domain/identification';
import { UserLabExamResultStatus } from './user-lab-exam-result-status';

export interface IUserLabExamResult {
  id: UUIDValueObject;
  userId: UUIDValueObject;
  identification: Identification;
  labExamnId: SimpleTextValueObject;
  labExamnValue: NumberValueObject;
  userLabExamnResultStatus: UserLabExamResultStatus;
  updatedAt: DateValueObject;
  createdAt: DateValueObject;
}
export class UserLabExamResult {
  private props: IUserLabExamResult;

  constructor(props: IUserLabExamResult) {
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get userId(): UUIDValueObject {
    return this.props.userId;
  }

  get identification(): Identification {
    return this.props.identification;
  }

  get labExamnId(): SimpleTextValueObject {
    return this.props.labExamnId;
  }

  get labExamnValue(): NumberValueObject {
    return this.props.labExamnValue;
  }

  get userLabExamnResultStatus(): UserLabExamResultStatus {
    return this.props.userLabExamnResultStatus;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  public update(raw: { labExamnId: string; labExamnValue: number }) {
    const labExamnId = SimpleTextValueObject.create(
      'labExamId',
      raw.labExamnId
    );
    const labExamnValue = NumberValueObject.create('value', raw.labExamnValue);
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([labExamnId, labExamnValue, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.labExamnId = labExamnId.getValue();
    this.props.labExamnValue = labExamnValue.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public archive() {
    const status = UserLabExamResultStatus.create('ARCHIVED');
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.userLabExamnResultStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public static create(props: IUserLabExamResult) {
    const useLabExamnResult = new UserLabExamResult(props);

    return Result.ok<UserLabExamResult>(useLabExamnResult);
  }
}
