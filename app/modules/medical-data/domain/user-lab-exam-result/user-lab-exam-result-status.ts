import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

type UserLabExamResultStatusOptions = 'ACTIVE' | 'ARCHIVED';
interface IUserLabExamResultStatusProps {
  value: string;
}
export class UserLabExamResultStatus extends ValueObject<IUserLabExamResultStatusProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IUserLabExamResultStatusProps) {
    super(props);
  }

  private static isStatusValid(userLabExamResultStatus: string) {
    if (
      !userLabExamResultStatus ||
      (userLabExamResultStatus !== 'ACTIVE' &&
        userLabExamResultStatus !== 'ARCHIVED')
    ) {
      return false;
    }

    return true;
  }

  public static create(
    status: UserLabExamResultStatusOptions | string
  ): Result<UserLabExamResultStatus> {
    if (!this.isStatusValid(status)) {
      return Result.fail<UserLabExamResultStatus>(
        'Invalid User Lab Exam Result Status'
      );
    }

    return Result.ok<UserLabExamResultStatus>(
      new UserLabExamResultStatus({ value: status })
    );
  }
}
