import { Guard } from '../../../../shared/core/Guard';
import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

export type GenderOptions = 'M' | 'F' | '-';
interface IExcelGenderProps {
  value: GenderOptions;
}
export class ExcelGender extends ValueObject<IExcelGenderProps> {
  get value(): GenderOptions {
    return this.props.value;
  }

  private constructor(props: IExcelGenderProps) {
    super(props);
  }

  private static setGenderOption(gender: string): GenderOptions {
    if (!gender) return '-';

    return gender.toLocaleLowerCase().includes('f') ? 'F' : 'M';
  }

  public static create(output: string): Result<ExcelGender> {
    const textValidation = Guard.againstNullOrUndefined(output, 'gender');
    if (textValidation.isFailure) {
      return Result.ok<ExcelGender>(new ExcelGender({ value: '-' }));
    }
    const gender: GenderOptions = this.setGenderOption(output);

    return Result.ok<ExcelGender>(new ExcelGender({ value: gender }));
  }
}
