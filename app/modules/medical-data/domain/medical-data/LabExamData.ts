import { Result } from '../../../../shared/core/Result';
import { MedicalOutput } from './medical-output';

export interface ILabExamDataProps {
  redBloodCells: MedicalOutput;
  whiteBloodCells: MedicalOutput;
  hemoglobin: MedicalOutput;
  platelets: MedicalOutput;
  glycemia: MedicalOutput;
  colTotal: MedicalOutput;
  HDL: MedicalOutput;
  LDL: MedicalOutput;
  TG: MedicalOutput;
  relationshipCholesterolHDL: MedicalOutput;
  CR: MedicalOutput;
  BUN: MedicalOutput;
  uricAcid: MedicalOutput;
  GGT: MedicalOutput;
  AST: MedicalOutput;
  ALT: MedicalOutput;
  FA: MedicalOutput;
  totalBilirubin: MedicalOutput;
  directBilirubin: MedicalOutput;
  indirectBilirubin: MedicalOutput;
  totalProteins: MedicalOutput;
  TSH: MedicalOutput;
  freeT3: MedicalOutput;
  totalT4: MedicalOutput;
  freeT4: MedicalOutput;
  EGO: MedicalOutput;
  totalPSA: MedicalOutput;
  HbA1C: MedicalOutput;
  electrocardiogram: MedicalOutput;
  conventionalPapSmear: MedicalOutput;
  abdomenUltrasound: MedicalOutput;
  breastUltrasound: MedicalOutput;
  diagnosis: MedicalOutput;
  referredSpecialty: MedicalOutput;
  folicAcid: MedicalOutput;
  bnp: MedicalOutput;
  calcium: MedicalOutput;
  totalCholesterol: MedicalOutput;
  alkalinePhosphatase: MedicalOutput;
  follicleStimulatingHormone: MedicalOutput;
  luteinizingHormone: MedicalOutput;
  fastingInsulin: MedicalOutput;
  magnesium: MedicalOutput;
  proBnpNt: MedicalOutput;
  progesterone: MedicalOutput;
  cReactiveProtein: MedicalOutput;
  sodium: MedicalOutput;
  transferrin: MedicalOutput;
  activeVitaminB12: MedicalOutput;
  vitaminD: MedicalOutput;
  potassium: MedicalOutput;
  totalTestosterone: MedicalOutput;
}
export class LabExamData {
  private labProps: ILabExamDataProps;

  constructor(props: ILabExamDataProps) {
    this.labProps = props;
  }

  get redBloodCells(): MedicalOutput {
    return this.labProps.redBloodCells;
  }

  get whiteBloodCells(): MedicalOutput {
    return this.labProps.whiteBloodCells;
  }

  get hemoglobin(): MedicalOutput {
    return this.labProps.hemoglobin;
  }

  get platelets(): MedicalOutput {
    return this.labProps.platelets;
  }

  get glycemia(): MedicalOutput {
    return this.labProps.glycemia;
  }

  get colTotal(): MedicalOutput {
    return this.labProps.colTotal;
  }

  get HDL(): MedicalOutput {
    return this.labProps.HDL;
  }

  get LDL(): MedicalOutput {
    return this.labProps.LDL;
  }

  get TG(): MedicalOutput {
    return this.labProps.TG;
  }

  get relationshipCholesterolHDL(): MedicalOutput {
    return this.labProps.relationshipCholesterolHDL;
  }

  get CR(): MedicalOutput {
    return this.labProps.CR;
  }

  get BUN(): MedicalOutput {
    return this.labProps.BUN;
  }

  get uricAcid(): MedicalOutput {
    return this.labProps.uricAcid;
  }

  get GGT(): MedicalOutput {
    return this.labProps.GGT;
  }

  get AST(): MedicalOutput {
    return this.labProps.AST;
  }

  get ALT(): MedicalOutput {
    return this.labProps.ALT;
  }

  get FA(): MedicalOutput {
    return this.labProps.FA;
  }

  get totalBilirubin(): MedicalOutput {
    return this.labProps.totalBilirubin;
  }

  get directBilirubin(): MedicalOutput {
    return this.labProps.directBilirubin;
  }

  get indirectBilirubin(): MedicalOutput {
    return this.labProps.indirectBilirubin;
  }

  get totalProteins(): MedicalOutput {
    return this.labProps.totalProteins;
  }

  get TSH(): MedicalOutput {
    return this.labProps.TSH;
  }

  get freeT3(): MedicalOutput {
    return this.labProps.freeT3;
  }

  get totalT4(): MedicalOutput {
    return this.labProps.totalT4;
  }

  get freeT4(): MedicalOutput {
    return this.labProps.freeT4;
  }

  get EGO(): MedicalOutput {
    return this.labProps.EGO;
  }

  get totalPSA(): MedicalOutput {
    return this.labProps.totalPSA;
  }

  get HbA1C(): MedicalOutput {
    return this.labProps.HbA1C;
  }

  get electrocardiogram(): MedicalOutput {
    return this.labProps.electrocardiogram;
  }

  get conventionalPapSmear(): MedicalOutput {
    return this.labProps.conventionalPapSmear;
  }

  get abdomenUltrasound(): MedicalOutput {
    return this.labProps.abdomenUltrasound;
  }

  get breastUltrasound(): MedicalOutput {
    return this.labProps.breastUltrasound;
  }

  get diagnosis(): MedicalOutput {
    return this.labProps.diagnosis;
  }

  get referredSpecialty(): MedicalOutput {
    return this.labProps.referredSpecialty;
  }

  get folicAcid(): MedicalOutput {
    return this.labProps.folicAcid;
  }

  get bnp(): MedicalOutput {
    return this.labProps.bnp;
  }

  get calcium(): MedicalOutput {
    return this.labProps.calcium;
  }

  get totalCholesterol(): MedicalOutput {
    return this.labProps.totalCholesterol;
  }

  get alkalinePhosphatase(): MedicalOutput {
    return this.labProps.alkalinePhosphatase;
  }

  get follicleStimulatingHormone(): MedicalOutput {
    return this.labProps.follicleStimulatingHormone;
  }

  get luteinizingHormone(): MedicalOutput {
    return this.labProps.luteinizingHormone;
  }

  get fastingInsulin(): MedicalOutput {
    return this.labProps.fastingInsulin;
  }

  get magnesium(): MedicalOutput {
    return this.labProps.magnesium;
  }

  get proBnpNt(): MedicalOutput {
    return this.labProps.proBnpNt;
  }

  get progesterone(): MedicalOutput {
    return this.labProps.progesterone;
  }

  get cReactiveProtein(): MedicalOutput {
    return this.labProps.cReactiveProtein;
  }

  get sodium(): MedicalOutput {
    return this.labProps.sodium;
  }

  get transferrin(): MedicalOutput {
    return this.labProps.transferrin;
  }

  get activeVitaminB12(): MedicalOutput {
    return this.labProps.activeVitaminB12;
  }

  get vitaminD(): MedicalOutput {
    return this.labProps.vitaminD;
  }

  get potassium(): MedicalOutput {
    return this.labProps.potassium;
  }

  get totalTestosterone(): MedicalOutput {
    return this.labProps.totalTestosterone;
  }

  public static create(props: ILabExamDataProps) {
    const labExamData = new LabExamData({
      ...props,
    });

    return Result.ok<LabExamData>(labExamData);
  }
}
