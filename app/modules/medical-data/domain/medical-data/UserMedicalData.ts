import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { MedicalOutput } from './medical-output';
import { Result } from '../../../../shared/core/Result';
import { Country } from '../../../../shared/common-value-objects/country';
import { Year } from './year';
import { ExcelDate } from './excel-date';
import { ILabExamDataProps, LabExamData } from './LabExamData';
import { SimpleTextValueObject } from '../../../../shared/common-value-objects/simple-text-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { ExcelGender } from './excel-gender';

export interface IUserMedicalDataProps extends ILabExamDataProps {
  id: UUIDValueObject;
  date: ExcelDate;
  patientName: SimpleTextValueObject;
  documentId: SimpleTextValueObject;
  patientGender: ExcelGender;
  patientAge: NumberValueObject;
  dateOfBirth: ExcelDate;
  country: Country;
  year: Year;
  createdAt: DateValueObject;
  updatedAt: DateValueObject;
}
export class UserMedicalData extends LabExamData {
  private props: IUserMedicalDataProps;

  constructor(props: IUserMedicalDataProps) {
    super(props);
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get date(): ExcelDate {
    return this.props.date;
  }

  get patientName(): SimpleTextValueObject {
    return this.props.patientName;
  }

  get documentId(): MedicalOutput {
    return this.props.documentId;
  }

  get patientGender(): ExcelGender {
    return this.props.patientGender;
  }

  get patientAge(): NumberValueObject {
    return this.props.patientAge;
  }

  get dateOfBirth(): ExcelDate {
    return this.props.dateOfBirth;
  }

  get country(): Country {
    return this.props.country;
  }

  get year(): Year {
    return this.props.year;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  public static create(props: IUserMedicalDataProps) {
    const medialData = new UserMedicalData({
      ...props,
    });

    return Result.ok<UserMedicalData>(medialData);
  }
}
