import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

interface IMedicalOutputPorps {
  value: string;
}
export class MedicalOutput extends ValueObject<IMedicalOutputPorps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IMedicalOutputPorps) {
    super(props);
  }

  public static create(output: string): Result<MedicalOutput> {
    const value = output ? output.toString().trim() : '-';

    return Result.ok<MedicalOutput>(new MedicalOutput({ value }));
  }
}
