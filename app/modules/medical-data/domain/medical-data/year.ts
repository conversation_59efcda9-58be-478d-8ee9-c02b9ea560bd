import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

interface IYearProps {
  value: string;
}
export class Year extends ValueObject<IYearProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IYearProps) {
    super(props);
  }

  private static isValidYear(year: string) {
    if (year === null) {
      return false;
    }
    const regex = /^\d{4}$/;

    return regex.test(year);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static create(year: any): Result<Year> {
    if (!this.isValidYear(year)) {
      return Result.fail<Year>('Invalid Year');
    }

    return Result.ok<Year>(new Year({ value: year }));
  }
}
