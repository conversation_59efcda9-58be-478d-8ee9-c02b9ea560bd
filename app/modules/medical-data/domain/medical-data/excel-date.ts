/* eslint-disable @typescript-eslint/naming-convention */
import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';
import { parse, isValid, startOfMonth } from 'date-fns';

interface IExcelDateProps {
  value: string;
}
export class ExcelDate extends ValueObject<IExcelDateProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IExcelDateProps) {
    super(props);
  }

  // Expresiones regulares para detectar los formatos de cadena
  private static readonly dateFormats = {
    dayMonthYear: /^(\d{2})[-/](\d{2})[-/](\d{4})$/, // dd-mm-yyyy o dd/mm/yyyy
    monthYear: /^(\d{2})[-/](\d{4})$/, // mm-yyyy o mm/yyyy
  };

  // Conversión de fecha de Excel a ISO
  private static parseExcelDateToISOString(excelDate: number): string {
    const date = new Date((excelDate - 25569) * 86400 * 1000);

    return date.toISOString();
  }

  // Validación y conversión de cadenas a ISO
  private static parseStringDate(dateStr: string): string {
    // Detectar el formato dd-mm-yyyy o dd/mm/yyyy
    const dmyMatch = dateStr.match(this.dateFormats.dayMonthYear);
    if (dmyMatch) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const [_, day, month, year] = dmyMatch;
      const parsedDate = parse(
        `${year}-${month}-${day}`,
        'yyyy-MM-dd',
        new Date()
      );
      if (isValid(parsedDate)) {
        return parsedDate.toISOString();
      }
    }
    // Detectar el formato mm-yyyy o mm/yyyy
    const myMatch = dateStr.match(this.dateFormats.monthYear);
    if (myMatch) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const [_, month, year] = myMatch;
      const parsedDate = startOfMonth(
        parse(`${year}-${month}-01`, 'yyyy-MM-dd', new Date())
      );
      if (isValid(parsedDate)) {
        return parsedDate.toISOString();
      }
    }
    // Validar si es un ISO string válido
    const parsedDate = new Date(dateStr);
    if (isValid(parsedDate)) {
      return parsedDate.toISOString();
    }
    throw new Error('Invalid date format');
  }

  // Crear instancia del Value Object
  public static create(
    argumentName: string,
    input: number | string
  ): Result<ExcelDate> {
    try {
      let isoDate: string;
      if (typeof input === 'number') {
        if (input > 0) {
          isoDate = this.parseExcelDateToISOString(input);
        } else {
          throw new Error('Invalid Excel date number');
        }
      } else if (typeof input === 'string' && input === '-') {
        isoDate = this.parseStringDate(new Date().toISOString().split('T')[0]);
      } else if (typeof input === 'string') {
        isoDate = this.parseStringDate(input);
      } else {
        throw new Error('Invalid input format');
      }

      return Result.ok<ExcelDate>(new ExcelDate({ value: isoDate }));
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        'Error creating Excel Date Value Object: ',
        error,
        'input: ',
        input
      );

      return Result.fail<ExcelDate>(`Invalid Date: ${argumentName}`);
    }
  }

  public getValue(): string {
    return this.value;
  }
}
