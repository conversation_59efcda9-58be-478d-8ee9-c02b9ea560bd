import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

interface IExcelTimeProps {
  value: string;
}
export class ExcelTime extends ValueObject<IExcelTimeProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IExcelTimeProps) {
    super(props);
  }

  private static parseExcelTimeToISOString(time: number): string {
    const millisecondsPerDay = 24 * 60 * 60 * 1000;
    const timeInMilliseconds = time * millisecondsPerDay;
    const parsedTime = new Date(timeInMilliseconds);

    return parsedTime.toISOString().substr(11, 8);
  }

  private static isValidNumber(phone: number) {
    return typeof phone === 'number' && !isNaN(phone) && phone !== undefined;
  }

  public static create(excelTime: number): Result<ExcelTime> {
    const excelTimeParsed =
      this.isValidNumber(excelTime) &&
      this.parseExcelTimeToISOString(excelTime);
    const time = excelTimeParsed ? excelTimeParsed : '-';

    return Result.ok<ExcelTime>(new ExcelTime({ value: time }));
  }
}
