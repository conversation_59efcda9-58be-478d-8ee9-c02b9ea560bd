import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { Country } from '../../../../shared/common-value-objects/country';
import { Year } from './year';
import { ExcelDate } from './excel-date';
import { ILabExamDataProps, LabExamData } from './LabExamData';
import { ExcelGender } from './excel-gender';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';

export interface IAnonymousMedicalDataProps extends ILabExamDataProps {
  id: UUIDValueObject;
  date: ExcelDate;
  patientGender: ExcelGender;
  patientAge: NumberValueObject;
  country: Country;
  year: Year;
  createdAt: DateValueObject;
  updatedAt: DateValueObject;
}
export class AnonymousMedicalData extends LabExamData {
  private props: IAnonymousMedicalDataProps;

  constructor(props: IAnonymousMedicalDataProps) {
    super(props);
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get date(): ExcelDate {
    return this.props.date;
  }

  get patientGender(): ExcelGender {
    return this.props.patientGender;
  }

  get patientAge(): NumberValueObject {
    return this.props.patientAge;
  }

  get country(): Country {
    return this.props.country;
  }

  get year(): Year {
    return this.props.year;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  public static create(props: IAnonymousMedicalDataProps) {
    const medialData = new AnonymousMedicalData({
      ...props,
    });

    return Result.ok<AnonymousMedicalData>(medialData);
  }
}
