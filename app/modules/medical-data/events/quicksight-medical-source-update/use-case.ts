import { UseCase } from '../../../../shared/core/use-case';
import { AnonymousMedicalDataRepository } from '../../repo/medical-data/anonymous-medical-data-repository';
import { AnonymousMedicalData } from '../../domain/medical-data/AnonymousMedicalData';
import { S3Service } from '../../../../shared/repos/file-repository';
import { AnonymousMedicalDataMap } from '../../mappers/medical-data/anonymous-medical-data-map';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

const KEY = 'quicksight-medical-data';
export class QuicksightMedicalSourceUpdateUseCase
  implements UseCase<void, Promise<void>>
{
  constructor(
    private anonymousMedicalDataRepository: AnonymousMedicalDataRepository,
    private s3Service: S3Service
  ) {}

  @LogUseCaseDTO
  public async execute() {
    const medicalData = await this.getMedicalData();
    if (medicalData && medicalData.length > 0) {
      const jsonData = this.mapAnonymousMedicalDataToJSON(medicalData);
      await this.storeJSONFileToS3(jsonData);
    }
  }

  private async getMedicalData() {
    const medicalData =
      await this.anonymousMedicalDataRepository.getAllMedicalData();

    return medicalData;
  }

  private mapAnonymousMedicalDataToJSON(medicalData: AnonymousMedicalData[]) {
    const jsonData = medicalData.map((data) =>
      AnonymousMedicalDataMap.toQuicksight(data)
    );

    return jsonData;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private async storeJSONFileToS3(jsonData: any[]) {
    const s3ServiceProps = {
      body: JSON.stringify(jsonData),
      key: KEY,
    };
    await this.s3Service.storePublicJSONFile(s3ServiceProps);
  }
}
