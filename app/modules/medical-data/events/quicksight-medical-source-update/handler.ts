import { Context } from 'aws-lambda';
import { QuicksightMedicalSourceUpdateUseCase } from './use-case';
import { AnonymousMedicalDataRepository } from '../../repo/medical-data/anonymous-medical-data-repository';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { S3Service } from '../../../../shared/repos/file-repository';

class Lambda extends BaseHandler implements LambdaInterface {
  private quicksightMedicalSourceUpdateUseCase: QuicksightMedicalSourceUpdateUseCase;

  private init() {
    const anonymousMedicalDataRepository = new AnonymousMedicalDataRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const s3Service = new S3Service(
      new AWS.S3(),
      process.env.BUCKET_NAME || ''
    );
    this.quicksightMedicalSourceUpdateUseCase =
      new QuicksightMedicalSourceUpdateUseCase(
        anonymousMedicalDataRepository,
        s3Service
      );
  }

  public async handler(_context: Context) {
    try {
      this.init();
      await this.quicksightMedicalSourceUpdateUseCase.execute();

      return this.ok('done');
    } catch (error) {
      return this.fail(error as Error);
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
