import { Context, S3Event, S3EventRecord } from 'aws-lambda';
import { UpdateMedicalDataUseCase } from './use-case';
import { AnonymousMedicalDataRepository } from '../../repo/medical-data/anonymous-medical-data-repository';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { S3EventDTO } from './dto';
import AWS from 'aws-sdk';
import { S3Service } from '../../../../shared/repos/file-repository';
import { UserMedicalDataRepository } from '../../repo/medical-data/user-medical-data-repository';
import { NotificationByCountryUseCase } from '../../use-cases/notification-by-country/use-case';
import { GetUserListByCountryUseCase } from '../../../user/use-cases/get-users-by-country/use-case';
import { UserRepository } from '../../../user/repo/user-repository';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private updateMedicalDataUseCase: UpdateMedicalDataUseCase;

  private s3Service: S3Service;

  private init() {
    this.s3Service = new S3Service(new AWS.S3(), process.env.TABLE_NAME || '');
    const anonymousMedicalDataRepository = new AnonymousMedicalDataRepository(
      new AWS.DynamoDB(),
      process.env.ANONYMOUS_MEDICAL_TABLE_NAME || ''
    );
    const userMedicalDataRepository = new UserMedicalDataRepository(
      new AWS.DynamoDB(),
      process.env.USER_MEDICAL_TABLE_NAME || ''
    );
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.USER_TABLE_NAME || ''
    );
    const getUserListByCountryUseCase = new GetUserListByCountryUseCase(
      userRepository
    );
    const eventService = new EventService(
      process.env.SQS_URL || '',
      'notify-user-new-lab-results'
    );
    const notificationByCountryUseCase = new NotificationByCountryUseCase(
      getUserListByCountryUseCase,
      eventService
    );
    this.updateMedicalDataUseCase = new UpdateMedicalDataUseCase(
      process.env.COUNTRY || '-',
      anonymousMedicalDataRepository,
      userMedicalDataRepository,
      notificationByCountryUseCase
    );
  }

  @LogHandlerEvent
  public async handler(event: S3Event, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      for (const event of dto) {
        try {
          const file = await this.getFileFromBucket(event);
          if (!file) return;
          await this.updateMedicalDataUseCase.execute({
            file,
          });
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log('Error processing file', error);
        }
      }

      return this.ok('done');
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: S3Event): S3EventDTO[] {
    const messages: S3EventDTO[] = event.Records.map(
      (record: S3EventRecord) => {
        const { bucket, object } = record.s3;

        return {
          bucket: bucket.name,
          key: object.key,
        };
      }
    );

    return messages;
  }

  private async getFileFromBucket(dto: S3EventDTO) {
    try {
      // eslint-disable-next-line no-console
      console.log('file to get: ', JSON.stringify(dto));
      const file = await this.s3Service.getFileFromS3Bucket(dto);
      // eslint-disable-next-line no-console
      console.log('file: ', JSON.stringify(file));
      if (file.status === 'fail') throw 'Error reading file';

      return file.file;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Error reading file', error);

      return undefined;
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
