import { UpdateMedicalDataDTO } from './dto';
import { UseCase } from '../../../../shared/core/use-case';
import { AnonymousMedicalDataRepository } from '../../repo/medical-data/anonymous-medical-data-repository';
import { AnonymousMedicalData } from '../../domain/medical-data/AnonymousMedicalData';

import { UserMedicalData } from '../../domain/medical-data/UserMedicalData';
import { UserMedicalDataRepository } from '../../repo/medical-data/user-medical-data-repository';
import { NotificationByCountryUseCase } from '../../use-cases/notification-by-country/use-case';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { MedicalDataService } from '../../services/medical-data-services';
import { HealthProviderMedicalFile } from '../../services/type';
import { XlsxService } from '../../../../shared/services/xlsx-service';
import { S3 } from 'aws-sdk';

export class UpdateMedicalDataUseCase
  implements UseCase<UpdateMedicalDataDTO, Promise<void>>
{
  constructor(
    private country: string,
    private anonymousMedicalDataRepository: AnonymousMedicalDataRepository,
    private userMedicalDataRepository: UserMedicalDataRepository,
    private notificationByCountryUseCase: NotificationByCountryUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: UpdateMedicalDataDTO) {
    const healthProviderMedicalFile = this.mapExcelToJSON(dto.file);
    await this.anonymousDataStorage(healthProviderMedicalFile);
    await this.userDataStorage(healthProviderMedicalFile);
    await this.sendBatchNotificationsToUsers();
  }

  private mapExcelToJSON(file: S3.Body) {
    const healthProviderMedicalFile = XlsxService.fromS3FileToJson(file);

    return MedicalDataService.filterJsonDataByFormatSchema(
      healthProviderMedicalFile
    );
  }

  private async anonymousDataStorage(
    healthProviderMedicalFile: HealthProviderMedicalFile[]
  ) {
    const medicalDataList = this.mapJSONToAnonymousMedicalData(
      healthProviderMedicalFile
    );
    await this.storeAnonymousDataInDatabase(medicalDataList);
  }

  private mapJSONToAnonymousMedicalData(raw: HealthProviderMedicalFile[]) {
    return MedicalDataService.mapJSONToAnonymousMedicalDataDomain(
      this.country,
      raw
    );
  }

  private async userDataStorage(
    healthProviderMedicalFile: HealthProviderMedicalFile[]
  ) {
    const medicalDataList = this.mapJSONToUserMedicalData(
      healthProviderMedicalFile
    );
    await this.storeDataInDatabase(medicalDataList);
  }

  private async storeAnonymousDataInDatabase(
    medicalDataList: AnonymousMedicalData[]
  ) {
    for (const medicalData of medicalDataList) {
      try {
        await this.anonymousMedicalDataRepository.createAnonymousMedicalDataRecord(
          medicalData
        );
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(
          'Error storing anonymous medical data',
          error,
          'medicalData: ',
          JSON.stringify(medicalData)
        );
      }
    }
  }

  private mapJSONToUserMedicalData(raw: HealthProviderMedicalFile[]) {
    return MedicalDataService.mapJSONToUserMedicalDataDomain(this.country, raw);
  }

  private async storeDataInDatabase(medicalDataList: UserMedicalData[]) {
    for (const medicalData of medicalDataList) {
      try {
        await this.userMedicalDataRepository.createUserMedicalDataRecord(
          medicalData
        );
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(
          'Error storing user medical data',
          error,
          'medicalData: ',
          JSON.stringify(medicalData)
        );
      }
    }
  }

  private async sendBatchNotificationsToUsers() {
    try {
      //TODO: Reactivate notification (deactivated do to business decision)
      // await this.notificationByCountryUseCase.execute({
      //   country: this.country,
      // });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Send notification Error', error);
    }
  }
}
