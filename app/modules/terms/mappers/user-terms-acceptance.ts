import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { StatusValueObject } from '../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { UserTermsAcceptance } from '../domain/UserTermsAcceptance';
import { UserTermsAcceptanceDTO } from '../dto/user-terms-acceptance-dto';

export class UserTermsAcceptanceMap {
  public static toDTO(
    userTermsAcceptance: UserTermsAcceptance
  ): UserTermsAcceptanceDTO {
    return {
      userId: userTermsAcceptance.userId.value,
      acceptedAt: userTermsAcceptance.acceptedAt.value,
      version: userTermsAcceptance.version.value,
      userTermsAcceptanceStatus:
        userTermsAcceptance.userTermsAcceptanceStatus.value,
      createdAt: userTermsAcceptance.createdAt.value,
      updatedAt: userTermsAcceptance.updatedAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): UserTermsAcceptance {
    const userId = UUIDValueObject.create('userId', raw.userId);
    const acceptedAt = DateValueObject.create('acceptedAt', raw.acceptedAt);
    const version = NumberValueObject.create('version', raw.version);
    const userTermsAcceptanceStatus = StatusValueObject.create(
      'userTermsAcceptanceStatus',
      raw.userTermsAcceptanceStatus
    );
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const updatedAt = DateValueObject.create('updatedAt', raw.updatedAt);
    const dtoCombine = Result.combine([
      userId,
      acceptedAt,
      version,
      userTermsAcceptanceStatus,
      createdAt,
      updatedAt,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const userTermsAcceptance = UserTermsAcceptance.create({
      userId: userId.getValue(),
      acceptedAt: acceptedAt.getValue(),
      version: version.getValue(),
      userTermsAcceptanceStatus: userTermsAcceptanceStatus.getValue(),
      createdAt: createdAt.getValue(),
      updatedAt: updatedAt.getValue(),
    });
    if (userTermsAcceptance.isFailure) {
      throw userTermsAcceptance.getErrorValue();
    }

    return userTermsAcceptance.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toPersistence(userTermsAcceptance: UserTermsAcceptance): any {
    return {
      userId: userTermsAcceptance.userId.value,
      acceptedAt: userTermsAcceptance.acceptedAt.value,
      version: userTermsAcceptance.version.value,
      userTermsAcceptanceStatus:
        userTermsAcceptance.userTermsAcceptanceStatus.value,
      createdAt: userTermsAcceptance.createdAt.value,
      updatedAt: userTermsAcceptance.updatedAt.value,
    };
  }
}
