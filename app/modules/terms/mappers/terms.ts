import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { SimpleEmptyTextValueObject } from '../../../shared/common-value-objects/simple-empty-text-value-object';
import { StatusValueObject } from '../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { Terms } from '../domain/Terms';
import { TermsDTO } from '../dto/terms-dto';

export class TermsMap {
  public static toDTO(terms: Terms): TermsDTO {
    return {
      id: terms.id.value,
      content: terms.content.value,
      version: terms.version.value,
      termsStatus: terms.termsStatus.value,
      createdAt: terms.createdAt.value,
      updatedAt: terms.updatedAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): Terms {
    const id = UUIDValueObject.create('id', raw.id);
    const content = SimpleEmptyTextValueObject.create(raw.content);
    const version = NumberValueObject.create('version', raw.version);
    const termsStatus = StatusValueObject.create(
      'termsStatus',
      raw.termsStatus
    );
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const updatedAt = DateValueObject.create('updatedAt', raw.updatedAt);
    const dtoCombine = Result.combine([
      id,
      content,
      version,
      termsStatus,
      createdAt,
      updatedAt,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const terms = Terms.create({
      id: id.getValue(),
      content: content.getValue(),
      version: version.getValue(),
      termsStatus: termsStatus.getValue(),
      createdAt: createdAt.getValue(),
      updatedAt: updatedAt.getValue(),
    });
    if (terms.isFailure) {
      throw terms.getErrorValue();
    }

    return terms.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toPersistence(terms: Terms): any {
    return {
      id: terms.id.value,
      content: terms.content.value,
      version: terms.version.value,
      termsStatus: terms.termsStatus.value,
      createdAt: terms.createdAt.value,
      updatedAt: terms.updatedAt.value,
    };
  }
}
