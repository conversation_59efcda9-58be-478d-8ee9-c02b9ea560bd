import AWS from 'aws-sdk';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { <PERSON>Handler } from '../../../../shared/infra/handler';
import { UserAcceptTermsUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { UserAcceptTermsDTO } from './dto';
import { HTTPRequestBodyValidator } from '../../../../shared/infra/http-request-body-validator';
import { UserTermsAcceptanceRepository } from '../../repo/user-terms-acceptance-repository';
import { UserMap } from '../../../user/mappers/user-map';
import { UserRepository } from '../../../user/repo/user-repository';
import { AcceptTermsUseCase } from '../../../user/use-cases/accept-terms/use-case';

class Lambda extends BaseHandler implements LambdaInterface {
  private userAcceptTermsUseCase: UserAcceptTermsUseCase;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.USER_TABLE_NAME || ''
    );
    const acceptTermsUseCase = new AcceptTermsUseCase(userRepository);
    const userTermsAcceptanceRepository = new UserTermsAcceptanceRepository(
      new AWS.DynamoDB(),
      process.env.USER_TERMS_ACCEPTANCE_TABLE_NAME || ''
    );
    this.userAcceptTermsUseCase = new UserAcceptTermsUseCase(
      userTermsAcceptanceRepository,
      acceptTermsUseCase
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.userAcceptTermsUseCase.execute(dto);

      return this.ok(UserMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): UserAcceptTermsDTO {
    const BODY_SCHEMA = ['userId', 'version'];

    return HTTPRequestBodyValidator<UserAcceptTermsDTO>(
      BODY_SCHEMA,
      event.body
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
