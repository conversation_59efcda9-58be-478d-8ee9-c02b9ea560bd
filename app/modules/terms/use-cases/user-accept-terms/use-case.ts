import { UseCase } from '../../../../shared/core/use-case';
import { UserAcceptTermsDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { Result } from '../../../../shared/core/Result';
import { UserTermsAcceptanceRepository } from '../../repo/user-terms-acceptance-repository';
import { User } from '../../../user/domain/User';
import { UserTermsAcceptance } from '../../domain/UserTermsAcceptance';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { UserTermsAcceptanceMap } from '../../mappers/user-terms-acceptance';
import { AcceptTermsUseCase } from '../../../user/use-cases/accept-terms/use-case';

export class UserAcceptTermsUseCase
  implements UseCase<UserAcceptTermsDTO, Promise<User>>
{
  constructor(
    private userTermsAcceptanceRepository: UserTermsAcceptanceRepository,
    private acceptTermsUseCase: AcceptTermsUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: UserAcceptTermsDTO) {
    const userTermsAcceptance = this.mapDTOtoUserTermsAcceptance(dto);
    await this.createAcceptanceLog(userTermsAcceptance);
    const userUpdated = await this.acceptTermsUseCase.execute({
      userId: userTermsAcceptance.userId.value,
    });

    return userUpdated;
  }

  private mapDTOtoUserTermsAcceptance(
    dto: UserAcceptTermsDTO
  ): UserTermsAcceptance {
    const userId = UUIDValueObject.create('userId', dto.userId);
    const version = NumberValueObject.create('version', dto.version);
    const dbCombined = Result.combine([userId, version]);
    if (dbCombined.isFailure) {
      throw dbCombined.getErrorValue();
    }

    return UserTermsAcceptanceMap.toDomain({
      ...dto,
      userTermsAcceptanceStatus: 'ACTIVE',
    });
  }

  private async createAcceptanceLog(
    userTermsAcceptance: UserTermsAcceptance
  ): Promise<UserTermsAcceptance> {
    return await this.userTermsAcceptanceRepository.create(userTermsAcceptance);
  }
}
