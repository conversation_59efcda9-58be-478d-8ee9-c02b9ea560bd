import { UseCase } from '../../../../shared/core/use-case';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { Result } from '../../../../shared/core/Result';
import { Terms } from '../../domain/Terms';
import { TermsRepository } from '../../repo/terms-repository';
import { StatusValueObject } from '../../../../shared/common-value-objects/status-value-object';

export class GetLatestTermsUseCase
  implements UseCase<void, Promise<Terms | null>>
{
  constructor(private termsRepository: TermsRepository) {}

  @LogUseCaseDTO
  public async execute() {
    const { status } = this.getQueryParams();

    return await this.getLatestUserTerms(status);
  }

  private getQueryParams() {
    const status = StatusValueObject.create('status', 'ACTIVE');
    const dbCombined = Result.combine([status]);
    if (dbCombined.isFailure) {
      throw dbCombined.getErrorValue();
    }

    return { status: status.getValue() };
  }

  private async getLatestUserTerms(
    status: StatusValueObject
  ): Promise<Terms | null> {
    const terms = await this.termsRepository.getTermsByStatus(status);

    return terms.length > 0 ? this.sortTermsByCreatedAt(terms)[0] : null;
  }

  private sortTermsByCreatedAt(terms: Terms[]) {
    return terms.sort(
      (
        { createdAt: { value: createdAtA } },
        { createdAt: { value: createdAtB } }
      ) => new Date(createdAtB).getTime() - new Date(createdAtA).getTime()
    );
  }
}
