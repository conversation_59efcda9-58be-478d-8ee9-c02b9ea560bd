import AWS from 'aws-sdk';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { GetLatestTermsUseCase } from './use-case';
import { TermsMap } from '../../mappers/terms';
import { TermsRepository } from '../../repo/terms-repository';

class Lambda extends BaseHandler implements LambdaInterface {
  private getLatestTermsUseCase: GetLatestTermsUseCase;

  private init() {
    const termsRepository = new TermsRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.getLatestTermsUseCase = new GetLatestTermsUseCase(termsRepository);
  }

  @LogHandlerEvent
  public async handler(_event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const result = await this.getLatestTermsUseCase.execute();

      return this.ok(result && TermsMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
