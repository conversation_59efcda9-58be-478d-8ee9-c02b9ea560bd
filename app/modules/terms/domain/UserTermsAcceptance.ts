import { Result } from '../../../shared/core/Result';
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { StatusValueObject } from '../../../shared/common-value-objects/status-value-object';

export interface IUserTermsAcceptanceProps {
  userId: UUIDValueObject;
  acceptedAt: DateValueObject;
  version: NumberValueObject;
  userTermsAcceptanceStatus: StatusValueObject;
  createdAt: DateValueObject;
  updatedAt: DateValueObject;
}
export class UserTermsAcceptance {
  private props: IUserTermsAcceptanceProps;

  constructor(props: IUserTermsAcceptanceProps) {
    this.props = props;
  }

  get userId(): UUIDValueObject {
    return this.props.userId;
  }

  get acceptedAt(): DateValueObject {
    return this.props.acceptedAt;
  }

  get version(): NumberValueObject {
    return this.props.version;
  }

  get userTermsAcceptanceStatus(): StatusValueObject {
    return this.props.userTermsAcceptanceStatus;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  public static create(props: IUserTermsAcceptanceProps) {
    const userTermsAcceptance = new UserTermsAcceptance(props);

    return Result.ok<UserTermsAcceptance>(userTermsAcceptance);
  }
}
