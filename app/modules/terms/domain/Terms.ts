import { Result } from '../../../shared/core/Result';
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { SimpleTextValueObject } from '../../../shared/common-value-objects/simple-text-value-object';
import { StatusValueObject } from '../../../shared/common-value-objects/status-value-object';

export interface ITermsProps {
  id: UUIDValueObject;
  content: SimpleTextValueObject;
  version: NumberValueObject;
  termsStatus: StatusValueObject;
  createdAt: DateValueObject;
  updatedAt: DateValueObject;
}
export class Terms {
  private props: ITermsProps;

  constructor(props: ITermsProps) {
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get content(): SimpleTextValueObject {
    return this.props.content;
  }

  get version(): NumberValueObject {
    return this.props.version;
  }

  get termsStatus(): StatusValueObject {
    return this.props.termsStatus;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  public static create(props: ITermsProps) {
    const terms = new Terms(props);

    return Result.ok<Terms>(terms);
  }
}
