import * as AWS from 'aws-sdk';
import { AttributeMap, QueryInput } from 'aws-sdk/clients/dynamodb';
import { DynamoServices } from '../../../shared/services/dynamo-services';
import { StatusValueObject } from '../../../shared/common-value-objects/status-value-object';
import { Terms } from '../domain/Terms';
import { TermsMap } from '../mappers/terms';

export class TermsRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async getTermsByStatus(
    termsStatus: StatusValueObject
  ): Promise<Terms[]> {
    const status = termsStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'termsStatus-createdAt-index',
      KeyConditionExpression: 'termsStatus = :termsStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':termsStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => TermsMap.toDomain(item));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
