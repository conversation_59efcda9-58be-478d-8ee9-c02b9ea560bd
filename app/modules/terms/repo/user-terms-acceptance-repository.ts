import * as AWS from 'aws-sdk';
import { AttributeMap, PutItemInput } from 'aws-sdk/clients/dynamodb';
import { UserTermsAcceptance } from '../domain/UserTermsAcceptance';
import { UserTermsAcceptanceMap } from '../mappers/user-terms-acceptance';

export class UserTermsAcceptanceRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async create(
    userTermsAcceptance: UserTermsAcceptance
  ): Promise<UserTermsAcceptance> {
    const rawUserTermsAcceptanceMap =
      UserTermsAcceptanceMap.toPersistence(userTermsAcceptance);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(rawUserTermsAcceptanceMap),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return userTermsAcceptance;
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
