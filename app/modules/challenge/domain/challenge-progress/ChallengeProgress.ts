import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../../shared/common-value-objects/simple-text-value-object';
import { StatusValueObject } from '../../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { Evidence } from './evidence';
import { ChallengeProgressType } from './type';

interface IChallengeProgressProps {
  id: UUIDValueObject;
  challengeId: UUIDValueObject;
  userId: UUIDValueObject;
  userName: SimpleTextValueObject;
  value: NumberValueObject;
  evidence: Evidence;
  challengeProgressStatus: StatusValueObject;
  challengeProgressType: ChallengeProgressType;
  updatedAt: DateValueObject;
  createdAt: DateValueObject;
}
export class ChallengeProgress {
  private props: IChallengeProgressProps;

  constructor(props: IChallengeProgressProps) {
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get challengeId(): UUIDValueObject {
    return this.props.challengeId;
  }

  get userId(): UUIDValueObject {
    return this.props.userId;
  }

  get userName(): SimpleTextValueObject {
    return this.props.userName;
  }

  get value(): NumberValueObject {
    return this.props.value;
  }

  get evidence(): Evidence {
    return this.props.evidence;
  }

  get challengeProgressStatus(): StatusValueObject {
    return this.props.challengeProgressStatus;
  }

  get challengeProgressType(): ChallengeProgressType {
    return this.props.challengeProgressType;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  public archive() {
    const status = StatusValueObject.create(
      'challengeProgressStatus',
      'ARCHIVED'
    );
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.challengeProgressStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public static create(props: IChallengeProgressProps) {
    const challengeProgress = new ChallengeProgress({ ...props });

    return Result.ok<ChallengeProgress>(challengeProgress);
  }
}
