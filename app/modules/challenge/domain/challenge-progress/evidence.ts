import { Guard } from '../../../../shared/core/Guard';
import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

interface IEvidenceProps {
  value: string;
}
export class Evidence extends ValueObject<IEvidenceProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IEvidenceProps) {
    super(props);
  }

  public static create(argumentName: string, text: string): Result<Evidence> {
    const textValidation = Guard.againstNullOrUndefined(text, argumentName);
    if (textValidation.isFailure) {
      return Result.ok<Evidence>(new Evidence({ value: '' }));
    }

    return Result.ok<Evidence>(new Evidence({ value: text }));
  }
}
