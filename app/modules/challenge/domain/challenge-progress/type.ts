import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const PROGRESS_TYPE_OPTIONS = ['HABIT', 'NUMERIC'];
interface IChallengeProgressTypeProps {
  value: string;
}
export class ChallengeProgressType extends ValueObject<IChallengeProgressTypeProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IChallengeProgressTypeProps) {
    super(props);
  }

  private static isStatusValid(elementStatus: string) {
    if (!PROGRESS_TYPE_OPTIONS.includes(elementStatus)) return false;

    return true;
  }

  public static create(progressType: string): Result<ChallengeProgressType> {
    if (!progressType || !this.isStatusValid(progressType)) {
      return Result.fail<ChallengeProgressType>(
        `Invalid Challenge Progress Type: ${progressType}`
      );
    }

    return Result.ok<ChallengeProgressType>(
      new ChallengeProgressType({ value: progressType })
    );
  }
}
