import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const CHALLENGE_OPTIONS = ['INVITED', 'ACCEPTED', 'ARCHIVED'];
interface IParticipantStatusProps {
  value: string;
}
export class ParticipantStatus extends ValueObject<IParticipantStatusProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IParticipantStatusProps) {
    super(props);
  }

  private static isStatusValid(elementStatus: string) {
    if (!CHALLENGE_OPTIONS.includes(elementStatus)) return false;

    return true;
  }

  public static create(status: string): Result<ParticipantStatus> {
    if (!this.isStatusValid(status)) {
      return Result.fail<ParticipantStatus>(
        `Invalid Challenge Type: ${status}`
      );
    }

    return Result.ok<ParticipantStatus>(
      new ParticipantStatus({ value: status })
    );
  }
}
