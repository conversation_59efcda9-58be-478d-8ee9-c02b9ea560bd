import { BooleanValueObject } from '../../../../shared/common-value-objects/boolean-value-object';
import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../../shared/common-value-objects/simple-text-value-object';
import { StatusValueObject } from '../../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { ParticipantStatus } from './participant-status';

interface IParticipantProps {
  id: UUIDValueObject;
  userId: UUIDValueObject;
  challengeId: UUIDValueObject;
  userName: SimpleTextValueObject;
  userEmail: SimpleTextValueObject;
  initialValue: NumberValueObject;
  participantStatus: ParticipantStatus;
  isWinner: BooleanValueObject;
  updatedAt: DateValueObject;
  createdAt: DateValueObject;
}
export class Participant {
  private props: IParticipantProps;

  constructor(props: IParticipantProps) {
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get userId(): UUIDValueObject {
    return this.props.userId;
  }

  get challengeId(): UUIDValueObject {
    return this.props.challengeId;
  }

  get userName(): SimpleTextValueObject {
    return this.props.userName;
  }

  get userEmail(): SimpleTextValueObject {
    return this.props.userEmail;
  }

  get initialValue(): NumberValueObject {
    return this.props.initialValue;
  }

  get participantStatus(): ParticipantStatus {
    return this.props.participantStatus;
  }

  get isWinner(): BooleanValueObject {
    return this.props.isWinner;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  public updateStatusAccept() {
    const status = ParticipantStatus.create('ACCEPTED');
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.participantStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public updateStatusReject() {
    const status = ParticipantStatus.create('ARCHIVED');
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.participantStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public setIsWinner(isParticipantWinner: boolean) {
    const isWinner = BooleanValueObject.create('isWinner', isParticipantWinner);
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([isWinner, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.isWinner = isWinner.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public updateInitialValue(participantInitialValue: number) {
    const initialValue = NumberValueObject.create(
      'initialValue',
      participantInitialValue
    );
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([initialValue, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.initialValue = initialValue.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public archive() {
    const status = StatusValueObject.create('participantStatus', 'ARCHIVED');
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.participantStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public static create(props: IParticipantProps) {
    const participant = new Participant({ ...props });

    return Result.ok<Participant>(participant);
  }
}
