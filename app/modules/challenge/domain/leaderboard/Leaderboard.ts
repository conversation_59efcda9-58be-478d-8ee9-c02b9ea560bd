import { Result } from '../../../../shared/core/Result';
import { ChallengeProgress } from '../challenge-progress/ChallengeProgress';
import { Challenge } from '../challenge/Challenge';
import { Participant } from '../participant/Participant';
import { LeaderboardParticipant } from './LeaderboardParticipant';

export interface ILeaderboardProps {
  winner: LeaderboardParticipant | undefined;
  leaderboard: LeaderboardParticipant[];
}
export class Leaderboard {
  private props: ILeaderboardProps;

  constructor(props: ILeaderboardProps) {
    this.props = props;
  }

  get winner(): LeaderboardParticipant | undefined {
    return this.props.winner;
  }

  get leaderboard(): LeaderboardParticipant[] {
    return this.props.leaderboard;
  }

  public calculateLeaderBoardProgress(
    participantList: Participant[],
    challengeProgress: ChallengeProgress[],
    challenge: Challenge
  ) {
    this.props.leaderboard = this.props.leaderboard
      .map((leaderboardParticipant) => {
        leaderboardParticipant.calculateProgress(
          participantList,
          challengeProgress,
          challenge
        );

        return leaderboardParticipant;
      })
      .slice()
      .sort((a, b) => b.overallProgress.value - a.overallProgress.value);
    this.setWinner();
  }

  private setWinner() {
    const winner =
      this.props.leaderboard.find(
        (participan) => participan.isWinner.value === true
      ) || this.props.leaderboard[0];
    this.props.winner = winner;
  }

  public static create(props: ILeaderboardProps) {
    const challenge = new Leaderboard({ ...props });

    return Result.ok<Leaderboard>(challenge);
  }
}
