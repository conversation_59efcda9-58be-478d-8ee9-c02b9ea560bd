//TODO: refactor this class, it is not a domain class, it is a service class
import { BooleanValueObject } from '../../../../shared/common-value-objects/boolean-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../../shared/common-value-objects/simple-text-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { ChallengeProgress } from '../challenge-progress/ChallengeProgress';
import { Challenge } from '../challenge/Challenge';
import { Participant } from '../participant/Participant';

export interface ILeaderboardParticipantProps {
  userId: UUIDValueObject;
  userName: SimpleTextValueObject;
  userEmail: SimpleTextValueObject;
  initialProgress: NumberValueObject;
  latestProgress: NumberValueObject;
  overallProgress: NumberValueObject;
  hasAchievedGoal: BooleanValueObject;
  isWinner: BooleanValueObject;
}
export class LeaderboardParticipant {
  private props: ILeaderboardParticipantProps;

  constructor(props: ILeaderboardParticipantProps) {
    this.props = props;
  }

  get userId(): UUIDValueObject {
    return this.props.userId;
  }

  get userName(): SimpleTextValueObject {
    return this.props.userName;
  }

  get userEmail(): SimpleTextValueObject {
    return this.props.userEmail;
  }

  get initialProgress(): NumberValueObject {
    return this.props.initialProgress;
  }

  get latestProgress(): NumberValueObject {
    return this.props.latestProgress;
  }

  get overallProgress(): NumberValueObject {
    return this.props.overallProgress;
  }

  get hasAchievedGoal(): BooleanValueObject {
    return this.props.hasAchievedGoal;
  }

  get isWinner(): BooleanValueObject {
    return this.props.isWinner;
  }

  public calculateProgress(
    participantList: Participant[],
    challengeProgress: ChallengeProgress[],
    challenge: Challenge
  ) {
    const participant = participantList.find(
      (participant) => participant.userId.value === this.userId.value
    );
    if (!participant) return;
    const userProgressList = challengeProgress
      .filter((progress) => progress.userId.value === this.userId.value)
      .slice()
      .sort(
        (a, b) =>
          new Date(b.updatedAt.value).getTime() -
          new Date(a.updatedAt.value).getTime()
      );
    this.setInitialProgress(participant);
    this.setLatestProgress(userProgressList, challenge);
    this.setOverallProgress(challenge);
    this.setHasAchievedGoal(challenge);
  }

  private setInitialProgress(participant: Participant) {
    this.props.initialProgress = participant.initialValue;
  }

  private setLatestProgress(
    userProgressList: ChallengeProgress[],
    challenge: Challenge
  ) {
    const {
      progressType: { value: progressType },
    } = challenge;
    if (progressType === 'ACCUMULATIVE') {
      const accumulatedValue = userProgressList
        .map((userProgress) => userProgress.value.value)
        .reduce(
          (previousValue, currentValue) => previousValue + currentValue,
          0
        );
      const latestProgress = NumberValueObject.create(
        'latestProgress',
        accumulatedValue
      );
      if (latestProgress.isFailure) throw latestProgress.getErrorValue();
      this.props.latestProgress = latestProgress.getValue();
    }
    if (progressType === 'FINAL_RESULT') {
      const latestValue =
        userProgressList && userProgressList.length > 0
          ? userProgressList[0].value.value
          : 0;
      const latestProgress = NumberValueObject.create(
        'latestProgress',
        latestValue
      );
      if (latestProgress.isFailure) throw latestProgress.getErrorValue();
      this.props.latestProgress = latestProgress.getValue();
    }
    if (progressType === 'HABIT') {
      const latestValue =
        this.filterByUniqueDates(userProgressList).length || 0;
      const latestProgress = NumberValueObject.create(
        'latestProgress',
        latestValue
      );
      if (latestProgress.isFailure) throw latestProgress.getErrorValue();
      this.props.latestProgress = latestProgress.getValue();
    }
  }

  private setOverallProgress(challenge: Challenge) {
    const {
      goal: { value: goal },
      goalType: { value: goalType },
      progressType: { value: progressType },
    } = challenge;
    let overallProgressValue = 0;
    if (progressType === 'FINAL_RESULT') {
      overallProgressValue = this.calculateFinalResultProgress(goalType, goal);
    }
    if (progressType === 'ACCUMULATIVE') {
      overallProgressValue = this.calculateAccumulativeProgress();
    }
    if (progressType === 'HABIT') {
      overallProgressValue = this.calculateAccumulativeProgress();
    }
    const overallProgress = NumberValueObject.create(
      'overallProgress',
      overallProgressValue
    );
    if (overallProgress.isFailure) throw overallProgress.getErrorValue();
    this.props.overallProgress = overallProgress.getValue();
  }

  private calculateAccumulativeProgress() {
    const initialValue = 0;

    return this.nonNegativeNumber(this.latestProgress.value - initialValue);
  }

  private calculateFinalResultProgress(goalType: string, goal: number) {
    if (this.latestProgress.value === 0) return 0;
    if (goalType === 'ADD') {
      const standardizedInitialValue =
        this.initialProgress.value > goal ? goal : this.initialProgress.value;

      return this.nonNegativeNumber(
        this.latestProgress.value - standardizedInitialValue
      );
    }
    if (goalType === 'REDUCE') {
      const standardizedInitialValue =
        this.initialProgress.value < goal ? goal : this.initialProgress.value;

      return this.nonNegativeNumber(
        standardizedInitialValue - this.latestProgress.value
      );
    }

    return 0;
  }

  private setHasAchievedGoal(challenge: Challenge) {
    const {
      goal: { value: goal },
      goalType: { value: goalType },
      progressType: { value: progressType },
      startDate: { value: startDate },
      endDate: { value: endDate },
    } = challenge;
    let isAchieved = false;
    if (progressType === 'HABIT') {
      const daysBetween = this.calculateDaysBetweenDates({
        startDate,
        endDate,
      });
      isAchieved = this.latestProgress.value >= daysBetween;
    } else if (progressType === 'ACCUMULATIVE' || goalType === 'ADD') {
      isAchieved = this.checkAddGoal(this.latestProgress.value, goal);
    } else if (goalType === 'REDUCE') {
      isAchieved = this.checkReduceGoal(this.latestProgress.value, goal);
    }
    const hasAchievedGoal = BooleanValueObject.create(
      'hasAchievedGoal',
      isAchieved
    );
    if (hasAchievedGoal.isFailure) throw hasAchievedGoal.getErrorValue();
    this.props.hasAchievedGoal = hasAchievedGoal.getValue();
  }

  private filterByUniqueDates(
    challenges: ChallengeProgress[]
  ): ChallengeProgress[] {
    return challenges.filter((challenge, index, self) => {
      const date = new Date(challenge.createdAt.value).toDateString();

      return (
        self.findIndex(
          (item) => new Date(item.createdAt.value).toDateString() === date
        ) === index
      );
    });
  }

  private calculateDaysBetweenDates({
    startDate,
    endDate,
  }: {
    startDate: string;
    endDate: string;
  }): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const oneDayInMilliseconds = 1000 * 60 * 60 * 24;
    const differenceInMilliseconds = Math.abs(end.getTime() - start.getTime());

    return Math.ceil(differenceInMilliseconds / oneDayInMilliseconds);
  }

  private checkAddGoal = (progress: number, goal: number) => progress >= goal;

  private checkReduceGoal = (progress: number, goal: number) =>
    progress <= goal;

  private nonNegativeNumber(input: number): number {
    return input < 0 ? 0 : input;
  }

  public static create(props: ILeaderboardParticipantProps) {
    const challenge = new LeaderboardParticipant({ ...props });

    return Result.ok<LeaderboardParticipant>(challenge);
  }
}
