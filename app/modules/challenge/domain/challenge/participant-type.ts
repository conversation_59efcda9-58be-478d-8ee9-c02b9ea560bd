import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const PARTICIPANT_OPTIONS = ['INDIVIDUAL', 'GROUP'];
interface IChallengesParticipantTypeProps {
  value: string;
}
export class ChallengesParticipantType extends ValueObject<IChallengesParticipantTypeProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IChallengesParticipantTypeProps) {
    super(props);
  }

  private static isStatusValid(elementStatus: string) {
    if (!PARTICIPANT_OPTIONS.includes(elementStatus)) return false;

    return true;
  }

  public static create(
    challengeParticipantType: string
  ): Result<ChallengesParticipantType> {
    if (!this.isStatusValid(challengeParticipantType)) {
      return Result.fail<ChallengesParticipantType>(
        `Invalid Challenges Participant Type: ${challengeParticipantType}`
      );
    }

    return Result.ok<ChallengesParticipantType>(
      new ChallengesParticipantType({ value: challengeParticipantType })
    );
  }
}
