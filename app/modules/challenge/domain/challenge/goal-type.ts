import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const GOAL_OPTIONS = ['ADD', 'REDUCE'];
interface IGoalTypeProps {
  value: string;
}
export class GoalType extends ValueObject<IGoalTypeProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IGoalTypeProps) {
    super(props);
  }

  private static isStatusValid(elementStatus: string) {
    if (!GOAL_OPTIONS.includes(elementStatus)) return false;

    return true;
  }

  public static create(status: string): Result<GoalType> {
    if (!this.isStatusValid(status)) {
      return Result.fail<GoalType>(`Invalid Goal Type: ${status}`);
    }

    return Result.ok<GoalType>(new GoalType({ value: status }));
  }
}
