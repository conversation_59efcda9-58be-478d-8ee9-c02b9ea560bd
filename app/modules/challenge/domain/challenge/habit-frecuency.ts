import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const OPTIONS = ['ACCUMULATIVE', 'FINAL_RESULT', 'HABIT'];
interface IProgressTypeProps {
  value: string;
}
export class ProgressType extends ValueObject<IProgressTypeProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IProgressTypeProps) {
    super(props);
  }

  private static isStatusProgressType(type: string | undefined) {
    if (!type || !OPTIONS.includes(type)) return false;

    return true;
  }

  public static create(progressType: string): Result<ProgressType> {
    if (!this.isStatusProgressType(progressType)) {
      return Result.fail<ProgressType>(
        `Invalid Reminder Type: ${progressType}`
      );
    }

    return Result.ok<ProgressType>(new ProgressType({ value: progressType }));
  }
}
