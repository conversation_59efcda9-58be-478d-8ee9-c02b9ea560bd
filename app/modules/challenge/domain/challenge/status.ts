import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const STATUS_OPTIONS = ['ACTIVE', 'COMPLETED', 'ARCHIVED'];
interface IChallengeStatusProps {
  value: string;
}
export class ChallengeStatus extends ValueObject<IChallengeStatusProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IChallengeStatusProps) {
    super(props);
  }

  private static isStatusValid(elementStatus: string) {
    if (!STATUS_OPTIONS.includes(elementStatus)) return false;

    return true;
  }

  public static create(status: string): Result<ChallengeStatus> {
    if (!this.isStatusValid(status)) {
      return Result.fail<ChallengeStatus>(
        `Invalid Challenge Status: ${status}`
      );
    }

    return Result.ok<ChallengeStatus>(new ChallengeStatus({ value: status }));
  }
}
