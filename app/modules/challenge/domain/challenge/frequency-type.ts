import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const FREQUENCY_OPTIONS = ['DAILY', 'WEEKLY', 'MONTHLY'];
interface IFrequencyTypeProps {
  value: string;
}
export class FrequencyType extends ValueObject<IFrequencyTypeProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IFrequencyTypeProps) {
    super(props);
  }

  private static isStatusValid(elementStatus: string) {
    if (!FREQUENCY_OPTIONS.includes(elementStatus)) return false;

    return true;
  }

  public static create(frequencyType?: string): Result<FrequencyType> {
    if (!frequencyType)
      return Result.ok<FrequencyType>(new FrequencyType({ value: '' }));
    if (!this.isStatusValid(frequencyType)) {
      return Result.fail<FrequencyType>(
        `Invalid Reminder Type: ${frequencyType}`
      );
    }

    return Result.ok<FrequencyType>(
      new FrequencyType({ value: frequencyType })
    );
  }
}
