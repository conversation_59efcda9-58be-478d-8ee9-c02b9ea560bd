import { Result } from '../../../../shared/core/Result';
import { ValueObject } from '../../../../shared/domain/ValueObject';

const GOAL_UNIT_OPTIONS = [
  'KG',
  'LB',
  'KCAL',
  'STEPS',
  'DAYS',
  'WEEKS',
  'HOURS',
  'MINUTES',
  'ML',
  'UNITS',
];
interface IGoalUnitProps {
  value: string;
}
export class GoalUnit extends ValueObject<IGoalUnitProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IGoalUnitProps) {
    super(props);
  }

  private static isStatusValid(elementStatus: string) {
    if (!GOAL_UNIT_OPTIONS.includes(elementStatus)) return false;

    return true;
  }

  public static create(status: string): Result<GoalUnit> {
    if (!this.isStatusValid(status)) {
      return Result.fail<GoalUnit>(`Invalid Goal Unit: ${status}`);
    }

    return Result.ok<GoalUnit>(new GoalUnit({ value: status }));
  }
}
