import { BooleanValueObject } from '../../../../shared/common-value-objects/boolean-value-object';
import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../../shared/common-value-objects/simple-text-value-object';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../shared/core/Result';
import { ProgressType } from './progress-type';
import { ChallengeStatus } from './status';
import { ChallengesParticipantType } from './participant-type';
import { GoalType } from './goal-type';
import { GoalUnit } from './goal-unit';
import { FrequencyType } from './frequency-type';

export interface IChallengeProps {
  id: UUIDValueObject;
  userId: UUIDValueObject;
  name: SimpleTextValueObject;
  description: SimpleTextValueObject;
  reward: SimpleTextValueObject;
  initialValue: NumberValueObject;
  startDate: DateValueObject;
  endDate: DateValueObject;
  goal: NumberValueObject;
  goalUnit: GoalUnit;
  goalType: GoalType;
  isRemainderActive: BooleanValueObject;
  isEvidenceRequired: BooleanValueObject;
  remainderType: FrequencyType;
  progressType: ProgressType;
  challengeStatus: ChallengeStatus;
  challengeParticipantType: ChallengesParticipantType;
  updatedAt: DateValueObject;
  createdAt: DateValueObject;
}
export class Challenge {
  private props: IChallengeProps;

  constructor(props: IChallengeProps) {
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get userId(): UUIDValueObject {
    return this.props.userId;
  }

  get name(): SimpleTextValueObject {
    return this.props.name;
  }

  get description(): SimpleTextValueObject {
    return this.props.description;
  }

  get initialValue(): NumberValueObject {
    return this.props.initialValue;
  }

  get startDate(): DateValueObject {
    return this.props.startDate;
  }

  get endDate(): DateValueObject {
    return this.props.endDate;
  }

  get goal(): NumberValueObject {
    return this.props.goal;
  }

  get goalUnit(): GoalUnit {
    return this.props.goalUnit;
  }

  get goalType(): GoalType {
    return this.props.goalType;
  }

  get isRemainderActive(): BooleanValueObject {
    return this.props.isRemainderActive;
  }

  get isEvidenceRequired(): BooleanValueObject {
    return this.props.isEvidenceRequired;
  }

  get remainderType(): FrequencyType {
    return this.props.remainderType;
  }

  get progressType(): ProgressType {
    return this.props.progressType;
  }

  get reward(): SimpleTextValueObject {
    return this.props.reward;
  }

  get challengeStatus(): ChallengeStatus {
    return this.props.challengeStatus;
  }

  get challengeParticipantType(): ChallengesParticipantType {
    return this.props.challengeParticipantType;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  public update({
    name,
    description,
    reward,
    initialValue,
    startDate,
    endDate,
    goal,
    goalUnit,
    goalType,
    isRemainderActive,
    isEvidenceRequired,
    remainderType,
    progressType,
  }: {
    name: string;
    description: string;
    reward: string;
    initialValue: number;
    startDate: string;
    endDate: string;
    goal: number;
    goalUnit: string;
    goalType: string;
    isRemainderActive: boolean;
    isEvidenceRequired: boolean;
    remainderType?: string;
    progressType: string;
  }) {
    const updatedName = SimpleTextValueObject.create('name', name);
    const updatedDescription = SimpleTextValueObject.create(
      'description',
      description
    );
    const updatedReward = SimpleTextValueObject.create('reward', reward);
    const updatedInitialValue = NumberValueObject.create(
      'initialValue',
      initialValue
    );
    const updatedStartDate = DateValueObject.create('startDate', startDate);
    const updatedEndDate = DateValueObject.create('endDate', endDate);
    const updatedGoal = NumberValueObject.create('goal', goal);
    const updatedGoalUnit = GoalUnit.create(goalUnit);
    const updatedGoalType = GoalType.create(goalType);
    const updatedIsRemainderActive = BooleanValueObject.create(
      'isRemainderActive',
      isRemainderActive
    );
    const updatedIsEvidenceRequired = BooleanValueObject.create(
      'isEvidenceRequired',
      isEvidenceRequired
    );
    const updatedRemainderType =
      remainderType && FrequencyType.create(remainderType);
    const updatedProgressType = ProgressType.create(progressType);
    const dbCombine = Result.combine([
      updatedName,
      updatedDescription,
      updatedReward,
      updatedInitialValue,
      updatedStartDate,
      updatedEndDate,
      updatedGoal,
      updatedGoalUnit,
      updatedGoalType,
      updatedIsRemainderActive,
      updatedIsEvidenceRequired,
      updatedProgressType,
    ]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();
    this.props.name = updatedName.getValue();
    this.props.description = updatedDescription.getValue();
    this.props.reward = updatedReward.getValue();
    this.props.initialValue = updatedInitialValue.getValue();
    this.props.startDate = updatedStartDate.getValue();
    this.props.endDate = updatedEndDate.getValue();
    this.props.goal = updatedGoal.getValue();
    this.props.goalUnit = updatedGoalUnit.getValue();
    this.props.goalType = updatedGoalType.getValue();
    this.props.isRemainderActive = updatedIsRemainderActive.getValue();
    if (updatedRemainderType)
      this.props.remainderType = updatedRemainderType.getValue();
    this.props.isEvidenceRequired = updatedIsEvidenceRequired.getValue();
    this.props.progressType = updatedProgressType.getValue();
  }

  public archive() {
    const status = ChallengeStatus.create('ARCHIVED');
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.challengeStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public close() {
    const status = ChallengeStatus.create('CLOSED');
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.challengeStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public complete() {
    const status = ChallengeStatus.create('COMPLETED');
    const updatedAt = DateValueObject.create('updatedAt');
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.challengeStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public static create(props: IChallengeProps) {
    const challenge = new Challenge({ ...props });

    return Result.ok<Challenge>(challenge);
  }
}
