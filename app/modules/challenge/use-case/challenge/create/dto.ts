export type CreateChallengeDTO = {
  userId: string;
  name: string;
  description: string;
  reward: string;
  initialValue: number;
  goalType: string;
  goal: number;
  goalUnit: string;
  startDate: string;
  endDate: string;
  isRemainderActive: boolean;
  remainderType?: string;
  progressType: string;
  participants: ParticipantList;
  challengeParticipantType: string;
};
type ParticipantList = Participant[];
type Participant = {
  id: string;
  name: string;
  email: string;
};
