import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { CreateChallengeDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { ChallengeMap } from '../../../mappers/challenge-map';
import { CreateChallengeUseCase } from './use-case';
import { CreateParticipantUseCase } from '../../participant/create/use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { HTTPRequestBodyValidator } from '../../../../../shared/infra/http-request-body-validator';
import { EventService } from '../../../../../shared/services/publish-sqs-event-service';

class Lambda extends BaseHandler implements LambdaInterface {
  private createChallengeUseCase: CreateChallengeUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const createParticipantUseCase = new CreateParticipantUseCase(
      participantRepository
    );
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    const eventService = new EventService(
      process.env.SQS_URL || '',
      'notify-participant'
    );
    this.createChallengeUseCase = new CreateChallengeUseCase(
      challengeRepository,
      createParticipantUseCase,
      eventService
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.createChallengeUseCase.execute(dto);

      return this.ok(ChallengeMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): CreateChallengeDTO {
    const BODY_SCHEMA = [
      'userId',
      'name',
      'description',
      'reward',
      'initialValue',
      'goalType',
      'goal',
      'goalUnit',
      'startDate',
      'endDate',
      'isRemainderActive',
      'progressType',
      'participants',
      'challengeParticipantType',
    ];

    return HTTPRequestBodyValidator<CreateChallengeDTO>(
      BODY_SCHEMA,
      event.body
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
