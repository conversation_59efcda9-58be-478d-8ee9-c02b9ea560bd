import { UseCase } from '../../../../../shared/core/use-case';
import { EventService } from '../../../../../shared/services/publish-sqs-event-service';
import { Challenge } from '../../../domain/challenge/Challenge';
import { ChallengeMap } from '../../../mappers/challenge-map';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { CreateParticipantUseCase } from '../../participant/create/use-case';
import { CreateChallengeDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class CreateChallengeUseCase
  implements UseCase<CreateChallengeDTO, Promise<Challenge>>
{
  constructor(
    private challengeRepository: ChallengeRepository,
    private createParticipantUseCase: CreateParticipantUseCase,
    private eventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute(dto: CreateChallengeDTO) {
    const challenge = this.mapDTOtoChallenge(dto);
    const challengeCreated = await this.createChallenge(challenge);
    await this.createAddParticipants(dto, challenge, challengeCreated);

    return challengeCreated;
  }

  private mapDTOtoChallenge(dto: CreateChallengeDTO) {
    const challenge = ChallengeMap.toDomain({
      ...dto,
      challengeStatus: 'ACTIVE',
    });

    return challenge;
  }

  private async createChallenge(challenge: Challenge): Promise<Challenge> {
    const challengeCreated = await this.challengeRepository.createChallenge(
      challenge
    );
    if (!challengeCreated) throw 'Error Creating Challenge';

    return challengeCreated;
  }

  private async createAddParticipants(
    dto: CreateChallengeDTO,
    challenge: Challenge,
    challengeCreated: Challenge
  ) {
    for (const participant of dto.participants) {
      const dto = {
        userId: participant.id,
        challengeId: challengeCreated.id.value,
        userName: participant.name,
        userEmail: participant.email,
        initialValue: 0,
        ...(participant.id === challenge.userId.value && {
          participantStatus: 'ACCEPTED',
        }),
      };
      await this.createParticipantUseCase.execute(dto);
      await this.notifyParticipants(participant.id, challenge);
    }
  }

  private async notifyParticipants(
    participantId: string,
    challenge: Challenge
  ) {
    const {
      id: { value: id },
      name: { value: name },
      challengeParticipantType: { value: participantType },
    } = challenge;
    if (!this.shouldNotificationBeSend(challenge, participantId)) return;
    const callToAction = this.getNotificationCTA(participantType, id);
    const dto = {
      userId: participantId,
      title: 'Te han invitado a un Reto Grupal ✉️',
      message: `Te han invitado al reto ${name}!`,
      notificationType: 'CTA',
      callToAction,
      priority: 'HIGH',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }

  private shouldNotificationBeSend(
    challenge: Challenge,
    participantId: string
  ) {
    const {
      challengeParticipantType: { value: participantType },
      userId: { value: owner },
    } = challenge;
    if (participantType !== 'GROUP' || owner === participantId) return false;

    return true;
  }

  private getNotificationCTA(participantType: string, id: string) {
    return participantType === 'GROUP'
      ? `../challenges/?view=INVITATION&challengeId=${id}`
      : '../challenges/?view=HOME';
  }
}
