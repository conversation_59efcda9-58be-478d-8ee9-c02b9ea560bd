import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../../shared/core/Result';
import { UseCase } from '../../../../../shared/core/use-case';
import { Challenge } from '../../../domain/challenge/Challenge';
import { Participant } from '../../../domain/participant/Participant';
import { ChallengeStatus } from '../../../domain/challenge/status';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { GetParticipantListByUserUseCase } from '../../participant/get-participant-list-by-user/use-case';
import { GetUserChallengeListDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class GetUserChallengeListByInvitationUseCase
  implements UseCase<GetUserChallengeListDTO, Promise<Challenge[]>>
{
  constructor(
    private challengeRepository: ChallengeRepository,
    private getParticipantListByUserUseCase: GetParticipantListByUserUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserChallengeListDTO) {
    const { id, status } = this.mapDTOToQueryParams(dto);
    const invitedChallengeList = await this.getInvitedChallengeList(id.value);

    return await this.getChallengeList(invitedChallengeList, status);
  }

  private mapDTOToQueryParams(dto: GetUserChallengeListDTO) {
    const id = UUIDValueObject.create('userId', dto.userId);
    const status = ChallengeStatus.create(dto.status);
    const dbCombine = Result.combine([id, status]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();

    return {
      id: id.getValue(),
      status: status.getValue(),
    };
  }

  private async getInvitedChallengeList(userId: string) {
    return await this.getParticipantListByUserUseCase.execute({
      userId,
    });
  }

  private async getChallengeList(
    invitedChallengeList: Participant[],
    status: ChallengeStatus
  ): Promise<Challenge[]> {
    const challegeRequests = invitedChallengeList.map((invitedChallenge) =>
      this.challengeRepository.getChallengeById(invitedChallenge.challengeId)
    );
    const result = await Promise.all(challegeRequests);

    return result
      .filter((challenge): challenge is Challenge => challenge !== null)
      .filter(
        (challenge): challenge is Challenge =>
          challenge.challengeStatus.value === status.value
      );
  }
}
