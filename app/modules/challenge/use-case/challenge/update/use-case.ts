import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { Challenge } from '../../../domain/challenge/Challenge';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { CreateParticipantUseCase } from '../../participant/create/use-case';
import { DeleteParticipantUseCase } from '../../participant/delete-participant/use-case';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { ParticipantList, UpdateChallengeDTO } from './dto';
import { Participant } from '../../../domain/participant/Participant';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class UpdateChallengeUseCase
  implements UseCase<UpdateChallengeDTO, Promise<Challenge>>
{
  constructor(
    private challengeRepository: ChallengeRepository,
    private createParticipantUseCase: CreateParticipantUseCase,
    private deleteParticipantUseCase: DeleteParticipantUseCase,
    private getParticipantListByChallengeUseCase: GetParticipantListByChallengeUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: UpdateChallengeDTO) {
    const id = this.mapDTOtoId(dto);
    const existingChallenge = await this.getExistingChallenge(id);
    const challenge = await this.updateChallenge(existingChallenge, dto);
    const updatedChallenge = await this.updateChallengeOnDatabase(challenge);
    await this.updateParticipants(challenge.id.value, dto);

    return updatedChallenge;
  }

  private mapDTOtoId(dto: UpdateChallengeDTO) {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) throw new Error('Invalid Id');

    return id.getValue();
  }

  private async getExistingChallenge(id: UUIDValueObject): Promise<Challenge> {
    const challenge = await this.challengeRepository.getChallengeById(id);
    if (!challenge) throw 'Challenge Not Found';

    return challenge;
  }

  private async updateChallenge(
    challenge: Challenge,
    dto: UpdateChallengeDTO
  ): Promise<Challenge> {
    challenge.update(dto);

    return challenge;
  }

  private async updateChallengeOnDatabase(
    challenge: Challenge
  ): Promise<Challenge> {
    const updatedChallenge = await this.challengeRepository.updateChallenge(
      challenge
    );
    if (!updatedChallenge) throw 'Error Updating Challenge';

    return updatedChallenge;
  }

  private async updateParticipants(
    challengeId: string,
    dto: UpdateChallengeDTO
  ) {
    if (!this.shouldUpdateParticipantList(dto)) return;
    const newParticipantList = dto.participants;
    const existingParticipantList =
      await this.getParticipantListByChallengeUseCase.execute({
        challengeId,
      });
    await this.createNecesaryParticipants(
      challengeId,
      newParticipantList,
      existingParticipantList
    );
    await this.updateNecesaryParticipants(
      newParticipantList,
      existingParticipantList
    );
  }

  private shouldUpdateParticipantList(dto: UpdateChallengeDTO) {
    return dto.participants && dto.challengeParticipantType === 'GROUP';
  }

  private async createNecesaryParticipants(
    challengeId: string,
    newParticipantList: ParticipantList,
    existingParticipantList: Participant[]
  ) {
    for (const participantToUpdate of newParticipantList) {
      const participantToCreate = existingParticipantList.find(
        (participant) => participant.userId.value === participantToUpdate.id
      );
      if (!participantToCreate) {
        await this.createParticipantUseCase.execute({
          userId: participantToUpdate.id,
          challengeId,
          userName: participantToUpdate.name,
          userEmail: participantToUpdate.email,
          initialValue: 0,
        });
      }
    }
  }

  private async updateNecesaryParticipants(
    newParticipantList: ParticipantList,
    existingParticipantList: Participant[]
  ) {
    for (const existingParticipant of existingParticipantList) {
      const participantToArchive = newParticipantList.find(
        (participant) => participant.id === existingParticipant.userId.value
      );
      if (!participantToArchive) {
        await this.deleteParticipantUseCase.execute({
          id: existingParticipant.id.value,
        });
      }
    }
  }
}
