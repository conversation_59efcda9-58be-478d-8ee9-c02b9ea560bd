import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UpdateChallengeDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { ChallengeMap } from '../../../mappers/challenge-map';
import { CreateParticipantUseCase } from '../../participant/create/use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { UpdateChallengeUseCase } from './use-case';
import { DeleteParticipantUseCase } from '../../participant/delete-participant/use-case';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { HTTPRequestBodyValidator } from '../../../../../shared/infra/http-request-body-validator';

class Lambda extends BaseHandler implements LambdaInterface {
  private updateChallengeUseCase: UpdateChallengeUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const createParticipantUseCase = new CreateParticipantUseCase(
      participantRepository
    );
    const deleteParticipantUseCase = new DeleteParticipantUseCase(
      participantRepository
    );
    const getParticipantListByChallengeUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    this.updateChallengeUseCase = new UpdateChallengeUseCase(
      challengeRepository,
      createParticipantUseCase,
      deleteParticipantUseCase,
      getParticipantListByChallengeUseCase
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.updateChallengeUseCase.execute(dto);

      return this.ok(ChallengeMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): UpdateChallengeDTO {
    const BODY_SCHEMA = [
      'id',
      'name',
      'description',
      'reward',
      'initialValue',
      'goalType',
      'goal',
      'goalUnit',
      'startDate',
      'endDate',
      'isRemainderActive',
      'progressType',
      'participants',
      'challengeParticipantType',
    ];

    return HTTPRequestBodyValidator<UpdateChallengeDTO>(
      BODY_SCHEMA,
      event.body
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
