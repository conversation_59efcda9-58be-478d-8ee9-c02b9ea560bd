import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { Challenge } from '../../../domain/challenge/Challenge';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { GetChallengeById } from './dto';
import { UseCaseResponse } from './types';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class GetUserChallengeByIdUseCase
  implements UseCase<GetChallengeById, Promise<UseCaseResponse>>
{
  constructor(
    private challengeRepository: ChallengeRepository,
    private getParticipantListByChallengeUseCase: GetParticipantListByChallengeUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetChallengeById) {
    const id = this.mapDTOtoChallengeId(dto);
    const challenge = await this.getChallenge(id);
    const challengeParticipantList = await this.getChallengeParticipantList(
      challenge
    );

    return { challenge, challengeParticipantList };
  }

  private mapDTOtoChallengeId(dto: GetChallengeById) {
    const id = UUIDValueObject.create('challengeId', dto.challengeId);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getChallenge(id: UUIDValueObject) {
    const challenge = await this.challengeRepository.getChallengeById(id);
    if (!challenge) throw 'Challenge Not Found';

    return challenge;
  }

  private async getChallengeParticipantList(challenge: Challenge) {
    const participantList =
      await this.getParticipantListByChallengeUseCase.execute({
        challengeId: challenge.id.value,
      });

    return participantList;
  }
}
