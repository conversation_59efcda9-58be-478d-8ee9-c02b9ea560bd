import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { GetChallengeById } from './dto';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { GetUserChallengeByIdUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { ChallengeMap } from '../../../mappers/challenge-map';

class <PERSON>da extends BaseHandler implements LambdaInterface {
  private getUserChallengeByIdUseCase: GetUserChallengeByIdUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    const getParticipantListByChallengeUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    this.getUserChallengeByIdUseCase = new GetUserChallengeByIdUseCase(
      challengeRepository,
      getParticipantListByChallengeUseCase
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const { challenge, challengeParticipantList } =
        await this.getUserChallengeByIdUseCase.execute(dto);

      return this.ok(ChallengeMap.toDTO(challenge, challengeParticipantList));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): GetChallengeById {
    const challengeId = event.queryStringParameters?.challengeId;
    if (!challengeId) {
      throw 'Failed to parse request body';
    }

    return {
      challengeId,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
