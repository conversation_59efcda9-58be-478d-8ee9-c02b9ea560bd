import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { ArchiveChallengeDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { ChallengeMap } from '../../../mappers/challenge-map';
import { ArchiveChallengeUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private archiveChallengeUseCase: ArchiveChallengeUseCase;

  private init() {
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.archiveChallengeUseCase = new ArchiveChallengeUseCase(
      challengeRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.archiveChallengeUseCase.execute(dto);

      return this.ok(ChallengeMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): ArchiveChallengeDTO {
    const id = event.queryStringParameters?.id;
    if (!id) {
      throw 'Failed to parse request body';
    }

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
