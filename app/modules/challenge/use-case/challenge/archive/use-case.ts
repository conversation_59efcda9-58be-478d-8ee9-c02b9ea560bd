import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { Challenge } from '../../../domain/challenge/Challenge';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { ArchiveChallengeDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class ArchiveChallengeUseCase
  implements UseCase<ArchiveChallengeDTO, Promise<Challenge>>
{
  constructor(private challengeRepository: ChallengeRepository) {}

  @LogUseCaseDTO
  public async execute(dto: ArchiveChallengeDTO) {
    const id = this.mapDTOToId(dto);
    const existingChallenge = await this.getExistingChallenge(id);
    const updatedChallenge = this.changeChallengeStatus(existingChallenge);
    const result = await this.updateChallenge(updatedChallenge);

    return result;
  }

  private mapDTOToId(dto: ArchiveChallengeDTO): UUIDValueObject {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingChallenge(id: UUIDValueObject): Promise<Challenge> {
    const challenge = await this.challengeRepository.getChallengeById(id);
    if (!challenge) throw 'Challenge Not Found';

    return challenge;
  }

  private changeChallengeStatus(challenge: Challenge): Challenge {
    challenge.archive();

    return challenge;
  }

  private async updateChallenge(challenge: Challenge): Promise<Challenge> {
    const result = await this.challengeRepository.updateChallenge(challenge);
    if (!result) throw 'Error Updating Challenge';

    return result;
  }
}
