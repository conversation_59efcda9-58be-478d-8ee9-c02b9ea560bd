import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { CompleteChallengeDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { ChallengeMap } from '../../../mappers/challenge-map';
import { CompleteChallengeUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { SetWinnerUseCase } from '../../participant/set-winner/use-case';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { GetUserChallengeByIdUseCase } from '../get-by-id/use-case';
import { GetUserChallengeProgressByIdUseCase } from '../../challenge-progress/get-challenge-progress-by-id/use-case';
import { GetLeaderboardByChallengeIdUseCase } from '../../leader-board/get-leader-board-by-challenge-id/use-case';
import { EventService } from '../../../../../shared/services/publish-sqs-event-service';

class Lambda extends BaseHandler implements LambdaInterface {
  private completeChallengeUseCase: CompleteChallengeUseCase;

  private init() {
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const setWinnerUseCase = new SetWinnerUseCase(participantRepository);
    const challengeProgressRepository = new ChallengeProgressRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_PROGRESS_TABLE_NAME || ''
    );
    const getParticipantListByUserUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    const getUserChallengeByIdUseCase = new GetUserChallengeByIdUseCase(
      challengeRepository,
      getParticipantListByUserUseCase
    );
    const getUserChallengeProgressByIdUseCase =
      new GetUserChallengeProgressByIdUseCase(challengeProgressRepository);
    const getLeaderboardByChallengeIdUseCase =
      new GetLeaderboardByChallengeIdUseCase(
        getUserChallengeByIdUseCase,
        getUserChallengeProgressByIdUseCase
      );
    const eventService = new EventService(
      process.env.SQS_URL || '',
      'notify-manual-challenge-closed'
    );
    this.completeChallengeUseCase = new CompleteChallengeUseCase(
      getUserChallengeByIdUseCase,
      challengeRepository,
      setWinnerUseCase,
      getLeaderboardByChallengeIdUseCase,
      eventService
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.completeChallengeUseCase.execute(dto);

      return this.ok(ChallengeMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): CompleteChallengeDTO {
    const id = event.queryStringParameters?.id;
    if (!id) {
      throw 'Failed to parse request body';
    }

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
