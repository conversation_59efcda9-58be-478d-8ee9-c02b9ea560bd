import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { EventService } from '../../../../../shared/services/publish-sqs-event-service';
import { Challenge } from '../../../domain/challenge/Challenge';
import { LeaderboardParticipant } from '../../../domain/leaderboard/LeaderboardParticipant';
import { Participant } from '../../../domain/participant/Participant';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { GetLeaderboardByChallengeIdUseCase } from '../../leader-board/get-leader-board-by-challenge-id/use-case';
import { SetWinnerUseCase } from '../../participant/set-winner/use-case';
import { GetUserChallengeByIdUseCase } from '../get-by-id/use-case';
import { CompleteChallengeDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class CompleteChallengeUseCase
  implements UseCase<CompleteChallengeDTO, Promise<Challenge>>
{
  private challengeWinner: LeaderboardParticipant | undefined;

  constructor(
    private getUserChallengeByIdUseCase: GetUserChallengeByIdUseCase,
    private challengeRepository: ChallengeRepository,
    private setWinnerUseCase: SetWinnerUseCase,
    private getLeaderboardByChallengeIdUseCase: GetLeaderboardByChallengeIdUseCase,
    private eventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute(dto: CompleteChallengeDTO) {
    const id = this.mapDTOToId(dto);
    const existingChallenge = await this.getExistingChallenge(id);
    const updatedChallenge = this.changeChallengeStatus(
      existingChallenge.challenge
    );
    const result = await this.updateChallenge(updatedChallenge);
    await this.updateChallengeWinner(result.id.value);
    await this.sendParticipantNotification(existingChallenge);

    return result;
  }

  private mapDTOToId(dto: CompleteChallengeDTO): UUIDValueObject {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingChallenge(id: UUIDValueObject): Promise<{
    challenge: Challenge;
    challengeParticipantList: Participant[];
  }> {
    const challenge = await this.getUserChallengeByIdUseCase.execute({
      challengeId: id.value,
    });
    if (!challenge) throw 'Challenge Not Found';

    return challenge;
  }

  private changeChallengeStatus(challenge: Challenge): Challenge {
    challenge.complete();

    return challenge;
  }

  private async updateChallenge(challenge: Challenge): Promise<Challenge> {
    const result = await this.challengeRepository.updateChallenge(challenge);
    if (!result) throw 'Error Updating Challenge';

    return result;
  }

  private async updateChallengeWinner(challengeId: string) {
    const winner = await this.getCurrentWinner(challengeId);
    if (winner) {
      await this.setChallengeWinner(winner.userId.value, challengeId);
      this.challengeWinner = winner;
    }
  }

  private async getCurrentWinner(challengeId: string) {
    const { winner } = await this.getLeaderboardByChallengeIdUseCase.execute({
      challengeId,
    });

    return winner;
  }

  private async setChallengeWinner(userId: string, challengeId: string) {
    await this.setWinnerUseCase.execute({
      userId,
      challengeId,
    });
  }

  private async sendParticipantNotification({
    challenge,
    challengeParticipantList,
  }: {
    challenge: Challenge;
    challengeParticipantList: Participant[];
  }) {
    try {
      for (const participant of challengeParticipantList) {
        const {
          userId: { value: userId },
        } = participant;
        await this.notifyParticipant(userId, challenge);
        if (this.isParticipantWinner(userId)) {
          await this.notifyWinner(userId, challenge);
        }
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Error notifying user:', error);
    }
  }

  private async notifyParticipant(participantId: string, challenge: Challenge) {
    const {
      name: { value: name },
    } = challenge;
    if (!this.shouldNotificationBeSend(challenge, participantId)) return;
    const dto = {
      userId: participantId,
      title: 'Reto Finalizado',
      message: `Te notificamos que el reto ${name} ha finalizado`,
      notificationType: 'CTA',
      callToAction: '../challenges/?view=COMPLETED',
      priority: 'LOW',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }

  private async notifyWinner(participantId: string, challenge: Challenge) {
    const {
      name: { value: name },
    } = challenge;
    if (!this.shouldNotificationBeSend(challenge, participantId)) return;
    const dto = {
      userId: participantId,
      title: 'Felicidades 🎉',
      message: `Eres el ganador del reto ${name}!!`,
      notificationType: 'CTA',
      callToAction: '../challenges/?view=COMPLETED',
      priority: 'LOW',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }

  private isParticipantWinner(participantId: string) {
    if (!this.challengeWinner) return false;

    return this.challengeWinner.userId.value === participantId;
  }

  private shouldNotificationBeSend(
    challenge: Challenge,
    participantId: string
  ) {
    const {
      userId: { value: owner },
    } = challenge;

    return owner === participantId ? false : true;
  }
}
