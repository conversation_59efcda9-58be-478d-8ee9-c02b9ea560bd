import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { GetUserChallengeListDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { GetUserChallengeListUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { GetUserChallengeListByInvitationUseCase } from '../get-list-by-invitation/use-case';
import { GetParticipantListByUserUseCase } from '../../participant/get-participant-list-by-user/use-case';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private getUserChallengeListUseCase: GetUserChallengeListUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const getParticipantListByChallengeUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    const getParticipantListByUserUseCase = new GetParticipantListByUserUseCase(
      participantRepository
    );
    const getUserChallengeListByInvitationUseCase =
      new GetUserChallengeListByInvitationUseCase(
        challengeRepository,
        getParticipantListByUserUseCase
      );
    this.getUserChallengeListUseCase = new GetUserChallengeListUseCase(
      challengeRepository,
      getParticipantListByChallengeUseCase,
      getUserChallengeListByInvitationUseCase
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.getUserChallengeListUseCase.execute(dto);

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): GetUserChallengeListDTO {
    const userId = event.queryStringParameters?.userId;
    const status = event.queryStringParameters?.status;
    if (!userId || !status) {
      throw 'Failed to parse request body';
    }

    return {
      userId,
      status,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
