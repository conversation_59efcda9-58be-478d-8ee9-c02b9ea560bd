import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../../shared/core/Result';
import { UseCase } from '../../../../../shared/core/use-case';
import { Challenge } from '../../../domain/challenge/Challenge';
import { ChallengeStatus } from '../../../domain/challenge/status';
import { ChallengeDTO } from '../../../dto/challenge-dto';
import { ChallengeMap } from '../../../mappers/challenge-map';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { GetUserChallengeListByInvitationUseCase } from '../get-list-by-invitation/use-case';
import { GetUserChallengeListDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class GetUserChallengeListUseCase
  implements UseCase<GetUserChallengeListDTO, Promise<ChallengeDTO[]>>
{
  constructor(
    private challengeRepository: ChallengeRepository,
    private getParticipantListByChallengeUseCase: GetParticipantListByChallengeUseCase,
    private getUserChallengeListByInvitationUseCase: GetUserChallengeListByInvitationUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserChallengeListDTO) {
    const { id, status } = this.mapDTOToQueryParams(dto);
    const challengeList = await this.getChallengeList(id, status);
    const invitedChallengeList =
      await this.getUserChallengeListByInvitationUseCase.execute({
        userId: id.value,
        status: status.value,
      });
    const standarizedList = this.standarizeChallengeList(
      challengeList,
      invitedChallengeList
    );

    return this.getParticipantList(standarizedList);
  }

  private mapDTOToQueryParams(dto: GetUserChallengeListDTO) {
    const id = UUIDValueObject.create('userId', dto.userId);
    const status = ChallengeStatus.create(dto.status);
    const dbCombine = Result.combine([id, status]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();

    return {
      id: id.getValue(),
      status: status.getValue(),
    };
  }

  private async getChallengeList(id: UUIDValueObject, status: ChallengeStatus) {
    const challengeProgressList =
      await this.challengeRepository.getChallengesByUserId(id, status);
    challengeProgressList.sort(
      (a: Challenge, b: Challenge) =>
        new Date(b.updatedAt.value).getTime() -
        new Date(a.updatedAt.value).getTime()
    );

    return challengeProgressList;
  }

  private async getParticipantList(challengeList: Challenge[]) {
    const updatedChallengeList: ChallengeDTO[] = [];
    for (const challenge of challengeList) {
      const id = challenge.id.value;
      const participantList =
        await this.getParticipantListByChallengeUseCase.execute({
          challengeId: id,
        });
      const participant = participantList || [];
      updatedChallengeList.push(ChallengeMap.toDTO(challenge, participant));
    }

    return updatedChallengeList;
  }

  private standarizeChallengeList(
    challengeList: Challenge[],
    invitedChallengeList: Challenge[]
  ) {
    const combinedChallengeList = [
      ...challengeList,
      ...invitedChallengeList,
    ].sort(
      (a: Challenge, b: Challenge) =>
        new Date(b.updatedAt.value).getTime() -
        new Date(a.updatedAt.value).getTime()
    );

    return this.removeDuplicatesById(combinedChallengeList);
  }

  private removeDuplicatesById(array: Challenge[]): Challenge[] {
    const seen = new Set<string>();

    return array.filter((challenge) => {
      const id = challenge.id.value;
      if (!seen.has(id)) {
        seen.add(id);

        return true;
      }

      return false;
    });
  }
}
