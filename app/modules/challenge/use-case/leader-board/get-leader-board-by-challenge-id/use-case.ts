import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { GetUserChallengeProgressByIdUseCase } from '../../challenge-progress/get-challenge-progress-by-id/use-case';
import { GetUserChallengeByIdUseCase } from '../../challenge/get-by-id/use-case';
import { GetLeaderboardByChallengeIdDTO } from './dto';
import { Leaderboard } from '../../../domain/leaderboard/Leaderboard';
import { LeaderboardMap } from '../../../mappers/leaderboard-map';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';
import { Challenge } from '../../../domain/challenge/Challenge';

export class GetLeaderboardByChallengeIdUseCase
  implements UseCase<GetLeaderboardByChallengeIdDTO, Leaderboard>
{
  constructor(
    private getUserChallengeByIdUseCase: GetUserChallengeByIdUseCase,
    private getUserChallengeProgressByIdUseCase: GetUserChallengeProgressByIdUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetLeaderboardByChallengeIdDTO) {
    const id = this.mapDTOtoChallengeId(dto);
    const { challenge, challengeParticipantList } = await this.getChallenge(id);
    const challengeProgress = await this.getChallengeParticipantProgress(id);
    const leaderBoard = this.createLeaderboard(challengeParticipantList);
    leaderBoard.calculateLeaderBoardProgress(
      challengeParticipantList,
      challengeProgress,
      challenge
    );

    return leaderBoard;
  }

  private mapDTOtoChallengeId(dto: GetLeaderboardByChallengeIdDTO) {
    const id = UUIDValueObject.create('challengeId', dto.challengeId);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getChallenge(id: UUIDValueObject): Promise<{
    challenge: Challenge;
    challengeParticipantList: Participant[];
  }> {
    const { challenge, challengeParticipantList } =
      await this.getUserChallengeByIdUseCase.execute({
        challengeId: id.value,
      });

    return {
      challenge,
      challengeParticipantList: challengeParticipantList.filter(
        (participant) => participant.participantStatus.value === 'ACCEPTED'
      ),
    };
  }

  private async getChallengeParticipantProgress(id: UUIDValueObject) {
    return await this.getUserChallengeProgressByIdUseCase.execute({
      challengeId: id.value,
    });
  }

  private createLeaderboard(
    challengeParticipantList: Participant[]
  ): Leaderboard {
    const progressByParticipants = challengeParticipantList.map((participant) =>
      this.createLeaderboardParticipant(participant)
    );
    const props = {
      leaderboard: progressByParticipants,
    };

    return LeaderboardMap.toDomain(props);
  }

  private createLeaderboardParticipant(participant: Participant) {
    return {
      userId: participant.userId.value,
      userName: participant.userName.value,
      userEmail: participant.userEmail.value,
      initialProgress: 0,
      latestProgress: 0,
      overallProgress: 0,
      hasAchievedGoal: false,
      isWinner: participant.isWinner.value || false,
    };
  }
}
