import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { GetLeaderboardByChallengeIdDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../../repo/challenge-repository';
import { GetLeaderboardByChallengeIdUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { GetUserChallengeByIdUseCase } from '../../challenge/get-by-id/use-case';
import { GetParticipantListByChallengeUseCase } from '../../participant/get-participant-list-by-challenge/use-case';
import { GetUserChallengeProgressByIdUseCase } from '../../challenge-progress/get-challenge-progress-by-id/use-case';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { LeaderboardMap } from '../../../mappers/leaderboard-map';

class Lambda extends BaseHandler implements LambdaInterface {
  private getLeaderboardByChallengeIdUseCase: GetLeaderboardByChallengeIdUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    const challengeProgressRepository = new ChallengeProgressRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_PROGRESS_TABLE_NAME || ''
    );
    const getParticipantListByUserUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    const getUserChallengeByIdUseCase = new GetUserChallengeByIdUseCase(
      challengeRepository,
      getParticipantListByUserUseCase
    );
    const getUserChallengeProgressByIdUseCase =
      new GetUserChallengeProgressByIdUseCase(challengeProgressRepository);
    this.getLeaderboardByChallengeIdUseCase =
      new GetLeaderboardByChallengeIdUseCase(
        getUserChallengeByIdUseCase,
        getUserChallengeProgressByIdUseCase
      );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.getLeaderboardByChallengeIdUseCase.execute(dto);

      return this.ok(LeaderboardMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): GetLeaderboardByChallengeIdDTO {
    const challengeId = event.queryStringParameters?.challengeId;
    if (!challengeId) {
      throw 'Failed to parse request body';
    }

    return {
      challengeId,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
