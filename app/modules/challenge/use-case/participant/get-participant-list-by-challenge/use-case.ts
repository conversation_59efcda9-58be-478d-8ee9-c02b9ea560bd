import { StatusValueObject } from '../../../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../../shared/core/Result';
import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantStatus } from '../../../domain/participant/participant-status';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { GetParticipantListByChallengeDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class GetParticipantListByChallengeUseCase
  implements UseCase<GetParticipantListByChallengeDTO, Promise<Participant[]>>
{
  constructor(private participantRepository: ParticipantRepository) {}

  @LogUseCaseDTO
  public async execute(dto: GetParticipantListByChallengeDTO) {
    const { id, status } = this.mapDTOToQueryParams(dto);

    return await this.getParticipantList(id, status);
  }

  private mapDTOToQueryParams(dto: GetParticipantListByChallengeDTO) {
    const id = UUIDValueObject.create('challengeId', dto.challengeId);
    const status = ParticipantStatus.create('ARCHIVED');
    const dbCombine = Result.combine([id, status]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();

    return {
      id: id.getValue(),
      status: status.getValue(),
    };
  }

  private async getParticipantList(
    id: UUIDValueObject,
    status: StatusValueObject
  ) {
    const participantList =
      await this.participantRepository.getParticipantsByChallengeId(id, status);
    participantList.sort(
      (a: Participant, b: Participant) =>
        new Date(b.updatedAt.value).getTime() -
        new Date(a.updatedAt.value).getTime()
    );

    return participantList;
  }
}
