import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { UpdateParticipationInitialValueDTO } from './dto';
import AWS from 'aws-sdk';
import { UpdateParticipationInitialValueUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { ParticipantMap } from '../../../mappers/participant-map';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { HTTPRequestBodyValidator } from '../../../../../shared/infra/http-request-body-validator';
import { GetParticipantListByChallengeUseCase } from '../get-participant-list-by-challenge/use-case';
import { AcceptInvitationUseCase } from '../post-accept-invitation/use-case';

class Lambda extends BaseHandler implements LambdaInterface {
  private updateParticipationInitialValueUseCase: UpdateParticipationInitialValueUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const acceptInvitationUseCase = new AcceptInvitationUseCase(
      participantRepository
    );
    const getParticipantListByChallengeUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    this.updateParticipationInitialValueUseCase =
      new UpdateParticipationInitialValueUseCase(
        participantRepository,
        getParticipantListByChallengeUseCase,
        acceptInvitationUseCase
      );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.updateParticipationInitialValueUseCase.execute(
        dto
      );

      return this.ok(ParticipantMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): UpdateParticipationInitialValueDTO {
    const BODY_SCHEMA = ['challengeId', 'userId', 'initialValue'];

    return HTTPRequestBodyValidator<UpdateParticipationInitialValueDTO>(
      BODY_SCHEMA,
      event.body
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
