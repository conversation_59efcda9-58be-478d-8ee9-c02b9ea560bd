import { NumberValueObject } from '../../../../../shared/common-value-objects/number-value-object';
import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../../shared/core/Result';
import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { GetParticipantListByChallengeUseCase } from '../get-participant-list-by-challenge/use-case';
import { AcceptInvitationUseCase } from '../post-accept-invitation/use-case';
import { UpdateParticipationInitialValueDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class UpdateParticipationInitialValueUseCase
  implements UseCase<UpdateParticipationInitialValueDTO, Promise<Participant>>
{
  constructor(
    private participantRepository: ParticipantRepository,
    private getParticipantListByChallengeUseCase: GetParticipantListByChallengeUseCase,
    private acceptInvitationUseCase: AcceptInvitationUseCase
  ) {}

  @LogUseCaseDTO
  public async execute(dto: UpdateParticipationInitialValueDTO) {
    const { userId, challengeId, initialValue } = this.mapDTOtoQueryParams(dto);
    const challengeParticipantList = await this.getChallengeParticipantList(
      challengeId
    );
    const participant = this.checkIfInvitationIsValid(
      challengeParticipantList,
      userId
    );
    await this.updateParticipantInitialValue(participant, initialValue);

    return participant;
  }

  private mapDTOtoQueryParams(dto: UpdateParticipationInitialValueDTO) {
    const challengeId = UUIDValueObject.create('challengeId', dto.challengeId);
    const userId = UUIDValueObject.create('userId', dto.userId);
    const initialValue = NumberValueObject.create(
      'initialValue',
      dto.initialValue
    );
    const dbCombined = Result.combine([challengeId, userId, initialValue]);
    if (dbCombined.isFailure) throw dbCombined.getErrorValue();

    return {
      userId: userId.getValue(),
      challengeId: challengeId.getValue(),
      initialValue: initialValue.getValue(),
    };
  }

  private async getChallengeParticipantList(challengeId: UUIDValueObject) {
    const challengeParticipantList =
      await this.getParticipantListByChallengeUseCase.execute({
        challengeId: challengeId.value,
      });
    if (!challengeParticipantList || challengeParticipantList.length === 0)
      throw 'Participant Not Found';

    return challengeParticipantList;
  }

  private checkIfInvitationIsValid(
    challengeParticipantList: Participant[],
    userId: UUIDValueObject
  ) {
    const participant = challengeParticipantList.find(
      (participant) => participant.userId.value === userId.value
    );
    if (!participant || participant.participantStatus.value !== 'INVITED')
      throw 'Participant Invalid';

    return participant;
  }

  private async updateParticipantInitialValue(
    participant: Participant,
    initialValue: NumberValueObject
  ) {
    participant.updateInitialValue(initialValue.value);
    await this.participantRepository.updateParticipant(participant);
    await this.acceptInvitationUseCase.execute({ id: participant.id.value });
  }
}
