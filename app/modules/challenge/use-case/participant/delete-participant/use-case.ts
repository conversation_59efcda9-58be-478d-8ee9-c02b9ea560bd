import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { DeleteParticipantDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class DeleteParticipantUseCase
  implements UseCase<DeleteParticipantDTO, Promise<Participant>>
{
  constructor(private participantRepository: ParticipantRepository) {}

  @LogUseCaseDTO
  public async execute(dto: DeleteParticipantDTO) {
    const id = this.mapDTOtoId(dto);
    const participant = await this.getExistingParticipant(id);
    participant.archive();
    const participantCreated = await this.updateParticipant(participant);

    return participantCreated;
  }

  private mapDTOtoId(dto: DeleteParticipantDTO) {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getExistingParticipant(
    id: UUIDValueObject
  ): Promise<Participant> {
    const participant = await this.participantRepository.getParticipantById(id);
    if (!participant) throw 'Participant Not Found';

    return participant;
  }

  private async updateParticipant(
    participant: Participant
  ): Promise<Participant> {
    const participantCreated =
      await this.participantRepository.updateParticipant(participant);
    if (!participantCreated) throw 'Error Updating Participant';

    return participantCreated;
  }
}
