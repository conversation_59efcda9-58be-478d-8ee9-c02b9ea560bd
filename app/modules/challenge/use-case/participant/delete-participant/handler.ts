import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { DeleteParticipantDTO } from './dto';
import AWS from 'aws-sdk';
import { DeleteParticipantUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { ParticipantMap } from '../../../mappers/participant-map';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private deleteParticipantUseCase: DeleteParticipantUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.deleteParticipantUseCase = new DeleteParticipantUseCase(
      participantRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.deleteParticipantUseCase.execute(dto);

      return this.ok(ParticipantMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): DeleteParticipantDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const userData = JSON.parse(event.body) as DeleteParticipantDTO;

    return {
      ...userData,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
