import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { GetParticipantListByUserDTO } from './dto';
import AWS from 'aws-sdk';
import { GetParticipantListByUserUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantMap } from '../../../mappers/participant-map';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private getetParticipantListByUserUseCase: GetParticipantListByUserUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.getetParticipantListByUserUseCase =
      new GetParticipantListByUserUseCase(participantRepository);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.getetParticipantListByUserUseCase.execute(dto);

      return this.ok(
        result.map((participant: Participant) =>
          ParticipantMap.toDTO(participant)
        )
      );
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): GetParticipantListByUserDTO {
    const userId = event.queryStringParameters?.userId;
    if (!userId) {
      throw 'Failed to parse request body';
    }

    return {
      userId,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
