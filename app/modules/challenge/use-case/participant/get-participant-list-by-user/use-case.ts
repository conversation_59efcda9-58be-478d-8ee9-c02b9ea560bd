import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../../shared/core/Result';
import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantStatus } from '../../../domain/participant/participant-status';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { GetParticipantListByUserDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class GetParticipantListByUserUseCase
  implements UseCase<GetParticipantListByUserDTO, Promise<Participant[]>>
{
  constructor(private participantRepository: ParticipantRepository) {}

  @LogUseCaseDTO
  public async execute(dto: GetParticipantListByUserDTO) {
    const { id, status } = this.mapDTOToQueryParams(dto);

    return await this.getParticipantList(id, status);
  }

  private mapDTOToQueryParams(dto: GetParticipantListByUserDTO) {
    const id = UUIDValueObject.create('userId', dto.userId);
    const status = ParticipantStatus.create('ARCHIVED');
    const dbCombine = Result.combine([id, status]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();

    return {
      id: id.getValue(),
      status: status.getValue(),
    };
  }

  private async getParticipantList(
    id: UUIDValueObject,
    status: ParticipantStatus
  ) {
    const participantList =
      await this.participantRepository.getParticipantsByUserId(id, status);
    participantList.sort(
      (a: Participant, b: Participant) =>
        new Date(b.updatedAt.value).getTime() -
        new Date(a.updatedAt.value).getTime()
    );

    return participantList;
  }
}
