import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { AcceptInvitationDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class AcceptInvitationUseCase
  implements UseCase<AcceptInvitationDTO, Promise<Participant>>
{
  constructor(private participantRepository: ParticipantRepository) {}

  @LogUseCaseDTO
  public async execute(dto: AcceptInvitationDTO) {
    const id = this.mapDTOtoId(dto);
    const participant = await this.getParticipant(id);
    await this.updateParticipantStatus(participant);

    return participant;
  }

  private mapDTOtoId(dto: AcceptInvitationDTO) {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getParticipant(id: UUIDValueObject) {
    const participant = await this.participantRepository.getParticipantById(id);
    if (!participant) throw 'Participant Not Found';

    return participant;
  }

  private async updateParticipantStatus(participant: Participant) {
    participant.updateStatusAccept();
    await this.participantRepository.updateParticipant(participant);
  }
}
