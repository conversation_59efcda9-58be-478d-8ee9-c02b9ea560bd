import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { AcceptInvitationDTO } from './dto';
import AWS from 'aws-sdk';
import { AcceptInvitationUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { ParticipantMap } from '../../../mappers/participant-map';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { HTTPRequestBodyValidator } from '../../../../../shared/infra/http-request-body-validator';

class Lambda extends BaseHandler implements LambdaInterface {
  private acceptInvitationUseCase: AcceptInvitationUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.acceptInvitationUseCase = new AcceptInvitationUseCase(
      participantRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.acceptInvitationUseCase.execute(dto);

      return this.ok(ParticipantMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): AcceptInvitationDTO {
    const BODY_SCHEMA = ['id'];

    return HTTPRequestBodyValidator<AcceptInvitationDTO>(
      BODY_SCHEMA,
      event.body
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
