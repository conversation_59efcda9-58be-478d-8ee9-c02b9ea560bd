import { StatusValueObject } from '../../../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../../shared/core/Result';
import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { SetWinnerDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class SetWinnerUseCase implements UseCase<SetWinnerDTO, Participant[]> {
  constructor(private participantRepository: ParticipantRepository) {}

  @LogUseCaseDTO
  public async execute(dto: SetWinnerDTO) {
    const { userId, challengeId, status } = this.mapDTOtoQueryParams(dto);
    const participantList =
      await this.participantRepository.getParticipantsByChallengeId(
        challengeId,
        status
      );
    const result = await this.setWinnerInParticipantList(
      userId,
      participantList
    );

    return result;
  }

  private mapDTOtoQueryParams(dto: SetWinnerDTO) {
    const userId = UUIDValueObject.create('userId', dto.userId);
    const challengeId = UUIDValueObject.create('challengeId', dto.challengeId);
    const status = StatusValueObject.create('status', 'ARCHIVED');
    const dbCombined = Result.combine([userId, challengeId]);
    if (dbCombined.isFailure) throw dbCombined.getErrorValue();

    return {
      userId: userId.getValue(),
      challengeId: challengeId.getValue(),
      status: status.getValue(),
    };
  }

  private async setWinnerInParticipantList(
    winnerId: UUIDValueObject,
    participantList: Participant[]
  ) {
    for (const participant of participantList) {
      participant.setIsWinner(this.isWinner(participant, winnerId));
      await this.participantRepository.updateParticipant(participant);
    }

    return participantList;
  }

  private isWinner(participant: Participant, userId: UUIDValueObject): boolean {
    return participant.userId.value === userId.value;
  }
}
