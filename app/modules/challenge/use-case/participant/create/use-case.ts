import { UseCase } from '../../../../../shared/core/use-case';
import { Participant } from '../../../domain/participant/Participant';
import { ParticipantMap } from '../../../mappers/participant-map';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { CreateParticipantDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class CreateParticipantUseCase
  implements UseCase<CreateParticipantDTO, Promise<Participant>>
{
  constructor(private participantRepository: ParticipantRepository) {}

  @LogUseCaseDTO
  public async execute(dto: CreateParticipantDTO) {
    const participant = this.mapDTOtoParticipant(dto);
    const participantCreated = await this.createParticipant(participant);

    return participantCreated;
  }

  private mapDTOtoParticipant(dto: CreateParticipantDTO) {
    const challenge = ParticipantMap.toDomain({
      ...dto,
      participantStatus: dto.participantStatus || 'INVITED',
      isWinner: false,
    });

    return challenge;
  }

  private async createParticipant(
    participant: Participant
  ): Promise<Participant> {
    const participantCreated =
      await this.participantRepository.createParticipant(participant);
    if (!participantCreated) throw 'Error Creating Participant';

    return participantCreated;
  }
}
