import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { CreateParticipantDTO } from './dto';
import AWS from 'aws-sdk';
import { CreateParticipantUseCase } from './use-case';
import { ParticipantRepository } from '../../../repo/participant-repository';
import { ParticipantMap } from '../../../mappers/participant-map';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private createParticipantUseCase: CreateParticipantUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.createParticipantUseCase = new CreateParticipantUseCase(
      participantRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.createParticipantUseCase.execute(dto);

      return this.ok(ParticipantMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): CreateParticipantDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const userData = JSON.parse(event.body) as CreateParticipantDTO;

    return {
      ...userData,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
