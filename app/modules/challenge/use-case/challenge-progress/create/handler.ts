import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { <PERSON><PERSON>and<PERSON> } from '../../../../../shared/infra/handler';
import { CreateChallengeProgressDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { ChallengeProgressMap } from '../../../mappers/challenge-progress-map';
import { CreateChallengeProgressUseCase } from './use-case';
import { S3Service } from '../../../../../shared/repos/file-repository';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { HTTPRequestBodyValidator } from '../../../../../shared/infra/http-request-body-validator';

class Lambda extends BaseHandler implements LambdaInterface {
  private createChallengeProgressUseCase: CreateChallengeProgressUseCase;

  private init() {
    const challengeProgressRepository = new ChallengeProgressRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const s3Service = new S3Service(
      new AWS.S3(),
      process.env.BUCKET_NAME || ''
    );
    this.createChallengeProgressUseCase = new CreateChallengeProgressUseCase(
      challengeProgressRepository,
      s3Service
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.createChallengeProgressUseCase.execute(dto);

      return this.ok(ChallengeProgressMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): CreateChallengeProgressDTO {
    const BODY_SCHEMA = [
      'challengeId',
      'userId',
      'userName',
      'value',
      'challengeProgressType',
      'evidence',
    ];

    return HTTPRequestBodyValidator<CreateChallengeProgressDTO>(
      BODY_SCHEMA,
      event.body
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
