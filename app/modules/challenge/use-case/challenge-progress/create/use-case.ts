import { UseCase } from '../../../../../shared/core/use-case';
import { S3Service } from '../../../../../shared/repos/file-repository';
import { ChallengeProgress } from '../../../domain/challenge-progress/ChallengeProgress';
import { ChallengeProgressMap } from '../../../mappers/challenge-progress-map';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { CreateChallengeProgressDTO } from './dto';
import { v4 as uuidv4 } from 'uuid';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';
import { DateValueObject } from '../../../../../shared/common-value-objects/date-value-object';

export class CreateChallengeProgressUseCase
  implements UseCase<CreateChallengeProgressDTO, Promise<ChallengeProgress>>
{
  private readonly bucketName = `${process.env.BUCKET || ''}progress-evidence`;

  constructor(
    private challengeProgressRepository: ChallengeProgressRepository,
    private s3Service: S3Service
  ) {}

  @LogUseCaseDTO
  public async execute(dto: CreateChallengeProgressDTO) {
    const challengeProgress = await this.mapDTOtoChallengeProgress(dto);
    const challengeProgressCreated = await this.createChallengeProgress(
      challengeProgress
    );

    return challengeProgressCreated;
  }

  private async mapDTOtoChallengeProgress(dto: CreateChallengeProgressDTO) {
    const evidence = await this.saveFileToS3(dto);
    const completedAt = this.getChallengeProgressCompletedAt(dto.progressDate);
    const challengeProgressProps = {
      ...dto,
      challengeProgressStatus: 'ACTIVE',
      evidence,
      createdAt: completedAt,
    };
    const challenge = ChallengeProgressMap.toDomain(challengeProgressProps);

    return challenge;
  }

  private getChallengeProgressCompletedAt(date: string | undefined) {
    try {
      if (!date) throw 'Invalid Completed At';
      const completedAt = DateValueObject.create('completedAt', date);
      if (completedAt.isFailure) {
        throw completedAt.getErrorValue();
      }

      return completedAt.getValue().value;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        'Error while trying to create the challenge progress:',
        error
      );

      return undefined;
    }
  }

  private async saveFileToS3(
    dto: CreateChallengeProgressDTO
  ): Promise<string | undefined> {
    try {
      if (dto.evidence === null) return;
      const {
        evidence: { name, file },
      } = dto;
      const key = this.getFileKey(this.getFileData(name));
      const s3ServiceProps = {
        base64File: this.getFileData(file),
        bucket: this.bucketName,
        key,
      };
      const result = await this.s3Service.storePublicBase64File(s3ServiceProps);
      if (result.status === 'error' || !result.url)
        throw 'Error while trying to store S3 file';

      return result.url;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Error while uploading the process evidence:', error);
      throw error;
    }
  }

  private getFileKey(name: string): string {
    const [fileName, fileExtension] = name.split('.');

    return `${fileName.trim()}-${uuidv4()}.${fileExtension.trim()}`
      .trim()
      .replace(' ', '');
  }

  private getFileData(data: string): string {
    if (!data) throw 'Invalid File Data';

    return data;
  }

  private async createChallengeProgress(
    challengeProgress: ChallengeProgress
  ): Promise<ChallengeProgress> {
    const challengeProgressCreated =
      await this.challengeProgressRepository.createChallengeProgress(
        challengeProgress
      );
    if (!challengeProgressCreated) throw 'Error Creating Challenge Progress';

    return challengeProgressCreated;
  }
}
