import AWS from 'aws-sdk';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { BatchCreateChallengeProgressDTO } from './dto';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { ChallengeProgressMap } from '../../../mappers/challenge-progress-map';
import { S3Service } from '../../../../../shared/repos/file-repository';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { HTTPRequestBodyValidator } from '../../../../../shared/infra/http-request-body-validator';
import { CreateChallengeProgressDTO } from '../create/dto';
import { CreateChallengeProgressUseCase } from '../create/use-case';

class Lambda extends BaseHandler implements LambdaInterface {
  private createChallengeProgressUseCase: CreateChallengeProgressUseCase;

  private init() {
    const challengeProgressRepository = new ChallengeProgressRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const s3Service = new S3Service(
      new AWS.S3(),
      process.env.BUCKET_NAME || ''
    );
    this.createChallengeProgressUseCase = new CreateChallengeProgressUseCase(
      challengeProgressRepository,
      s3Service
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const batchResult = [];
      for (const data of dto) {
        const result = await this.createChallengeProgressUseCase.execute(data);
        batchResult.push(result);
      }

      return this.ok(batchResult.map(ChallengeProgressMap.toDTO));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): BatchCreateChallengeProgressDTO {
    if (!event.body) throw new Error('Fail to parse request body');
    const BODY_SCHEMA = [
      'challengeId',
      'userId',
      'userName',
      'value',
      'challengeProgressType',
    ];
    const body = JSON.parse(event.body) as BatchCreateChallengeProgressDTO;

    return body.map((data) =>
      HTTPRequestBodyValidator<CreateChallengeProgressDTO>(BODY_SCHEMA, data)
    );
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
