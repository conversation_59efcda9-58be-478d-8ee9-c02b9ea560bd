import { StatusValueObject } from '../../../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../../../shared/core/Result';
import { UseCase } from '../../../../../shared/core/use-case';
import { ChallengeProgress } from '../../../domain/challenge-progress/ChallengeProgress';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { GetUserChallengeProgressByUserIdDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class GetUserChallengeProgressByUserIdUseCase
  implements
    UseCase<GetUserChallengeProgressByUserIdDTO, Promise<ChallengeProgress[]>>
{
  constructor(
    private challengeProgressRepository: ChallengeProgressRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserChallengeProgressByUserIdDTO) {
    const { challengeId, userId, status } = this.mapDTOToQueryParams(dto);

    return await this.getChallengeProgressList(challengeId, userId, status);
  }

  private mapDTOToQueryParams(dto: GetUserChallengeProgressByUserIdDTO) {
    const challengeId = UUIDValueObject.create('challengeId', dto.challengeId);
    const userId = UUIDValueObject.create('userId', dto.userId);
    const status = StatusValueObject.create('status', 'ACTIVE');
    const dbCombine = Result.combine([challengeId, userId, status]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();

    return {
      challengeId: challengeId.getValue(),
      userId: userId.getValue(),
      status: status.getValue(),
    };
  }

  private async getChallengeProgressList(
    challengeId: UUIDValueObject,
    userId: UUIDValueObject,
    status: StatusValueObject
  ) {
    const challengeProgressList =
      await this.challengeProgressRepository.getChallengeProgresssByIdAndUserId(
        challengeId,
        userId,
        status
      );
    challengeProgressList.sort(
      (a: ChallengeProgress, b: ChallengeProgress) =>
        new Date(b.updatedAt.value).getTime() -
        new Date(a.updatedAt.value).getTime()
    );

    return challengeProgressList;
  }
}
