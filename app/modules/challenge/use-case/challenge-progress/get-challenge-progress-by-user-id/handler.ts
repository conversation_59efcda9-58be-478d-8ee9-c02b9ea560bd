import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/handler';
import { GetUserChallengeProgressByUserIdDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { ChallengeProgressMap } from '../../../mappers/challenge-progress-map';
import { GetUserChallengeProgressByUserIdUseCase } from './use-case';
import { ChallengeProgress } from '../../../domain/challenge-progress/ChallengeProgress';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class <PERSON>da extends BaseHandler implements LambdaInterface {
  private getUserChallengeProgressByUserIdUseCase: GetUserChallengeProgressByUserIdUseCase;

  private init() {
    const challengeProgressRepository = new ChallengeProgressRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.getUserChallengeProgressByUserIdUseCase =
      new GetUserChallengeProgressByUserIdUseCase(challengeProgressRepository);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.getUserChallengeProgressByUserIdUseCase.execute(
        dto
      );

      return this.ok(
        result.map((challengeProgress: ChallengeProgress) =>
          ChallengeProgressMap.toDTO(challengeProgress)
        )
      );
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): GetUserChallengeProgressByUserIdDTO {
    const challengeId = event.queryStringParameters?.challengeId;
    const userId = event.queryStringParameters?.userId;
    if (!userId || !challengeId) {
      throw 'Failed to parse request body';
    }

    return { challengeId, userId };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
