import { StatusValueObject } from '../../../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { ChallengeProgress } from '../../../domain/challenge-progress/ChallengeProgress';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { GetUserChallengeProgressByIdDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class GetUserChallengeProgressByIdUseCase
  implements
    UseCase<GetUserChallengeProgressByIdDTO, Promise<ChallengeProgress[]>>
{
  constructor(
    private challengeProgressRepository: ChallengeProgressRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserChallengeProgressByIdDTO) {
    const id = this.mapDTOToId(dto);
    const status = this.getChallengeStatus();

    return await this.getChallengeProgressList(id, status);
  }

  private mapDTOToId(dto: GetUserChallengeProgressByIdDTO): UUIDValueObject {
    const id = UUIDValueObject.create('challengeId', dto.challengeId);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private getChallengeStatus() {
    const status = StatusValueObject.create('status', 'ACTIVE');
    if (status.isFailure) throw status.getErrorValue();

    return status.getValue();
  }

  private async getChallengeProgressList(
    id: UUIDValueObject,
    status: StatusValueObject
  ) {
    const challengeProgressList =
      await this.challengeProgressRepository.getChallengeProgressByChallengeId(
        id,
        status
      );
    challengeProgressList.sort(
      (a: ChallengeProgress, b: ChallengeProgress) =>
        new Date(b.updatedAt.value).getTime() -
        new Date(a.updatedAt.value).getTime()
    );

    return challengeProgressList;
  }
}
