import { UUIDValueObject } from '../../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../../shared/core/use-case';
import { ChallengeProgress } from '../../../domain/challenge-progress/ChallengeProgress';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { ArchiveChallengeProgressDTO } from './dto';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';

export class ArchiveChallengeProgressUseCase
  implements UseCase<ArchiveChallengeProgressDTO, Promise<ChallengeProgress>>
{
  constructor(
    private challengeProgressRepository: ChallengeProgressRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: ArchiveChallengeProgressDTO) {
    const id = this.mapDTOToId(dto);
    const existingChallengeProgress = await this.getExistingChallengeProgress(
      id
    );
    const updatedChallenge = this.changeChallengeStatus(
      existingChallengeProgress
    );
    const result = await this.updateChallenge(updatedChallenge);

    return result;
  }

  private mapDTOToId(dto: ArchiveChallengeProgressDTO): UUIDValueObject {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingChallengeProgress(
    id: UUIDValueObject
  ): Promise<ChallengeProgress> {
    const challengeProgress =
      await this.challengeProgressRepository.getChallengeProgressById(id);
    if (!challengeProgress) throw 'Challenge Progress Not Found';

    return challengeProgress;
  }

  private changeChallengeStatus(
    challengeProgress: ChallengeProgress
  ): ChallengeProgress {
    challengeProgress.archive();

    return challengeProgress;
  }

  private async updateChallenge(
    challengeProgress: ChallengeProgress
  ): Promise<ChallengeProgress> {
    const result =
      await this.challengeProgressRepository.updateChallengeProgress(
        challengeProgress
      );
    if (!result) throw 'Error Updating Challenge Progress';

    return result;
  }
}
