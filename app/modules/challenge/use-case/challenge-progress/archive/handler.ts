import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { <PERSON>Handler } from '../../../../../shared/infra/handler';
import { ArchiveChallengeProgressDTO } from './dto';
import AWS from 'aws-sdk';
import { ChallengeProgressMap } from '../../../mappers/challenge-progress-map';
import { ChallengeProgressRepository } from '../../../repo/challenge-progress-repository';
import { ArchiveChallengeProgressUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private archiveChallengeProgressUseCase: ArchiveChallengeProgressUseCase;

  private init() {
    const challengeProgressRepository = new ChallengeProgressRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.archiveChallengeProgressUseCase = new ArchiveChallengeProgressUseCase(
      challengeProgressRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.archiveChallengeProgressUseCase.execute(dto);

      return this.ok(ChallengeProgressMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): ArchiveChallengeProgressDTO {
    const id = event.queryStringParameters?.id;
    if (!id) {
      throw 'Failed to parse request body';
    }

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
