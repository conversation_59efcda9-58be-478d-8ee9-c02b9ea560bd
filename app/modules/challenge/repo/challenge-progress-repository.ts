import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { ChallengeProgress } from '../domain/challenge-progress/ChallengeProgress';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { StatusValueObject } from '../../../shared/common-value-objects/status-value-object';
import { ChallengeProgressMap } from '../mappers/challenge-progress-map';
import { DynamoServices } from '../../../shared/services/dynamo-services';

export class ChallengeProgressRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createChallengeProgress(
    challengeProgress: ChallengeProgress
  ): Promise<ChallengeProgress> {
    const rawChallengeProgress =
      ChallengeProgressMap.toPersistence(challengeProgress);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(rawChallengeProgress),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return challengeProgress;
  }

  public async getChallengeProgressById(
    challengeProgressID: UUIDValueObject
  ): Promise<ChallengeProgress | null> {
    const id = challengeProgressID.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return ChallengeProgressMap.toDomain(this.unmarshallElement(result.Item));
  }

  public async getChallengeProgressByChallengeId(
    challengeId: UUIDValueObject,
    challengeProgressStatus: StatusValueObject
  ): Promise<ChallengeProgress[]> {
    const id = challengeId.value;
    const status = challengeProgressStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'challengeId-challengeProgressStatus-index',
      KeyConditionExpression:
        'challengeId = :challengeId AND challengeProgressStatus = :challengeProgressStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':challengeId': { S: id },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':challengeProgressStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ChallengeProgressMap.toDomain(item));
  }

  public async getChallengeProgresssByIdAndUserId(
    challengeId: UUIDValueObject,
    challegeUserId: UUIDValueObject,
    challengeProgressStatus: StatusValueObject
  ): Promise<ChallengeProgress[]> {
    const id = challengeId.value;
    const userId = challegeUserId.value;
    const status = challengeProgressStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'challengeId-userId-index',
      KeyConditionExpression: 'challengeId = :challengeId AND userId = :userId',
      FilterExpression: 'challengeProgressStatus = :status',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':challengeId': { S: id },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userId': { S: userId },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':status': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ChallengeProgressMap.toDomain(item));
  }

  public async updateChallengeProgress(
    challengeProgress: ChallengeProgress
  ): Promise<ChallengeProgress | null> {
    const id = challengeProgress.id.value;
    const data = ChallengeProgressMap.toPersistence(challengeProgress);
    delete data.id;
    const params = {
      TableName: this.tableName,
      Key: this.marshallElement({
        id,
      }),
      ReturnValues: 'ALL_NEW',
      UpdateExpression:
        'set ' +
        Object.keys(data)
          .map((k) => `#${k} = :${k}`)
          .join(', '),
      ExpressionAttributeNames: Object.entries(data).reduce(
        (acc, cur) => ({ ...acc, [`#${cur[0]}`]: cur[0] }),
        {}
      ),
      ExpressionAttributeValues: this.marshallElement(
        Object.entries(data).reduce(
          (acc, cur) => ({ ...acc, [`:${cur[0]}`]: cur[1] }),
          {}
        )
      ),
    };
    const result = await this.dynamodb.updateItem(params).promise();
    if (!result.Attributes) throw 'Query return values error';
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return ChallengeProgressMap.toDomain(
      this.unmarshallElement(result.Attributes)
    );
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
