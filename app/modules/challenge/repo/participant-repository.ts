import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Participant } from '../domain/participant/Participant';
import { ParticipantMap } from '../mappers/participant-map';
import { ParticipantStatus } from '../domain/participant/participant-status';
import { DynamoServices } from '../../../shared/services/dynamo-services';

export class ParticipantRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createParticipant(
    participant: Participant
  ): Promise<Participant> {
    const raw = ParticipantMap.toPersistence(participant);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(raw),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return participant;
  }

  public async getParticipantById(
    participantId: UUIDValueObject
  ): Promise<Participant | null> {
    const id = participantId.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return ParticipantMap.toDomain(this.unmarshallElement(result.Item));
  }

  public async getParticipantsByStatus(
    participantStatus: ParticipantStatus
  ): Promise<Participant[]> {
    const status = participantStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'participantStatus-createdAt-index',
      KeyConditionExpression: 'participantStatus = :participantStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':participantStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ParticipantMap.toDomain(item));
  }

  public async getParticipantsByUserId(
    userId: UUIDValueObject,
    participantStatus: ParticipantStatus
  ): Promise<Participant[]> {
    const id = userId.value;
    const status = participantStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'userId-index',
      KeyConditionExpression: 'userId = :userId ',
      FilterExpression: 'participantStatus <> :participantStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userId': { S: id },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':participantStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ParticipantMap.toDomain(item));
  }

  public async getParticipantsByChallengeId(
    challengeId: UUIDValueObject,
    participantStatus: ParticipantStatus
  ): Promise<Participant[]> {
    const id = challengeId.value;
    const status = participantStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'challengeId-index',
      KeyConditionExpression: 'challengeId = :challengeId ',
      FilterExpression: 'participantStatus <> :participantStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':challengeId': { S: id },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':participantStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ParticipantMap.toDomain(item));
  }

  public async updateParticipant(
    participant: Participant
  ): Promise<Participant | null> {
    const id = participant.id.value;
    const data = ParticipantMap.toPersistence(participant);
    delete data.id;
    const params = {
      TableName: this.tableName,
      Key: this.marshallElement({
        id,
      }),
      ReturnValues: 'ALL_NEW',
      UpdateExpression:
        'set ' +
        Object.keys(data)
          .map((k) => `#${k} = :${k}`)
          .join(', '),
      ExpressionAttributeNames: Object.entries(data).reduce(
        (acc, cur) => ({ ...acc, [`#${cur[0]}`]: cur[0] }),
        {}
      ),
      ExpressionAttributeValues: this.marshallElement(
        Object.entries(data).reduce(
          (acc, cur) => ({ ...acc, [`:${cur[0]}`]: cur[1] }),
          {}
        )
      ),
    };
    const result = await this.dynamodb.updateItem(params).promise();
    if (!result.Attributes) throw 'Query return values error';
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return ParticipantMap.toDomain(this.unmarshallElement(result.Attributes));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
