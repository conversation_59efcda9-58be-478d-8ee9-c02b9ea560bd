import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { ChallengeMap } from '../mappers/challenge-map';
import { Challenge } from '../domain/challenge/Challenge';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { ChallengeStatus } from '../domain/challenge/status';
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { DynamoServices } from '../../../shared/services/dynamo-services';

export class ChallengeRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createChallenge(challenge: Challenge): Promise<Challenge> {
    const rawChallenge = ChallengeMap.toPersistence(challenge);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(rawChallenge),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return challenge;
  }

  public async getChallengeById(
    challengeID: UUIDValueObject
  ): Promise<Challenge | null> {
    const id = challengeID.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return ChallengeMap.toDomain(this.unmarshallElement(result.Item));
  }

  public async getChallengesByStatus(
    challengeStatus: ChallengeStatus
  ): Promise<Challenge[]> {
    const status = challengeStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'challengeStatus-createdAt-index',
      KeyConditionExpression: 'challengeStatus = :challengeStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':challengeStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ChallengeMap.toDomain(item));
  }

  public async getChallengesByEndDate(
    endDate: DateValueObject,
    challengeStatus: ChallengeStatus
  ): Promise<Challenge[]> {
    const date = endDate.value;
    const status = challengeStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'challengeStatus-endDate-index',
      KeyConditionExpression:
        'challengeStatus = :challengeStatus AND endDate <= :endDate',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':challengeStatus': { S: status },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':endDate': { S: date },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ChallengeMap.toDomain(item));
  }

  public async getChallengesByUserId(
    userId: UUIDValueObject,
    challengeStatus: ChallengeStatus
  ): Promise<Challenge[]> {
    const id = userId.value;
    const status = challengeStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'userId-challengeStatus-index',
      KeyConditionExpression:
        'userId = :userId AND challengeStatus = :challengeStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userId': { S: id },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':challengeStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => ChallengeMap.toDomain(item));
  }

  public async updateChallenge(
    challenge: Challenge
  ): Promise<Challenge | null> {
    const id = challenge.id.value;
    const data = ChallengeMap.toPersistence(challenge);
    delete data.id;
    const params = {
      TableName: this.tableName,
      Key: this.marshallElement({
        id,
      }),
      ReturnValues: 'ALL_NEW',
      UpdateExpression:
        'set ' +
        Object.keys(data)
          .map((k) => `#${k} = :${k}`)
          .join(', '),
      ExpressionAttributeNames: Object.entries(data).reduce(
        (acc, cur) => ({ ...acc, [`#${cur[0]}`]: cur[0] }),
        {}
      ),
      ExpressionAttributeValues: this.marshallElement(
        Object.entries(data).reduce(
          (acc, cur) => ({ ...acc, [`:${cur[0]}`]: cur[1] }),
          {}
        )
      ),
    };
    const result = await this.dynamodb.updateItem(params).promise();
    if (!result.Attributes) throw 'Query return values error';
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return ChallengeMap.toDomain(this.unmarshallElement(result.Attributes));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
