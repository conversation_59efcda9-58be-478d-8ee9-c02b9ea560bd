import { ParticipantDTO } from './participant-dto';

export type ChallengeDTO = {
  id: string;
  userId: string;
  description: string;
  name: string;
  reward: string;
  initialValue: number;
  startDate: string;
  endDate: string;
  goal: number;
  goalUnit: string;
  goalType: string;
  isRemainderActive: boolean;
  isEvidenceRequired: boolean;
  remainderType?: string;
  progressType: string;
  challengeStatus: string;
  participants: ParticipantDTO[];
  challengeParticipantType: string;
  timeProgress: number;
  challengeProgress: number;
  updatedAt: string;
  createdAt: string;
};
