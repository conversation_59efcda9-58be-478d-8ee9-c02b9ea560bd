import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { ChallengeRepository } from '../../repo/challenge-repository';
import { GetUserChallengeListUseCase } from './use-case';
import { ParticipantRepository } from '../../repo/participant-repository';
import { GetParticipantListByChallengeUseCase } from '../../use-case/participant/get-participant-list-by-challenge/use-case';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private getUserChallengeListUseCase: GetUserChallengeListUseCase;

  private init() {
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const getParticipantListByChallengeUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    const eventService = new EventService(
      process.env.SQS_URL || '',
      'challelge-notification'
    );
    this.getUserChallengeListUseCase = new GetUserChallengeListUseCase(
      challengeRepository,
      getParticipantListByChallengeUseCase,
      eventService
    );
  }

  @LogHandlerEvent
  public async handler() {
    try {
      this.init();
      const result = await this.getUserChallengeListUseCase.execute();

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
