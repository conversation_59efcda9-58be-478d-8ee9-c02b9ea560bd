import { Result } from '../../../../shared/core/Result';
import { UseCase } from '../../../../shared/core/use-case';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';
import { Challenge } from '../../domain/challenge/Challenge';
import { ChallengeStatus } from '../../domain/challenge/status';
import { ChallengeDTO } from '../../dto/challenge-dto';
import { ChallengeMap } from '../../mappers/challenge-map';
import { ChallengeRepository } from '../../repo/challenge-repository';
import { GetParticipantListByChallengeUseCase } from '../../use-case/participant/get-participant-list-by-challenge/use-case';

export class GetUserChallengeListUseCase
  implements UseCase<null, Promise<void>>
{
  constructor(
    private challengeRepository: ChallengeRepository,
    private getParticipantListByChallengeUseCase: GetParticipantListByChallengeUseCase,
    private eventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute() {
    const { status } = this.mapDTOToQueryParams();
    const challengeList = await this.getChallengeList(status);
    // eslint-disable-next-line no-console
    console.log('challengeList', challengeList);
    const challengeAndParticipantList = await this.getParticipantList(
      challengeList
    );
    // eslint-disable-next-line no-console
    console.log('challengeAndParticipantList', challengeAndParticipantList);
    await this.publishChallengeRemianders(challengeAndParticipantList);
  }

  private mapDTOToQueryParams() {
    const status = ChallengeStatus.create('ACTIVE');
    const dbCombine = Result.combine([status]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();

    return {
      status: status.getValue(),
    };
  }

  private async getChallengeList(status: ChallengeStatus) {
    const challengeProgressList =
      await this.challengeRepository.getChallengesByStatus(status);
    challengeProgressList.sort(
      (a: Challenge, b: Challenge) =>
        new Date(b.updatedAt.value).getTime() -
        new Date(a.updatedAt.value).getTime()
    );

    return challengeProgressList;
  }

  private async getParticipantList(challengeList: Challenge[]) {
    const updatedChallengeList: ChallengeDTO[] = [];
    for (const challenge of challengeList) {
      const id = challenge.id.value;
      const participantList =
        await this.getParticipantListByChallengeUseCase.execute({
          challengeId: id,
        });
      const participant = participantList || [];
      updatedChallengeList.push(ChallengeMap.toDTO(challenge, participant));
    }

    return updatedChallengeList;
  }

  private async publishChallengeRemianders(challengeList: ChallengeDTO[]) {
    for (const challenge of challengeList) {
      // eslint-disable-next-line no-console
      console.log('challenge', challenge);
      const { challengeParticipantType } = challenge;
      if (challengeParticipantType === 'GROUP') {
        await this.notifyGroupChallenge(challenge);
      }
      if (challengeParticipantType === 'INDIVIDUAL') {
        await this.notifyIndividualChallenge(challenge);
      }
    }
  }

  private async notifyGroupChallenge(challenge: ChallengeDTO) {
    if (!challenge.participants) return;
    for (const participant of challenge.participants) {
      if (this.shouldSendProgressRemiander(challenge)) {
        try {
          await this.sendChallengeProgressRemiander(
            participant.userId,
            challenge
          );
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log('GROUP sendChallengeProgressRemiander', challenge);
          // eslint-disable-next-line no-console
          console.log('error', error);
        }
      }
      if (this.shouldSendDeadlineRemiander(challenge)) {
        try {
          await this.sendChallengeDeadlineRemiander(
            participant.userId,
            challenge
          );
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log('GROUP shouldSendDeadlineRemiander', challenge);
          // eslint-disable-next-line no-console
          console.log('error', error);
        }
      }
    }
  }

  private async notifyIndividualChallenge(challenge: ChallengeDTO) {
    if (this.shouldSendProgressRemiander(challenge)) {
      try {
        await this.sendChallengeProgressRemiander(challenge.userId, challenge);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('INDIVIDUAL sendChallengeProgressRemiander', challenge);
        // eslint-disable-next-line no-console
        console.log('error', error);
      }
    }
    if (this.shouldSendDeadlineRemiander(challenge)) {
      try {
        await this.sendChallengeDeadlineRemiander(challenge.userId, challenge);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('INDIVIDUAL sendChallengeDeadlineRemiander', challenge);
        // eslint-disable-next-line no-console
        console.log('error', error);
      }
    }
  }

  private shouldSendDeadlineRemiander(challenge: ChallengeDTO) {
    const { isRemainderActive, endDate } = challenge;
    if (!isRemainderActive) return false;

    return this.isWithin5Days(endDate);
  }

  private shouldSendProgressRemiander(challenge: ChallengeDTO) {
    const { isRemainderActive, remainderType } = challenge;
    if (!isRemainderActive) return false;
    if (remainderType === 'MONTHLY') {
      return this.isFirstDayOfMonth();
    } else if (remainderType === 'WEEKLY') {
      return this.isMonday();
    }

    return true;
  }

  private async sendChallengeProgressRemiander(
    participantId: string,
    challenge: ChallengeDTO
  ) {
    const { name } = challenge;
    const dto = {
      userId: participantId,
      title: 'Actualiza el Progreso de tu Reto 📈',
      message: `Te recordamos llenar tu progreso del reto: ${name}!`,
      notificationType: 'CTA',
      callToAction: '../challenges/?view=HOME',
      priority: 'LOW',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }

  private async sendChallengeDeadlineRemiander(
    participantId: string,
    challenge: ChallengeDTO
  ) {
    const { name } = challenge;
    const dto = {
      userId: participantId,
      title: 'Recordatorio de Reto 🚨',
      message: `El reto ${name} esta proximo a terminar!`,
      notificationType: 'CTA',
      callToAction: '../challenges/?view=HOME',
      priority: 'LOW',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }

  private isFirstDayOfMonth(): boolean {
    const date = new Date();

    // Returns true if it's the first day of the month
    return date.getDate() === 1;
  }

  private isMonday(): boolean {
    const date = new Date();

    // Sunday is 0, Monday is 1, ..., Saturday is 6
    return date.getDay() === 1;
  }

  private isWithin5Days(targetDateISO: string): boolean {
    const targetDate = new Date(targetDateISO);
    const currentDate = new Date();
    // Check if the current date is before the target date
    if (currentDate < targetDate) {
      const timeDifference = targetDate.getTime() - currentDate.getTime();
      const daysDifference = timeDifference / (1000 * 60 * 60 * 24);
      // Check if the current date is within 5 days of reaching the target date
      if (daysDifference <= 5) {
        return true;
      }
    }

    return false;
  }
}
