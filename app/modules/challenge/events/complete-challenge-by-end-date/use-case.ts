import { DateValueObject } from '../../../../shared/common-value-objects/date-value-object';
import { Result } from '../../../../shared/core/Result';
import { UseCase } from '../../../../shared/core/use-case';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';
import { Challenge } from '../../domain/challenge/Challenge';
import { ChallengeStatus } from '../../domain/challenge/status';
import { LeaderboardParticipant } from '../../domain/leaderboard/LeaderboardParticipant';
import { Participant } from '../../domain/participant/Participant';
import { ChallengeMap } from '../../mappers/challenge-map';
import { ChallengeRepository } from '../../repo/challenge-repository';
import { GetUserChallengeByIdUseCase } from '../../use-case/challenge/get-by-id/use-case';
import { GetLeaderboardByChallengeIdUseCase } from '../../use-case/leader-board/get-leader-board-by-challenge-id/use-case';
import { SetWinnerUseCase } from '../../use-case/participant/set-winner/use-case';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class CompleteChallengeByEndDateUseCase
  implements UseCase<void, Promise<void>>
{
  private challengeWinner: LeaderboardParticipant | undefined;

  constructor(
    private getUserChallengeByIdUseCase: GetUserChallengeByIdUseCase,
    private challengeRepository: ChallengeRepository,
    private setWinnerUseCase: SetWinnerUseCase,
    private getLeaderboardByChallengeIdUseCase: GetLeaderboardByChallengeIdUseCase,
    private eventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute() {
    const { endDate, status } = this.getChallengeQueryParams();
    const challengeList = await this.getChallengeList(endDate, status);
    await this.updateChallengeList(challengeList);
  }

  private getChallengeQueryParams() {
    const today = new Date();
    // Add 1 day (24 hours) to the date
    const nextDay = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    const endDate = new Date(nextDay.toISOString().split('T')[0]).toISOString();
    const endDateOrError = DateValueObject.create('endDate', endDate);
    const statusOrError = ChallengeStatus.create('ACTIVE');
    const dbCombine = Result.combine([endDateOrError, statusOrError]);
    if (dbCombine.isFailure) throw dbCombine.getErrorValue();

    return {
      endDate: endDateOrError.getValue(),
      status: statusOrError.getValue(),
    };
  }

  private async getChallengeList(
    endDate: DateValueObject,
    status: ChallengeStatus
  ) {
    const challengeList = await this.challengeRepository.getChallengesByEndDate(
      endDate,
      status
    );
    const challengeWithParticipantList = [];
    for (const challenge of challengeList || []) {
      const result = await this.getUserChallengeByIdUseCase.execute({
        challengeId: challenge.id.value,
      });
      if (result) challengeWithParticipantList.push(result);
    }

    return challengeWithParticipantList;
  }

  private async updateChallengeList(
    challengeWithParticipantList: {
      challenge: Challenge;
      challengeParticipantList: Participant[];
    }[]
  ) {
    for (const challengeWithParticipant of challengeWithParticipantList) {
      const { challenge } = challengeWithParticipant;
      // eslint-disable-next-line no-console
      console.log('Challenge To Update', ChallengeMap.toDTO(challenge));
      challenge.complete(); // eslint-disable-next-line no-console
      console.log('Challenge Updated', ChallengeMap.toDTO(challenge));
      await this.challengeRepository.updateChallenge(challenge);
      await this.updateChallengeWinner(challenge.id.value);
      await this.sendParticipantNotification(challengeWithParticipant);
    }
  }

  private async updateChallengeWinner(challengeId: string) {
    const winner = await this.getCurrentWinner(challengeId);
    winner && (await this.setChallengeWinner(winner.userId.value, challengeId));
  }

  private async getCurrentWinner(challengeId: string) {
    const { winner } = await this.getLeaderboardByChallengeIdUseCase.execute({
      challengeId,
    });

    return winner;
  }

  private async setChallengeWinner(userId: string, challengeId: string) {
    await this.setWinnerUseCase.execute({
      userId,
      challengeId,
    });
  }

  private async sendParticipantNotification({
    challenge,
    challengeParticipantList,
  }: {
    challenge: Challenge;
    challengeParticipantList: Participant[];
  }) {
    try {
      for (const participant of challengeParticipantList) {
        const {
          userId: { value: userId },
        } = participant;
        await this.notifyParticipant(userId, challenge);
        if (this.isParticipantWinner(userId)) {
          await this.notifyWinner(userId, challenge);
        }
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Error notifying user:', error);
    }
  }

  private async notifyParticipant(participantId: string, challenge: Challenge) {
    const {
      name: { value: name },
    } = challenge;
    if (!this.shouldNotificationBeSend(challenge, participantId)) return;
    const dto = {
      userId: participantId,
      title: 'Reto Finalizado',
      message: `Te notificamos que el reto ${name} ha finalizado`,
      notificationType: 'CTA',
      callToAction: '../challenges/?view=COMPLETED',
      priority: 'LOW',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }

  private async notifyWinner(participantId: string, challenge: Challenge) {
    const {
      name: { value: name },
    } = challenge;
    if (!this.shouldNotificationBeSend(challenge, participantId)) return;
    const dto = {
      userId: participantId,
      title: 'Felicidades 🎉',
      message: `Eres el ganador del reto ${name}!!`,
      notificationType: 'CTA',
      callToAction: '../challenges/?view=COMPLETED',
      priority: 'LOW',
    };
    await this.eventService.publishEvent({ body: JSON.stringify(dto) });
  }

  private isParticipantWinner(participantId: string) {
    if (!this.challengeWinner) return false;

    return this.challengeWinner.userId.value === participantId;
  }

  private shouldNotificationBeSend(
    challenge: Challenge,
    participantId: string
  ) {
    const {
      userId: { value: owner },
    } = challenge;

    return owner === participantId ? false : true;
  }
}
