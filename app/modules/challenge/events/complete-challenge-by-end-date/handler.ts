import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import AWS from 'aws-sdk';
import { BaseHandler } from '../../../../shared/infra/handler';
import { ChallengeRepository } from '../../repo/challenge-repository';
import { CompleteChallengeByEndDateUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { ParticipantRepository } from '../../repo/participant-repository';
import { SetWinnerUseCase } from '../../use-case/participant/set-winner/use-case';
import { GetLeaderboardByChallengeIdUseCase } from '../../use-case/leader-board/get-leader-board-by-challenge-id/use-case';
import { ChallengeProgressRepository } from '../../repo/challenge-progress-repository';
import { GetParticipantListByChallengeUseCase } from '../../use-case/participant/get-participant-list-by-challenge/use-case';
import { GetUserChallengeByIdUseCase } from '../../use-case/challenge/get-by-id/use-case';
import { GetUserChallengeProgressByIdUseCase } from '../../use-case/challenge-progress/get-challenge-progress-by-id/use-case';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';

class Lambda extends BaseHandler implements LambdaInterface {
  private completeChallengeByEndDateUseCase: CompleteChallengeByEndDateUseCase;

  private init() {
    const challengeRepository = new ChallengeRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_TABLE_NAME || ''
    );
    const participantRepository = new ParticipantRepository(
      new AWS.DynamoDB(),
      process.env.PARTICIPANT_TABLE_NAME || ''
    );
    const setWinnerUseCase = new SetWinnerUseCase(participantRepository);
    const challengeProgressRepository = new ChallengeProgressRepository(
      new AWS.DynamoDB(),
      process.env.CHALLENGE_PROGRESS_TABLE_NAME || ''
    );
    const getParticipantListByUserUseCase =
      new GetParticipantListByChallengeUseCase(participantRepository);
    const getUserChallengeByIdUseCase = new GetUserChallengeByIdUseCase(
      challengeRepository,
      getParticipantListByUserUseCase
    );
    const getUserChallengeProgressByIdUseCase =
      new GetUserChallengeProgressByIdUseCase(challengeProgressRepository);
    const getLeaderboardByChallengeIdUseCase =
      new GetLeaderboardByChallengeIdUseCase(
        getUserChallengeByIdUseCase,
        getUserChallengeProgressByIdUseCase
      );
    const eventService = new EventService(
      process.env.SQS_URL || '',
      'notify-automatic-challenge-closed'
    );
    this.completeChallengeByEndDateUseCase =
      new CompleteChallengeByEndDateUseCase(
        getUserChallengeByIdUseCase,
        challengeRepository,
        setWinnerUseCase,
        getLeaderboardByChallengeIdUseCase,
        eventService
      );
  }

  @LogHandlerEvent
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const result = await this.completeChallengeByEndDateUseCase.execute();

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
