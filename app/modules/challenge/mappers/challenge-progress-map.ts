import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../shared/common-value-objects/simple-text-value-object';
import { StatusValueObject } from '../../../shared/common-value-objects/status-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { ChallengeProgress } from '../domain/challenge-progress/ChallengeProgress';
import { Evidence } from '../domain/challenge-progress/evidence';
import { ChallengeProgressType } from '../domain/challenge-progress/type';
import { ChallengeProgressDTO } from '../dto/challenge-progress';

export class ChallengeProgressMap {
  public static toDTO(
    challengeProgress: ChallengeProgress
  ): ChallengeProgressDTO {
    return {
      id: challengeProgress.id.value,
      challengeId: challengeProgress.challengeId.value,
      userId: challengeProgress.userId.value,
      userName: challengeProgress.userName.value,
      value: challengeProgress.value.value,
      evidence: challengeProgress.evidence.value,
      challengeProgressStatus: challengeProgress.challengeProgressStatus.value,
      challengeProgressType: challengeProgress.challengeProgressType.value,
      updatedAt: challengeProgress.updatedAt.value,
      createdAt: challengeProgress.createdAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): ChallengeProgress {
    const id = UUIDValueObject.create('id', raw.id);
    const challengeId = UUIDValueObject.create('challengeId', raw.challengeId);
    const userId = UUIDValueObject.create('userId', raw.userId);
    const userName = SimpleTextValueObject.create('userName', raw.userName);
    const value = NumberValueObject.create('value', raw.value);
    const evidence = Evidence.create('evidence', raw.evidence);
    const challengeProgressStatus = StatusValueObject.create(
      'challengeProgressStatus',
      raw.challengeProgressStatus
    );
    const challengeProgressType = ChallengeProgressType.create(
      raw.challengeProgressType
    );
    const updatedAt = DateValueObject.create('updatedAt', raw.updatedAt);
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const dbCombined = Result.combine([
      id,
      challengeId,
      userId,
      userName,
      value,
      evidence,
      challengeProgressStatus,
      challengeProgressType,
      updatedAt,
      createdAt,
    ]);
    if (dbCombined.isFailure) throw dbCombined.getErrorValue();
    const participant = ChallengeProgress.create({
      id: id.getValue(),
      challengeId: challengeId.getValue(),
      userId: userId.getValue(),
      userName: userName.getValue(),
      value: value.getValue(),
      evidence: evidence.getValue(),
      challengeProgressStatus: challengeProgressStatus.getValue(),
      challengeProgressType: challengeProgressType.getValue(),
      updatedAt: updatedAt.getValue(),
      createdAt: createdAt.getValue(),
    });
    if (participant.isFailure) throw participant.getErrorValue();

    return participant.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toPersistence(challengeProgress: ChallengeProgress): any {
    return {
      id: challengeProgress.id.value,
      challengeId: challengeProgress.challengeId.value,
      userId: challengeProgress.userId.value,
      userName: challengeProgress.userName.value,
      value: challengeProgress.value.value,
      evidence: challengeProgress.evidence.value,
      challengeProgressStatus: challengeProgress.challengeProgressStatus.value,
      challengeProgressType: challengeProgress.challengeProgressType.value,
      updatedAt: challengeProgress.updatedAt.value,
      createdAt: challengeProgress.createdAt.value,
    };
  }
}
