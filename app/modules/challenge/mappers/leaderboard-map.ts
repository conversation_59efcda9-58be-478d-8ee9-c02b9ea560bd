import { Leaderboard } from '../domain/leaderboard/Leaderboard';
import { LeaderboardParticipant } from '../domain/leaderboard/LeaderboardParticipant';
import { LeaderboardDTO } from '../dto/leaderboard-dto';
import { LeaderboardParticipantMap } from './leaderboard-participant-map';

export class LeaderboardMap {
  public static toDTO(leaderboard: Leaderboard): LeaderboardDTO {
    return {
      winner:
        leaderboard.winner &&
        LeaderboardParticipantMap.toDTO(leaderboard.winner),
      leaderboard: leaderboard.leaderboard.map((participant) =>
        LeaderboardParticipantMap.toDTO(participant)
      ),
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): Leaderboard {
    const winner: LeaderboardParticipant | undefined =
      raw.winner && LeaderboardParticipantMap.toDomain(raw.winner);
    const leaderboard: LeaderboardParticipant[] = raw.leaderboard.map(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (participant: any) => LeaderboardParticipantMap.toDomain(participant)
    );
    const leaderboardparticipant = Leaderboard.create({
      winner,
      leaderboard,
    });
    if (leaderboardparticipant.isFailure)
      throw leaderboardparticipant.getErrorValue();

    return leaderboardparticipant.getValue();
  }
}
