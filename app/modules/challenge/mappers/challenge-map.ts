import { BooleanValueObject } from '../../../shared/common-value-objects/boolean-value-object';
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../shared/common-value-objects/simple-text-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { DateServices } from '../../../shared/services/date-services';
import { Challenge } from '../domain/challenge/Challenge';
import { Participant } from '../domain/participant/Participant';
import { ChallengeStatus } from '../domain/challenge/status';
import { ChallengesParticipantType } from '../domain/challenge/participant-type';
import { GoalType } from '../domain/challenge/goal-type';
import { GoalUnit } from '../domain/challenge/goal-unit';
import { FrequencyType } from '../domain/challenge/frequency-type';
import { ChallengeDTO } from '../dto/challenge-dto';
import { ParticipantMap } from './participant-map';
import { ProgressType } from '../domain/challenge/progress-type';

export class ChallengeMap {
  public static toDTO(
    challenge: Challenge,
    participantList?: Participant[]
  ): ChallengeDTO {
    const {
      startDate: { value: startDate },
      endDate: { value: endDate },
    } = challenge;
    const timeProgress = DateServices.calculatePorcentualDateProgress(
      startDate,
      endDate
    );
    const calculateChallengeProgress = (challenge: Challenge): number => {
      if (challenge.goalType.value === 'ADD') {
        return 0;
      }
      if (challenge.goalType.value === 'REDUCE') {
        return 0;
      }

      return 0;
    };

    return {
      id: challenge.id.value,
      userId: challenge.userId.value,
      name: challenge.name.value,
      description: challenge.description.value,
      initialValue: challenge.initialValue.value,
      startDate: challenge.startDate.value,
      endDate: challenge.endDate.value,
      goal: challenge.goal.value,
      goalUnit: challenge.goalUnit.value,
      goalType: challenge.goalType.value,
      isRemainderActive: challenge.isRemainderActive.value,
      isEvidenceRequired: challenge.isEvidenceRequired.value,
      remainderType: challenge.remainderType.value,
      progressType: challenge.progressType.value,
      reward: challenge.reward.value,
      challengeStatus: challenge.challengeStatus.value,
      participants: participantList?.map(ParticipantMap.toDTO) || [],
      challengeParticipantType: challenge.challengeParticipantType.value,
      timeProgress,
      challengeProgress: calculateChallengeProgress(challenge),
      updatedAt: challenge.updatedAt.value,
      createdAt: challenge.createdAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): Challenge {
    const id = UUIDValueObject.create('id', raw.id);
    const userId = UUIDValueObject.create('userId', raw.userId);
    const name = SimpleTextValueObject.create('name', raw.name);
    const description = SimpleTextValueObject.create(
      'description',
      raw.description
    );
    const initialValue = NumberValueObject.create(
      'initialValue',
      raw.initialValue
    );
    const startDate = DateValueObject.create('startDate', raw.startDate);
    const endDate = DateValueObject.create('endDate', raw.endDate);
    const goal = NumberValueObject.create('goal', raw.goal);
    const goalUnit = GoalUnit.create(raw.goalUnit);
    const goalType = GoalType.create(raw.goalType);
    const isRemainderActive = BooleanValueObject.create(
      'isRemainderActive',
      raw.isRemainderActive
    );
    const isEvidenceRequired = BooleanValueObject.create(
      'isEvidenceRequired',
      raw.isEvidenceRequired
    );
    const remainderType = FrequencyType.create(raw.remainderType);
    const progressType = ProgressType.create(raw.progressType);
    const reward = SimpleTextValueObject.create('reward', raw.reward);
    const challengeStatus = ChallengeStatus.create(raw.challengeStatus);
    const challengeParticipantType = ChallengesParticipantType.create(
      raw.challengeParticipantType
    );
    const updatedAt = DateValueObject.create('updatedAt', raw.updatedAt);
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const dbCombined = Result.combine([
      id,
      userId,
      name,
      description,
      initialValue,
      startDate,
      endDate,
      goal,
      goalUnit,
      goalType,
      isRemainderActive,
      isEvidenceRequired,
      remainderType,
      progressType,
      reward,
      challengeStatus,
      challengeParticipantType,
      updatedAt,
      createdAt,
    ]);
    if (dbCombined.isFailure) throw dbCombined.getErrorValue();
    const challenge = Challenge.create({
      id: id.getValue(),
      userId: userId.getValue(),
      name: name.getValue(),
      description: description.getValue(),
      initialValue: initialValue.getValue(),
      startDate: startDate.getValue(),
      endDate: endDate.getValue(),
      goal: goal.getValue(),
      goalUnit: goalUnit.getValue(),
      goalType: goalType.getValue(),
      isRemainderActive: isRemainderActive.getValue(),
      isEvidenceRequired: isEvidenceRequired.getValue(),
      remainderType: remainderType.getValue(),
      progressType: progressType.getValue(),
      reward: reward.getValue(),
      challengeStatus: challengeStatus.getValue(),
      challengeParticipantType: challengeParticipantType.getValue(),
      updatedAt: updatedAt.getValue(),
      createdAt: createdAt.getValue(),
    });
    if (challenge.isFailure) throw challenge.getErrorValue();

    return challenge.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toPersistence(challenge: Challenge): any {
    return {
      id: challenge.id.value,
      userId: challenge.userId.value,
      name: challenge.name.value,
      description: challenge.description.value,
      initialValue: challenge.initialValue.value,
      startDate: challenge.startDate.value,
      endDate: challenge.endDate.value,
      goal: challenge.goal.value,
      goalUnit: challenge.goalUnit.value,
      goalType: challenge.goalType.value,
      isRemainderActive: challenge.isRemainderActive.value,
      isEvidenceRequired: challenge.isEvidenceRequired.value,
      remainderType: challenge.remainderType.value,
      progressType: challenge.progressType.value,
      reward: challenge.reward.value,
      challengeStatus: challenge.challengeStatus.value,
      challengeParticipantType: challenge.challengeParticipantType.value,
      updatedAt: challenge.updatedAt.value,
      createdAt: challenge.createdAt.value,
    };
  }
}
