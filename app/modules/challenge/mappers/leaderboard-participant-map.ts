import { BooleanValueObject } from '../../../shared/common-value-objects/boolean-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../shared/common-value-objects/simple-text-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { LeaderboardParticipant } from '../domain/leaderboard/LeaderboardParticipant';
import { LeaderboardParticipantDTO } from '../dto/leaderboard-participant-dto';

export class LeaderboardParticipantMap {
  public static toDTO(
    leaderboardParticipant: LeaderboardParticipant
  ): LeaderboardParticipantDTO {
    return {
      userId: leaderboardParticipant.userId.value,
      userName: leaderboardParticipant.userName.value,
      userEmail: leaderboardParticipant.userEmail.value,
      initialProgress: leaderboardParticipant.initialProgress.value,
      latestProgress: leaderboardParticipant.latestProgress.value,
      overallProgress: leaderboardParticipant.overallProgress.value,
      hasAchievedGoal: leaderboardParticipant.hasAchievedGoal.value,
      isWinner: leaderboardParticipant.isWinner.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): LeaderboardParticipant {
    const userId = UUIDValueObject.create('userId', raw.userId);
    const userName = SimpleTextValueObject.create('userName', raw.userName);
    const userEmail = SimpleTextValueObject.create('userEmail', raw.userEmail);
    const initialProgress = NumberValueObject.create(
      'initialProgress',
      raw.initialProgress
    );
    const latestProgress = NumberValueObject.create(
      'latestProgress',
      raw.latestProgress
    );
    const overallProgress = NumberValueObject.create(
      'overallProgress',
      raw.overallProgress
    );
    const hasAchievedGoal = BooleanValueObject.create(
      'hasAchievedGoal',
      raw.hasAchievedGoal
    );
    const isWinner = BooleanValueObject.create('isWinner', raw.isWinner);
    const dbCombined = Result.combine([
      userId,
      userName,
      userEmail,
      initialProgress,
      latestProgress,
      overallProgress,
      hasAchievedGoal,
    ]);
    if (dbCombined.isFailure) throw dbCombined.getErrorValue();
    const leaderBoardParticipant = LeaderboardParticipant.create({
      userId: userId.getValue(),
      userName: userName.getValue(),
      userEmail: userEmail.getValue(),
      initialProgress: initialProgress.getValue(),
      latestProgress: latestProgress.getValue(),
      overallProgress: overallProgress.getValue(),
      hasAchievedGoal: hasAchievedGoal.getValue(),
      isWinner: isWinner.getValue(),
    });
    if (leaderBoardParticipant.isFailure)
      throw leaderBoardParticipant.getErrorValue();

    return leaderBoardParticipant.getValue();
  }
}
