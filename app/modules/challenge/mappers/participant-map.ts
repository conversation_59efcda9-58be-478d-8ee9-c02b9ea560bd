import { BooleanValueObject } from '../../../shared/common-value-objects/boolean-value-object';
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { SimpleTextValueObject } from '../../../shared/common-value-objects/simple-text-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { Participant } from '../domain/participant/Participant';
import { ParticipantStatus } from '../domain/participant/participant-status';
import { ParticipantDTO } from '../dto/participant-dto';

export class ParticipantMap {
  public static toDTO(participant: Participant): ParticipantDTO {
    return {
      id: participant.id.value,
      userId: participant.userId.value,
      challengeId: participant.challengeId.value,
      userName: participant.userName.value,
      userEmail: participant.userEmail.value,
      initialValue: participant.initialValue.value,
      participantStatus: participant.participantStatus.value,
      isWinner: participant.isWinner.value,
      updatedAt: participant.updatedAt.value,
      createdAt: participant.createdAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): Participant {
    const id = UUIDValueObject.create('id', raw.id);
    const userId = UUIDValueObject.create('userId', raw.userId);
    const challengeId = UUIDValueObject.create('challengeId', raw.challengeId);
    const userName = SimpleTextValueObject.create('userName', raw.userName);
    const userEmail = SimpleTextValueObject.create('userEmail', raw.userEmail);
    const initialValue = NumberValueObject.create(
      'initialValue',
      raw.initialValue
    );
    const participantStatus = ParticipantStatus.create(raw.participantStatus);
    const isWinner = BooleanValueObject.create('isWinner', raw.isWinner);
    const updatedAt = DateValueObject.create('updatedAt', raw.updatedAt);
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const dbCombined = Result.combine([
      id,
      userId,
      challengeId,
      userName,
      userEmail,
      initialValue,
      participantStatus,
      updatedAt,
      createdAt,
    ]);
    if (dbCombined.isFailure) throw dbCombined.getErrorValue();
    const participant = Participant.create({
      id: id.getValue(),
      userId: userId.getValue(),
      challengeId: challengeId.getValue(),
      userName: userName.getValue(),
      userEmail: userEmail.getValue(),
      initialValue: initialValue.getValue(),
      participantStatus: participantStatus.getValue(),
      isWinner: isWinner.getValue(),
      updatedAt: updatedAt.getValue(),
      createdAt: createdAt.getValue(),
    });
    if (participant.isFailure) throw participant.getErrorValue();

    return participant.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toPersistence(participant: Participant): any {
    return {
      id: participant.id.value,
      userId: participant.userId.value,
      challengeId: participant.challengeId.value,
      userName: participant.userName.value,
      userEmail: participant.userEmail.value,
      initialValue: participant.initialValue.value,
      participantStatus: participant.participantStatus.value,
      isWinner: participant.isWinner.value,
      updatedAt: participant.updatedAt.value,
      createdAt: participant.createdAt.value,
    };
  }
}
