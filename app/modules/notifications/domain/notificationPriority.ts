import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

export type NotificationPriorityOptions = 'HIGH' | 'LOW';
interface INotificationPriorityProps {
  value: string;
}
export class NotificationPriority extends ValueObject<INotificationPriorityProps> {
  private constructor(props: INotificationPriorityProps) {
    super(props);
  }

  get value(): NotificationPriorityOptions {
    return this.props.value as NotificationPriorityOptions;
  }

  updateValue(update: Partial<INotificationPriorityProps>): void {
    if (update.value) {
      this.props.value = update.value;
    }
  }

  private static isStatusValid(status: string) {
    return status && (status === 'HIGH' || status === 'LOW');
  }

  public static create(
    status: NotificationPriorityOptions | string
  ): Result<NotificationPriority> {
    if (!this.isStatusValid(status)) {
      return Result.fail<NotificationPriority>('Invalid Notification Priority');
    }

    return Result.ok<NotificationPriority>(
      new NotificationPriority({ value: status })
    );
  }
}
