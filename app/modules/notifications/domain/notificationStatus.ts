import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

export type NotificationStatusOptions = 'ACTIVE' | 'ARCHIVED';
interface INotificationStatusProps {
  value: string;
}
export class NotificationStatus extends ValueObject<INotificationStatusProps> {
  private constructor(props: INotificationStatusProps) {
    super(props);
  }

  get value(): NotificationStatusOptions {
    return this.props.value as NotificationStatusOptions;
  }

  updateValue(update: Partial<INotificationStatusProps>): void {
    if (update.value) {
      this.props.value = update.value;
    }
  }

  private static isStatusValid(status: string) {
    return status && (status === 'ACTIVE' || status === 'ARCHIVED');
  }

  public static create(
    status: NotificationStatusOptions | string
  ): Result<NotificationStatus> {
    if (!this.isStatusValid(status)) {
      return Result.fail<NotificationStatus>('Invalid Notification Status');
    }

    return Result.ok<NotificationStatus>(
      new NotificationStatus({ value: status })
    );
  }
}
