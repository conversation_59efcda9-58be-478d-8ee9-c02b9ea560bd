import { ValueObject } from '../../../shared/domain/ValueObject';
import { Result } from '../../../shared/core/Result';

export type NotificationTypeOptions = 'READ_ONLY' | 'CTA';
interface INotificationTypeProps {
  value: string;
}
export class NotificationType extends ValueObject<INotificationTypeProps> {
  private constructor(props: INotificationTypeProps) {
    super(props);
  }

  get value(): NotificationTypeOptions {
    return this.props.value as NotificationTypeOptions;
  }

  updateValue(update: Partial<INotificationTypeProps>): void {
    if (update.value) {
      this.props.value = update.value;
    }
  }

  private static isStatusValid(status: string) {
    return status && (status === 'READ_ONLY' || status === 'CTA');
  }

  public static create(
    status: NotificationTypeOptions | string
  ): Result<NotificationType> {
    if (!this.isStatusValid(status)) {
      return Result.fail<NotificationType>('Invalid Notification Type');
    }

    return Result.ok<NotificationType>(new NotificationType({ value: status }));
  }
}
