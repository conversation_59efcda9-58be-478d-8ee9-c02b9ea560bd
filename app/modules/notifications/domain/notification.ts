import { Result } from '../../../shared/core/Result';
import { BoolT } from '../../../shared/domain/BoolT';
import { ISODate } from '../../../shared/domain/ISODate';
import { ID } from '../../../shared/domain/Id';
import { Text } from '../../../shared/domain/Text';
import { ValueObject } from '../../../shared/domain/ValueObject';
import { NotificationPriority } from './notificationPriority';
import { NotificationStatus } from './notificationStatus';
import { NotificationType } from './notificationType';

interface INotificationsProps {
  id?: ID;
  userId: ID;
  title: Text;
  message: Text;
  notificationType: NotificationType;
  callToAction?: Text;
  notificationStatus: NotificationStatus;
  priority: NotificationPriority;
  isRead: BoolT;
  createdAt: ISODate;
}
export class Notification extends ValueObject<INotificationsProps> {
  private constructor(props: INotificationsProps) {
    super(props);
  }

  getId(): ID | undefined {
    return this.props.id;
  }

  getUserId(): ID {
    return this.props.userId;
  }

  getTitle(): Text {
    return this.props.title;
  }

  getMessage(): Text {
    return this.props.message;
  }

  getNotificationType(): NotificationType {
    return this.props.notificationType;
  }

  getCallToAction(): Text | undefined {
    return this.props.callToAction;
  }

  getNotificationStatus(): NotificationStatus {
    return this.props.notificationStatus;
  }

  getPriority(): NotificationPriority {
    return this.props.priority;
  }

  getIsRead(): BoolT {
    return this.props.isRead;
  }

  getCreatedAt(): ISODate {
    return this.props.createdAt;
  }

  archiveNotification() {
    const status = NotificationStatus.create('ARCHIVED');
    const dtoCombine = Result.combine([status]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.notificationStatus = status.getValue();
  }

  updateNotificationData(update: Partial<INotificationsProps>): void {
    if (update.id) {
      const id = ID.create(update.id.value);
      if (id.isFailure) throw id.getErrorValue();
      this.props.id = id.getValue();
    }
    if (update.userId) {
      const userId = ID.create(update.userId.value);
      if (userId.isFailure) throw userId.getErrorValue();
      this.props.userId = userId.getValue();
    }
    if (update.title) {
      const title = Text.create(update.title.value);
      if (title.isFailure) throw title.getErrorValue();
      this.props.title = title.getValue();
    }
    if (update.message) {
      const message = Text.create(update.message.value);
      if (message.isFailure) throw message.getErrorValue();
      this.props.message = message.getValue();
    }
    if (update.notificationType) {
      this.props.notificationType = update.notificationType;
    }
    if (update.callToAction) {
      const callToAction = Text.create(update.callToAction.value);
      if (callToAction.isFailure) throw callToAction.getErrorValue();
      this.props.callToAction = callToAction.getValue();
    }
    if (update.priority) {
      this.props.priority = update.priority;
    }
    if (update.isRead) {
      this.props.isRead = update.isRead;
    }
  }

  public markNotificationAsRead() {
    this.props.isRead = BoolT.create(true).getValue();
  }

  public static create(props: INotificationsProps) {
    const data = new Notification({
      ...props,
    });

    return Result.ok<Notification>(data);
  }
}
