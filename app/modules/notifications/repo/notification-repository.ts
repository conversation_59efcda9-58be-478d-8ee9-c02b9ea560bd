import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { Notification } from '../domain/notification';
import { NotificationMap } from '../mappers/notification-map';
import { ID } from '../../../shared/domain/Id';
import { NotificationStatus } from '../domain/notificationStatus';
import { DynamoServices } from '../../../shared/services/dynamo-services';

export class NotificationRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createNotification(
    notification: Notification
  ): Promise<Notification> {
    const rawNotification = NotificationMap.toPersistence(notification);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(rawNotification),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line
    console.log('Repository result', JSON.stringify(result));

    return notification;
  }

  public async getNotificationById(
    notificationId: ID
  ): Promise<Notification | null> {
    const id = notificationId.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line
    console.log('Repository result', JSON.stringify(result));

    return NotificationMap.toDomain(this.unmarshallElement(result.Item));
  }

  public async getNotificationsByUserId(
    userId: ID,
    notificationStatus: NotificationStatus
  ): Promise<Notification[]> {
    const userIdValue = userId.value;
    const status = notificationStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'userId-notificationStatus-index',
      KeyConditionExpression:
        'userId = :userId AND notificationStatus = :notificationStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line
        ':userId': { S: userIdValue },   
        // eslint-disable-next-line
        ':notificationStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => NotificationMap.toDomain(item));
  }

  public async updateNotification(
    notification: Notification
  ): Promise<Notification | null> {
    const id = notification.getId()?.value || '';
    const data = NotificationMap.toPersistence(notification);
    delete data.id;
    const params = {
      TableName: this.tableName,
      Key: this.marshallElement({
        id,
      }),
      ReturnValues: 'ALL_NEW',
      UpdateExpression:
        'set ' +
        Object.keys(data)
          .map((k) => `#${k} = :${k}`)
          .join(', '),
      ExpressionAttributeNames: Object.entries(data).reduce(
        (acc, cur) => ({ ...acc, [`#${cur[0]}`]: cur[0] }),
        {}
      ),
      ExpressionAttributeValues: this.marshallElement(
        Object.entries(data).reduce(
          (acc, cur) => ({ ...acc, [`:${cur[0]}`]: cur[1] }),
          {}
        )
      ),
    };
    const result = await this.dynamodb.updateItem(params).promise();
    if (!result.Attributes) throw 'Query return values error';
    // eslint-disable-next-line
    console.log('Repository result', JSON.stringify(result));

    return NotificationMap.toDomain(this.unmarshallElement(result.Attributes));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
