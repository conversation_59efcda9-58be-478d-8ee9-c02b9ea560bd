import { Result } from '../../../shared/core/Result';
import { ID } from '../../../shared/domain/Id';
import { Text } from '../../../shared/domain/Text';
import { ISODate } from '../../../shared/domain/ISODate';
import { Notification } from '../domain/notification';
import { NotificationDTO } from '../dto/notification-dto';
import { NotificationType } from '../domain/notificationType';
import { NotificationPriority } from '../domain/notificationPriority';
import { BoolT } from '../../../shared/domain/BoolT';
import { NotificationStatus } from '../domain/notificationStatus';

export class NotificationMap {
  public static toDTO(notification: Notification): NotificationDTO {
    return {
      id: notification.getId()?.value,
      userId: notification.getUserId().value,
      title: notification.getTitle().value,
      message: notification.getMessage().value,
      notificationType: notification.getNotificationType().value,
      callToAction: notification.getCallToAction()?.value,
      notificationStatus: notification.getNotificationStatus().value,
      priority: notification.getPriority().value,
      isRead: notification.getIsRead().value,
      createdAt: notification.getCreatedAt().value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): Notification {
    const id = ID.create(raw.id);
    const userId = ID.create(raw.userId);
    const title = Text.create(raw.title);
    const message = Text.create(raw.message);
    const notificationType = NotificationType.create(raw.notificationType);
    const callToAction = Text.create(raw.callToAction);
    const notificationStatus = NotificationStatus.create(
      raw.notificationStatus ?? 'ACTIVE'
    );
    const priority = NotificationPriority.create(raw.priority);
    const isRead = BoolT.create(raw.isRead ?? false);
    const createdAt = ISODate.create(raw.createdAt);
    const dtoCombine = Result.combine([
      id,
      userId,
      title,
      message,
      notificationType,
      callToAction,
      notificationStatus,
      priority,
      isRead,
      createdAt,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const notification = Notification.create({
      id: id.getValue(),
      userId: userId.getValue(),
      title: title.getValue(),
      message: message.getValue(),
      notificationType: notificationType.getValue(),
      callToAction: callToAction.getValue(),
      notificationStatus: notificationStatus.getValue(),
      priority: priority.getValue(),
      isRead: isRead.getValue(),
      createdAt: createdAt.getValue(),
    });
    if (notification.isFailure) throw notification.getErrorValue();

    return notification.getValue();
  }

  // eslint-disable-next-line
  public static toPersistence(notification: Notification): any {
    return {
      id: notification.getId()?.value,
      userId: notification.getUserId().value,
      title: notification.getTitle().value,
      message: notification.getMessage().value,
      notificationType: notification.getNotificationType().value,
      callToAction: notification.getCallToAction()?.value,
      notificationStatus: notification.getNotificationStatus().value,
      priority: notification.getPriority().value,
      isRead: notification.getIsRead().value,
      createdAt: notification.getCreatedAt().value,
    };
  }
}
