import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { ArchiveNotificationDTO } from './dto';
import AWS from 'aws-sdk';
import { ArchiveNotificationUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { NotificationRepository } from '../../repo/notification-repository';
import { NotificationMap } from '../../mappers/notification-map';

class Lambda extends BaseHandler implements LambdaInterface {
  private archiveNotificationUseCase: ArchiveNotificationUseCase;

  private init() {
    const notificationRepository = new NotificationRepository(
      new AWS.DynamoDB(),
      process.env.NOTIFICATION_TABLE_NAME || ''
    );
    this.archiveNotificationUseCase = new ArchiveNotificationUseCase(
      notificationRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.archiveNotificationUseCase.execute(dto);

      return this.ok(NotificationMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): ArchiveNotificationDTO {
    const id = event.queryStringParameters?.id;
    if (!id) {
      throw 'Failed to parse request body';
    }

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
