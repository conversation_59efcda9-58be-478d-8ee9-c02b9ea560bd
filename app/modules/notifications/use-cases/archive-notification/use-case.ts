import { UseCase } from '../../../../shared/core/use-case';
import { ArchiveNotificationDTO } from './dto';
import { NotificationRepository } from '../../repo/notification-repository';
import { Notification } from '../../domain/notification';
import { ID } from '../../../../shared/domain/Id';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class ArchiveNotificationUseCase
  implements UseCase<ArchiveNotificationDTO, Promise<Notification>>
{
  constructor(private notificationRepository: NotificationRepository) {}

  @LogUseCaseDTO
  public async execute(dto: ArchiveNotificationDTO) {
    const id = this.mapDTOToId(dto);
    const existingNotification = await this.getExistingNotification(id);
    const updatedNotification = this.changeStatus(existingNotification);
    const result = await this.updateNotification(updatedNotification);

    return result;
  }

  private mapDTOToId(dto: ArchiveNotificationDTO): ID {
    const id = ID.create(dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingNotification(id: ID): Promise<Notification> {
    const notification = await this.notificationRepository.getNotificationById(
      id
    );
    if (!notification) throw 'Notification Not Found';

    return notification;
  }

  private changeStatus(notification: Notification): Notification {
    notification.archiveNotification();

    return notification;
  }

  private async updateNotification(
    notification: Notification
  ): Promise<Notification> {
    const result = await this.notificationRepository.updateNotification(
      notification
    );
    if (!result) throw 'Error Updating Notification';

    return result;
  }
}
