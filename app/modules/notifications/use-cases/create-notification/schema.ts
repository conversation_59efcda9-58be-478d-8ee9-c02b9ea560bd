import { object, string } from 'yup';

export const CreateNotificationSchema = object({
  userId: string().required('User Id is required'),
  title: string().required('Title for notification is required'),
  message: string().required('Message is required'),
  notificationType: string()
    .matches(/READ_ONLY|CTA/)
    .required('Notification Type must be "READ_ONLY" or "CTA"'),
  callToAction: string().notRequired(),
  priority: string()
    .matches(/HIGH|LOW/)
    .required('Notification Priority must be "HIGH" or "LOW"'),
});
