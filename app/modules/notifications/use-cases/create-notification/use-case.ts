import { UseCase } from '../../../../shared/core/use-case';
import { Notification } from '../../domain/notification';
import { NotificationMap } from '../../mappers/notification-map';
import { NotificationRepository } from '../../repo/notification-repository';
import { CreateNotificationDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class CreateNotificationUseCase
  implements UseCase<CreateNotificationDTO[], Promise<Notification[]>>
{
  constructor(private repository: NotificationRepository) {}

  @LogUseCaseDTO
  public async execute(dto: CreateNotificationDTO[]) {
    const notificationCreated = [];
    for (const element of dto) {
      // eslint-disable-next-line no-console
      console.log('notification dto', element);
      const notification = this.mapDTOtoDomain(element);
      // eslint-disable-next-line no-console
      console.log('notification domain', notification);
      await this.repository.createNotification(notification);
      notificationCreated.push(notification);
    }

    return notificationCreated;
  }

  private mapDTOtoDomain(dto: CreateNotificationDTO) {
    const notification = NotificationMap.toDomain(dto);

    return notification;
  }
}
