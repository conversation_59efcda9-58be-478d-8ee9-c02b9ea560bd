import { Context, SQSEvent } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { CreateNotificationUseCase } from './use-case';
import { NotificationMap } from '../../mappers/notification-map';
import { NotificationRepository } from '../../repo/notification-repository';
import { CreateNotificationDTO } from './dto';

class Lambda extends BaseHandler implements LambdaInterface {
  private createNotificationUseCase: CreateNotificationUseCase;

  private init() {
    const repo = new NotificationRepository(
      new AWS.DynamoDB(),
      process.env.NOTIFICATION_TABLE_NAME || ''
    );
    this.createNotificationUseCase = new CreateNotificationUseCase(repo);
  }

  public async handler(event: SQSEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventoToDTO(event);
      const result = await this.createNotificationUseCase.execute(dto);

      return this.ok(
        result.map((notification) => NotificationMap.toDTO(notification))
      );
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventoToDTO(event: SQSEvent): CreateNotificationDTO[] {
    // eslint-disable-next-line no-console
    console.log('event: ', JSON.stringify(event));
    const dto: CreateNotificationDTO[] = event.Records.map((record) => {
      const body = JSON.parse(record.body);

      return body;
    });

    return dto;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
