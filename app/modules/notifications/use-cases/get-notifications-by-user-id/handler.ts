import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { <PERSON>Handler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { GetNotificationsByUserIdUseCase } from './use-case';
import { NotificationMap } from '../../mappers/notification-map';
import { NotificationRepository } from '../../repo/notification-repository';
import { GetNotificationsByUserIdSchema } from './schema';
import { GetNotificationsByUserIdDTO } from './dto';

class Lambda extends BaseHandler implements LambdaInterface {
  private getNotificationsByUserIdUseCase: GetNotificationsByUserIdUseCase;

  private init() {
    const repo = new NotificationRepository(
      new AWS.DynamoDB(),
      process.env.NOTIFICATION_TABLE_NAME || ''
    );
    this.getNotificationsByUserIdUseCase = new GetNotificationsByUserIdUseCase(
      repo
    );
  }

  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventoToDTO(event);
      const result = await this.getNotificationsByUserIdUseCase.execute(dto);

      return this.ok(
        result.map((notification) => NotificationMap.toDTO(notification))
      );
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventoToDTO(
    event: APIGatewayProxyEvent
  ): Promise<GetNotificationsByUserIdDTO> {
    if (!event.body) {
      throw new Error('Missing body');
    }
    const body = JSON.parse(event.body);
    const dto = await GetNotificationsByUserIdSchema.validate(body);

    return dto;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
