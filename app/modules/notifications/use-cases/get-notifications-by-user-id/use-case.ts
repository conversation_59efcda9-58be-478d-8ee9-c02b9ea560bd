import { UseCase } from '../../../../shared/core/use-case';
import { ID } from '../../../../shared/domain/Id';
import { Notification } from '../../domain/notification';
import { NotificationStatus } from '../../domain/notificationStatus';
import { NotificationRepository } from '../../repo/notification-repository';
import { GetNotificationsByUserIdDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

// eslint-disable-next-line
  const PRIORITIES: any = {
  HIGH: 1,
  LOW: 0,
};
export class GetNotificationsByUserIdUseCase
  implements UseCase<GetNotificationsByUserIdDTO, Promise<Notification[]>>
{
  constructor(private repository: NotificationRepository) {}

  @LogUseCaseDTO
  public async execute(dto: GetNotificationsByUserIdDTO) {
    const id = this.mapDTOtoId(dto);
    const status = this.getNotificationStatus();
    const result = await this.getNotifications(id, status);

    return result;
  }

  private mapDTOtoId(dto: GetNotificationsByUserIdDTO) {
    const id = ID.create(dto.userId);
    if (id.isFailure) throw new Error('Invalid ID');

    return id.getValue();
  }

  private getNotificationStatus() {
    const status = NotificationStatus.create('ACTIVE');
    if (status.isFailure) throw new Error('Invalid Notification Status');

    return status.getValue();
  }

  private async getNotifications(id: ID, status: NotificationStatus) {
    const result = await this.repository.getNotificationsByUserId(id, status);

    return this.sortNotificationsByPriority(result);
  }

  private sortNotificationsByPriority(notifications: Notification[]) {
    return notifications.sort(
      (a, b) =>
        PRIORITIES[b.getPriority().value] - PRIORITIES[a.getPriority().value]
    );
  }
}
