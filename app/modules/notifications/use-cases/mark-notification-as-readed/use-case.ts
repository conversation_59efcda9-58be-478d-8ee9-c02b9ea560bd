import { UseCase } from '../../../../shared/core/use-case';
import { ID } from '../../../../shared/domain/Id';
import { Notification } from '../../domain/notification';
import { NotificationRepository } from '../../repo/notification-repository';
import { MarkNotificationAsReadedDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class MarkNotificationAsReadedUseCase
  implements UseCase<MarkNotificationAsReadedDTO, Promise<Notification>>
{
  constructor(private repository: NotificationRepository) {}

  @LogUseCaseDTO
  public async execute(dto: MarkNotificationAsReadedDTO) {
    const id = this.mapDTOtoId(dto);
    const notification = await this.getNotificationById(id);
    const result = await this.markNotificationAsRead(notification);

    return result;
  }

  private mapDTOtoId(dto: MarkNotificationAsReadedDTO) {
    const id = ID.create(dto.id);
    if (id.isFailure) throw new Error('Invalid ID');

    return id.getValue();
  }

  private async getNotificationById(id: ID) {
    const result = await this.repository.getNotificationById(id);
    if (!result) {
      throw new Error(`Notification with ID [${id}] does not exists`);
    }

    return result;
  }

  private async markNotificationAsRead(notification: Notification) {
    notification.markNotificationAsRead();
    await this.repository.updateNotification(notification);

    return notification;
  }
}
