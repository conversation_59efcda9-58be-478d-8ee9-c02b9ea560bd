import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { <PERSON>Handler } from '../../../../shared/infra/handler';
import AWS from 'aws-sdk';
import { MarkNotificationAsReadedUseCase } from './use-case';
import { NotificationMap } from '../../mappers/notification-map';
import { NotificationRepository } from '../../repo/notification-repository';
import { MarkNotificationAsReadedSchema } from './schema';
import { MarkNotificationAsReadedDTO } from './dto';

class Lambda extends BaseHandler implements LambdaInterface {
  private markNotificationAsReadedUseCase: MarkNotificationAsReadedUseCase;

  private init() {
    const repo = new NotificationRepository(
      new AWS.DynamoDB(),
      process.env.NOTIFICATION_TABLE_NAME || ''
    );
    this.markNotificationAsReadedUseCase = new MarkNotificationAsReadedUseCase(
      repo
    );
  }

  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventoToDTO(event);
      const result = await this.markNotificationAsReadedUseCase.execute(dto);

      return this.ok(NotificationMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventoToDTO(
    event: APIGatewayProxyEvent
  ): Promise<MarkNotificationAsReadedDTO> {
    if (!event.body) {
      throw new Error('Missing body');
    }
    const body = JSON.parse(event.body);
    const dto = await MarkNotificationAsReadedSchema.validate(body);

    return dto;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
