import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface IUpdatedAtProps {
  value: string;
}
export class UpdatedAt extends ValueObject<IUpdatedAtProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IUpdatedAtProps) {
    super(props);
  }

  private static isValidISODate(updatedAt: string) {
    if (typeof updatedAt !== 'string') {
      return false;
    }
    const date = new Date(updatedAt);

    return date.toISOString() === updatedAt;
  }

  public static create(updatedAt?: string): Result<UpdatedAt> {
    const date = updatedAt ? updatedAt : new Date().toISOString();
    const updatedAtProps: IUpdatedAtProps = { value: date };
    if (!this.isValidISODate(updatedAtProps.value)) {
      return Result.fail<UpdatedAt>('Missing update at date');
    }

    return Result.ok<UpdatedAt>(new UpdatedAt(updatedAtProps));
  }
}
