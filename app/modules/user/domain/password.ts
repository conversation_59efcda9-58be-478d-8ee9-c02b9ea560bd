import { Guard } from '../../../shared/core/Guard';
import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface IPasswordProps {
  value: string;
}
export class Password extends ValueObject<IPasswordProps> {
  public static minLength = 8;

  get value(): string {
    return this.props.value;
  }

  private constructor(props: IPasswordProps) {
    super(props);
  }

  public static create(password: string): Result<Password> {
    const usernameResult = Guard.againstNullOrUndefined(password, 'password');
    if (usernameResult.isFailure) {
      return Result.fail<Password>(usernameResult.getErrorValue());
    }
    const minLengthResult = Guard.againstAtLeast(this.minLength, password);
    if (minLengthResult.isFailure) {
      return Result.fail<Password>(minLengthResult.getErrorValue());
    }

    return Result.ok<Password>(new Password({ value: password }));
  }
}
