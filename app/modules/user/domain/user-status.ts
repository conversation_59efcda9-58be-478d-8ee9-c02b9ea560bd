import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

type UserStatusOptions = 'ACTIVE' | 'EXPIRED' | 'ARCHIVED';
interface IUserStatusProps {
  value: string;
}
export class UserStatus extends ValueObject<IUserStatusProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IUserStatusProps) {
    super(props);
  }

  private static isStatusValid(applicationStatus: string) {
    if (
      !applicationStatus ||
      (applicationStatus !== 'ACTIVE' &&
        applicationStatus !== 'EXPIRED' &&
        applicationStatus !== 'ARCHIVED')
    ) {
      return false;
    }

    return true;
  }

  public static create(status: UserStatusOptions | string): Result<UserStatus> {
    if (!this.isStatusValid(status)) {
      return Result.fail<UserStatus>('Invalid User Status');
    }

    return Result.ok<UserStatus>(new UserStatus({ value: status }));
  }
}
