import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface ICreatedAtProps {
  value: string;
}
export class CreatedAt extends ValueObject<ICreatedAtProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: ICreatedAtProps) {
    super(props);
  }

  private static isValidISODate(createdAt: string) {
    if (typeof createdAt !== 'string') {
      return false;
    }
    const date = new Date(createdAt);

    return date.toISOString() === createdAt;
  }

  public static create(createdAt?: string): Result<CreatedAt> {
    const date = createdAt ? createdAt : new Date().toISOString();
    const updatedAtProps: ICreatedAtProps = { value: date };
    if (!this.isValidISODate(updatedAtProps.value)) {
      return Result.fail<CreatedAt>('Missing created at date');
    }

    return Result.ok<CreatedAt>(new CreatedAt(updatedAtProps));
  }
}
