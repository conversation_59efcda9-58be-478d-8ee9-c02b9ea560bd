import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface IEmailProps {
  value: string;
}
export class Email extends ValueObject<IEmailProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IEmailProps) {
    super(props);
  }

  private static isValidEmail(email: string) {
    const re =
      // eslint-disable-next-line no-useless-escape
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    return re.test(email);
  }

  private static format(email: string): string {
    return email.trim().toLowerCase();
  }

  public static create(email: string): Result<Email> {
    if (!this.isValidEmail(email)) {
      return Result.fail<Email>('Email address not valid');
    } else {
      return Result.ok<Email>(new Email({ value: this.format(email) }));
    }
  }
}
