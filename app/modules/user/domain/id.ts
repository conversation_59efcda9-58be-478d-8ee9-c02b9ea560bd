import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';
import { v4 as uuidv4 } from 'uuid';

interface IIdProps {
  value: string;
}
export class Id extends ValueObject<IIdProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IIdProps) {
    super(props);
  }

  private static isValidUUID(id: string) {
    if (typeof id !== 'string' || id === undefined) {
      return false;
    }
    const pattern =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;

    return pattern.test(id);
  }

  public static create(userId?: string): Result<Id> {
    const id = userId ? userId : uuidv4();
    if (!this.isValidUUID(id)) {
      return Result.fail<Id>('Invalid User ID');
    }

    return Result.ok<Id>(new Id({ value: id }));
  }
}
