import { Guard } from '../../../shared/core/Guard';
import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface IIdentificationProps {
  value: string;
}
export class Identification extends ValueObject<IIdentificationProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IIdentificationProps) {
    super(props);
  }

  public static create(identification: string): Result<Identification> {
    const userIdentification = Guard.againstNullOrUndefined(
      identification,
      'identification'
    );
    if (userIdentification.isFailure) {
      return Result.fail<Identification>(userIdentification.getErrorValue());
    }

    return Result.ok<Identification>(
      new Identification({ value: identification })
    );
  }
}
