import { Guard } from '../../../shared/core/Guard';
import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface IProfilePictureProps {
  value: string;
}
export class ProfilePicture extends ValueObject<IProfilePictureProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IProfilePictureProps) {
    super(props);
  }

  private static format(url: string): string {
    return url.trim().toLowerCase();
  }

  public static create(profilePictureUrl: string): Result<ProfilePicture> {
    const profilePicturURL = Guard.againstNullOrUndefined(
      profilePictureUrl,
      'profilePicture'
    );
    if (profilePicturURL.isFailure) {
      return Result.fail<ProfilePicture>('profile picture url not valid');
    } else {
      return Result.ok<ProfilePicture>(
        new ProfilePicture({ value: this.format(profilePictureUrl) })
      );
    }
  }
}
