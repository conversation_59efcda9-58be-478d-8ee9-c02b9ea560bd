import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

type UserTypeOptions = 'ADMIN' | 'DESITION_MAKER' | 'CONTENT_CREATOR' | 'USER';
interface IUserTypeProps {
  value: string;
}
export class UserType extends ValueObject<IUserTypeProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: IUserTypeProps) {
    super(props);
  }

  private static isStatusValid(userType: string) {
    if (
      !userType ||
      (userType !== 'USER' &&
        userType !== 'DESITION_MAKER' &&
        userType !== 'CONTENT_CREATOR' &&
        userType !== 'ADMIN')
    ) {
      return false;
    }

    return true;
  }

  public static create(status: UserTypeOptions | string): Result<UserType> {
    if (!this.isStatusValid(status)) {
      return Result.fail<UserType>('Invalid User Type');
    }

    return Result.ok<UserType>(new UserType({ value: status }));
  }
}
