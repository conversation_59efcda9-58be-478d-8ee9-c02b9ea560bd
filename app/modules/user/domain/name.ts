import { Guard } from '../../../shared/core/Guard';
import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface INameProps {
  value: string;
}
export class Name extends ValueObject<INameProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: INameProps) {
    super(props);
  }

  public static create(name: string): Result<Name> {
    const usernameResult = Guard.againstNullOrUndefined(name, 'name');
    if (usernameResult.isFailure) {
      return Result.fail<Name>(usernameResult.getErrorValue());
    }

    return Result.ok<Name>(new Name({ value: name }));
  }
}
