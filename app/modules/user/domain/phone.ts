import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

interface IPhoneProps {
  value: number;
}
export class Phone extends ValueObject<IPhoneProps> {
  get value(): number {
    return this.props.value;
  }

  private constructor(props: IPhoneProps) {
    super(props);
  }

  private static isValidNumber(phone: number) {
    return typeof phone === 'number' && !isNaN(phone) && phone !== undefined;
  }

  public static create(phone: number): Result<Phone> {
    if (!this.isValidNumber(phone)) {
      return Result.fail<Phone>('phone number not valid number');
    }

    return Result.ok<Phone>(new Phone({ value: phone }));
  }
}
