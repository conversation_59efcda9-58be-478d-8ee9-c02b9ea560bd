import { Result } from '../../../shared/core/Result';
import { CreatedAt } from './created-at';
import { Email } from './email';
import { Id } from './id';
import { Name } from './name';
import { Phone } from './phone';
import { ProfilePicture } from './profile-picture';
import { UserStatus } from './user-status';
import { UpdatedAt } from './updated-at';
import { UserType } from './user-type';
import { Identification } from './identification';
import { BooleanValueObject } from '../../../shared/common-value-objects/boolean-value-object';
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { Country } from '../../../shared/common-value-objects/country';

export interface IUserProps {
  id: Id;
  name: Name;
  email: Email;
  identification: Identification;
  phone: Phone;
  profilePicture: ProfilePicture;
  userStatus: UserStatus;
  userType: UserType;
  country: Country;
  hasAcceptedTerms: BooleanValueObject;
  height: NumberValueObject;
  weight: NumberValueObject;
  birthDate: DateValueObject;
  createdAt: CreatedAt;
  updatedAt: UpdatedAt;
}
export class User {
  private props: IUserProps;

  constructor(props: IUserProps) {
    this.props = props;
  }

  get identification(): Identification {
    return this.props.identification;
  }

  get profilePicture(): ProfilePicture {
    return this.props.profilePicture;
  }

  get createdAt(): CreatedAt {
    return this.props.createdAt;
  }

  get id(): Id {
    return this.props.id;
  }

  get email(): Email {
    return this.props.email;
  }

  get name(): Name {
    return this.props.name;
  }

  get phone(): Phone {
    return this.props.phone;
  }

  get country(): Country {
    return this.props.country;
  }

  get hasAcceptedTerms(): BooleanValueObject {
    return this.props.hasAcceptedTerms;
  }

  get updatedAt(): UpdatedAt {
    return this.props.updatedAt;
  }

  get userStatus(): UserStatus {
    return this.props.userStatus;
  }

  get userType(): UserType {
    return this.props.userType;
  }

  get height(): NumberValueObject {
    return this.props.height;
  }

  get weight(): NumberValueObject {
    return this.props.weight;
  }

  get birthDate(): DateValueObject {
    return this.props.birthDate;
  }

  public updateUserData(raw: {
    email: string;
    name: string;
    identification: string;
    phone: number;
    country: string;
    userType: string;
    height: number;
    weight: number;
    birthDate: string;
  }) {
    const name = Name.create(raw.name);
    const email = Email.create(raw.email);
    const identification = Identification.create(raw.identification);
    const phone = Phone.create(raw.phone);
    const country = Country.create(raw.country);
    const updatedAt = UpdatedAt.create();
    const userType = UserType.create(raw.userType);
    const height = NumberValueObject.create('height', raw.height ?? 0);
    const weight = NumberValueObject.create('weight', raw.weight ?? 0);
    const birthDate = DateValueObject.create('birthDate', raw.birthDate);
    const dtoCombine = Result.combine([
      email,
      name,
      identification,
      phone,
      updatedAt,
      userType,
      country,
      height,
      weight,
      birthDate,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.name = name.getValue();
    this.props.email = email.getValue();
    this.props.identification = identification.getValue();
    this.props.userType = userType.getValue();
    this.props.phone = phone.getValue();
    this.props.country = country.getValue();
    this.props.updatedAt = updatedAt.getValue();
    this.props.height = height.getValue();
    this.props.weight = weight.getValue();
    this.props.birthDate = birthDate.getValue();
  }

  public updateProfilePicture(url: string) {
    const profilePicture = ProfilePicture.create(url);
    const updatedAt = UpdatedAt.create();
    const dtoCombine = Result.combine([profilePicture, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.profilePicture = profilePicture.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public archiveUser() {
    const status = UserStatus.create('ARCHIVED');
    const updatedAt = UpdatedAt.create();
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.userStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public acceptTerms() {
    const hasAcceptedTerms = BooleanValueObject.create(
      'hasAcceptedTerms',
      true
    );
    const updatedAt = UpdatedAt.create();
    const dbCombined = Result.combine([hasAcceptedTerms, updatedAt]);
    if (dbCombined.isFailure) {
      throw dbCombined.getErrorValue();
    }
    this.props.hasAcceptedTerms = hasAcceptedTerms.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public activateUser() {
    const status = UserStatus.create('ACTIVE');
    const updatedAt = UpdatedAt.create();
    const dtoCombine = Result.combine([status, updatedAt]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    this.props.userStatus = status.getValue();
    this.props.updatedAt = updatedAt.getValue();
  }

  public static create(props: IUserProps) {
    const user = new User({
      ...props,
    });

    return Result.ok<User>(user);
  }
}
