import { Result } from '../../../shared/core/Result';
import { CreatedAt } from '../domain/created-at';
import { Email } from '../domain/email';
import { Id } from '../domain/id';
import { Name } from '../domain/name';
import { Phone } from '../domain/phone';
import { ProfilePicture } from '../domain/profile-picture';
import { UserStatus } from '../domain/user-status';
import { UpdatedAt } from '../domain/updated-at';
import { User } from '../domain/User';
import { UserDTO } from '../dto/user-dto';
import { UserType } from '../domain/user-type';
import { Identification } from '../domain/identification';
import { BooleanValueObject } from '../../../shared/common-value-objects/boolean-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { Country } from '../../../shared/common-value-objects/country';
import { DateServices } from '../../../shared/services/date-services';

export class UserMap {
  public static toDTO(user: User): UserDTO {
    const calculateBMI = (height: number, weight: number): number => {
      if (
        !height ||
        !weight ||
        height <= 0 ||
        weight <= 0 ||
        isNaN(height) ||
        isNaN(weight)
      ) {
        return 0;
      }
      const bmi = weight / (height * height);

      return isFinite(bmi) ? parseFloat(bmi.toFixed(2)) : 0;
    };

    return {
      id: user.id.value,
      name: user.name.value,
      email: user.email.value,
      identification: user.identification.value,
      phone: user.phone.value,
      profilePicture: user.profilePicture.value,
      userStatus: user.userStatus.value,
      userType: user.userType.value,
      country: user.country.value,
      hasAcceptedTerms: user.hasAcceptedTerms.value ?? false,
      height: user.height.value,
      weight: user.weight.value,
      bmi: calculateBMI(user.height.value, user.weight.value),
      birthDate: user.birthDate.value,
      age: DateServices.calculateAgeBasedOnDate(user.birthDate.value),
      createdAt: user.createdAt.value,
      updatedAt: user.updatedAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): User {
    const id = Id.create(raw.id);
    const name = Name.create(raw.name);
    const email = Email.create(raw.email);
    const identification = Identification.create(raw.identification);
    const phone = Phone.create(raw.phone);
    const profilePicture = ProfilePicture.create(raw.profilePicture);
    const userStatus = UserStatus.create(raw.userStatus);
    const userType = UserType.create(raw.userType);
    const country = Country.create(raw.country);
    const hasAcceptedTerms = BooleanValueObject.create(
      'hasAcceptedTerms',
      raw.hasAcceptedTerms ?? false
    );
    const height = NumberValueObject.create('height', raw.height ?? 0);
    const weight = NumberValueObject.create('weight', raw.weight ?? 0);
    const birthDate = DateValueObject.create('birthDate', raw.birthDate);
    const createdAt = CreatedAt.create(raw.createdAt);
    const updatedAt = UpdatedAt.create(raw.updatedAt);
    const dtoCombine = Result.combine([
      profilePicture,
      createdAt,
      id,
      email,
      name,
      identification,
      phone,
      country,
      hasAcceptedTerms,
      height,
      weight,
      birthDate,
      updatedAt,
      userStatus,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const user = User.create({
      id: id.getValue(),
      name: name.getValue(),
      email: email.getValue(),
      identification: identification.getValue(),
      phone: phone.getValue(),
      profilePicture: profilePicture.getValue(),
      userStatus: userStatus.getValue(),
      userType: userType.getValue(),
      country: country.getValue(),
      hasAcceptedTerms: hasAcceptedTerms.getValue(),
      height: height.getValue(),
      weight: weight.getValue(),
      birthDate: birthDate.getValue(),
      createdAt: createdAt.getValue(),
      updatedAt: updatedAt.getValue(),
    });
    if (user.isFailure) {
      throw user.getErrorValue();
    }

    return user.getValue();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toPersistence(user: User): any {
    return {
      id: user.id.value,
      name: user.name.value,
      email: user.email.value,
      identification: user.identification.value,
      phone: user.phone.value,
      profilePicture: user.profilePicture.value,
      userStatus: user.userStatus.value,
      userType: user.userType.value,
      country: user.country.value,
      hasAcceptedTerms: user.hasAcceptedTerms.value ?? false,
      height: user.height.value ?? 0,
      weight: user.weight.value ?? 0,
      birthDate: user.birthDate.value,
      createdAt: user.createdAt.value,
      updatedAt: user.updatedAt.value,
    };
  }
}
