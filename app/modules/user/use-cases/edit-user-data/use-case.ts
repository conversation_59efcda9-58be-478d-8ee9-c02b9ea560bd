import { UserRepository } from '../../repo/user-repository';
import { EditUserDTO } from './dto';
import { UseCase } from '../../../../shared/core/use-case';
import { User } from '../../domain/User';
import { Id } from '../../domain/id';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class EditUserDataUseCase
  implements UseCase<EditUserDTO, Promise<User>>
{
  constructor(private userRepository: UserRepository) {}

  @LogUseCaseDTO
  public async execute(dto: EditUserDTO) {
    const id = this.mapDTOToUserId(dto);
    const existingUser = await this.getExistingUser(id);
    const updatedUser = this.editUser(existingUser, dto);
    const result = await this.updateUser(updatedUser);

    return result;
  }

  private mapDTOToUserId(dto: EditUserDTO): Id {
    const id = Id.create(dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingUser(id: Id): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private editUser(user: User, dto: EditUserDTO): User {
    user.updateUserData(dto);

    return user;
  }

  private async updateUser(user: User): Promise<User> {
    const result = await this.userRepository.updateUser(user);
    if (!result) throw 'Error Updating User';

    return result;
  }
}
