import { UseCase } from '../../../../shared/core/use-case';
import { UserRepository } from '../../repo/user-repository';
import { Id } from '../../domain/id';
import { ArchiveUserDTO } from './dto';
import { User } from '../../domain/User';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { AdminGetUserResponse } from 'aws-sdk/clients/cognitoidentityserviceprovider';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class ArchiveUserUseCase
  implements UseCase<ArchiveUserDTO, Promise<User>>
{
  constructor(
    private userRepository: UserRepository,
    private cognitoService: CognitoRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: ArchiveUserDTO) {
    const id = this.mapDTOToUserId(dto);
    await this.getExistingAuthorizerUser(id);
    const existingUser = await this.getExistingUser(id);
    const updatedUser = this.changeUserStatus(existingUser);
    const result = await this.updateUser(updatedUser);
    await this.disableUserInAuthorizer(id);

    return result;
  }

  private mapDTOToUserId(dto: ArchiveUserDTO): Id {
    const id = Id.create(dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingAuthorizerUser(id: Id) {
    const userOrError = await this.cognitoService.getUerById(id.value);
    if ('UserAttributes' in userOrError) {
      return userOrError as AdminGetUserResponse;
    } else {
      throw 'Authorizer User Not Found';
    }
  }

  private async getExistingUser(id: Id): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private changeUserStatus(user: User): User {
    user.archiveUser();

    return user;
  }

  private async updateUser(user: User): Promise<User> {
    const result = await this.userRepository.updateUser(user);
    if (!result) throw 'Error Updating User';

    return result;
  }

  private async disableUserInAuthorizer(id: Id) {
    const disbledUser = await this.cognitoService.disabledUser(id.value);
    if (disbledUser.$response.error)
      throw 'Error while trying to disable user in authorizer';
  }
}
