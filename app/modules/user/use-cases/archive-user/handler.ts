import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { UserRepository } from '../../repo/user-repository';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { ArchiveUserDTO } from './dto';
import AWS from 'aws-sdk';
import { UserMap } from '../../mappers/user-map';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { ArchiveUserUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private archiveUserUseCase: ArchiveUserUseCase;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const cognitoService = new CognitoRepository(
      new AWS.CognitoIdentityServiceProvider(),
      process.env.COGNITO_POOL || ''
    );
    this.archiveUserUseCase = new ArchiveUserUseCase(
      userRepository,
      cognitoService
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.archiveUserUseCase.execute(dto);

      return this.ok(UserMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): ArchiveUserDTO {
    const id = event.queryStringParameters?.id;
    if (!id) {
      throw 'Failed to parse request body';
    }

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
