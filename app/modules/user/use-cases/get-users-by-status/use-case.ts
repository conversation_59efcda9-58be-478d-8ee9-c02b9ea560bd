import { UseCase } from '../../../../shared/core/use-case';
import { UserStatus } from '../../domain/user-status';
import { User } from '../../domain/User';
import { UserRepository } from '../../repo/user-repository';

import { GetUserListByStatusDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class GetUserListByStatusUseCase
  implements UseCase<GetUserListByStatusDTO, Promise<User[]>>
{
  constructor(private userRepository: UserRepository) {}

  @LogUseCaseDTO
  public execute(dto: GetUserListByStatusDTO) {
    const userStatus = this.mapDTOtApplicationStatus(dto);

    return this.userRepository.getUsersByStatus(userStatus);
  }

  private mapDTOtApplicationStatus(dto: GetUserListByStatusDTO) {
    const userStatus = UserStatus.create(dto.status);
    if (userStatus.isFailure) throw userStatus.getErrorValue();

    return userStatus.getValue();
  }
}
