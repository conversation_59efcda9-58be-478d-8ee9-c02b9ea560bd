import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { GetUserListByStatusUseCase } from './use-case';
import { GetUserListByStatusDTO } from './dto';
import AWS from 'aws-sdk';
import { UserRepository } from '../../repo/user-repository';
import { UserMap } from '../../mappers/user-map';
import { User } from '../../domain/User';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private getUserListByStatusUseCase: GetUserListByStatusUseCase;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.getUserListByStatusUseCase = new GetUserListByStatusUseCase(
      userRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.getUserListByStatusUseCase.execute(dto);

      return this.ok(result.map((user: User) => UserMap.toDTO(user)));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): GetUserListByStatusDTO {
    const status = event.queryStringParameters?.status;
    if (!status) {
      throw 'Failed to parse request body';
    }

    return {
      status,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
