import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { EditUserProfilePicture } from './use-case';
import { UserRepository } from '../../repo/user-repository';

import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { EditUserProfilePictureDTO } from './dto';
import AWS from 'aws-sdk';
import { S3Service } from '../../../../shared/repos/file-repository';
import { UserMap } from '../../mappers/user-map';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private editUserProfilePicture: EditUserProfilePicture;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const s3Service = new S3Service(
      new AWS.S3(),
      process.env.BUCKET_NAME || ''
    );
    this.editUserProfilePicture = new EditUserProfilePicture(
      userRepository,
      s3Service
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.editUserProfilePicture.execute(dto);

      return this.ok(UserMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(
    event: APIGatewayProxyEvent
  ): EditUserProfilePictureDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const userData = JSON.parse(event.body);

    return {
      id: userData.userId,
      profilePicture: userData.profilePicture,
      name: userData.name,
      size: userData.size,
      type: userData.type,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
