import { S3Service } from '../../../../shared/repos/file-repository';
import { UserRepository } from '../../repo/user-repository';
import { EditUserProfilePictureDTO } from './dto';
import { v4 as uuidv4 } from 'uuid';
import { UseCase } from '../../../../shared/core/use-case';
import { Id } from '../../domain/id';
import { User } from '../../domain/User';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class EditUserProfilePicture
  implements UseCase<EditUserProfilePictureDTO, Promise<User>>
{
  private readonly bucketName = `${process.env.BUCKET || ''}profile-picture`;

  constructor(
    private userRepository: UserRepository,
    private s3Service: S3Service
  ) {}

  @LogUseCaseDTO
  public async execute(dto: EditUserProfilePictureDTO) {
    const id = this.mapDTOToUserId(dto);
    const fileURL = await this.saveFileToS3(dto);
    const existingUser = await this.getExistingUser(id);
    const updatedUser = this.updateUserProfilePicture(existingUser, fileURL);
    const result = await this.updateUser(updatedUser);

    return result;
  }

  private mapDTOToUserId(dto: EditUserProfilePictureDTO): Id {
    const id = Id.create(dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async saveFileToS3(dto: EditUserProfilePictureDTO): Promise<string> {
    const s3ServiceProps = {
      base64File: this.getFileData(dto.profilePicture),
      bucket: this.bucketName,
      key: this.getFileKey(this.getFileName(dto.name)),
    };
    const result = await this.s3Service.storePublicBase64File(s3ServiceProps);
    if (result.status === 'error' || !result.url)
      throw 'Error while trying to store S3 file';

    return result.url;
  }

  private async getExistingUser(id: Id): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private getFileKey(name: string): string {
    const [fileExtension] = name.split('.').reverse();

    return `${uuidv4()}.${fileExtension.trim()}`.trim();
  }

  private getFileData(data: string): string {
    if (!data) throw 'Invalid File Data (base64)';

    return data;
  }

  private getFileName(name: string): string {
    if (!name) throw 'Invalid File Name';

    return name;
  }

  private updateUserProfilePicture(user: User, url: string): User {
    user.updateProfilePicture(url);

    return user;
  }

  private async updateUser(user: User): Promise<User> {
    const result = await this.userRepository.updateUser(user);
    if (!result) throw 'Error Updating User';

    return result;
  }
}
