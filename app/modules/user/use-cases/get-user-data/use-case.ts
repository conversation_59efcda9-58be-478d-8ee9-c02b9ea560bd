import { UseCase } from '../../../../shared/core/use-case';
import { UserRepository } from '../../repo/user-repository';
import { Id } from '../../domain/id';
import { GetUserDTO } from './dto';
import { User } from '../../domain/User';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class GetUserDataUseCase implements UseCase<GetUserDTO, Promise<User>> {
  constructor(private userRepository: UserRepository) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserDTO) {
    const id = this.mapDTOtoUserID(dto);
    const user = await this.getExistingUser(id);

    return user;
  }

  private mapDTOtoUserID(dto: GetUserDTO) {
    const id = Id.create(dto.id);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getExistingUser(id: Id): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }
}
