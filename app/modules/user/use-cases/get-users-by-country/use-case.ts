import { UseCase } from '../../../../shared/core/use-case';
import { UserStatus } from '../../domain/user-status';
import { User } from '../../domain/User';
import { UserRepository } from '../../repo/user-repository';
import { GetUserListByCountryDTO } from './dto';
import { Result } from '../../../../shared/core/Result';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { Country } from '../../../../shared/common-value-objects/country';

export class GetUserListByCountryUseCase
  implements UseCase<GetUserListByCountryDTO, Promise<User[]>>
{
  constructor(private userRepository: UserRepository) {}

  @LogUseCaseDTO
  public execute(dto: GetUserListByCountryDTO) {
    const { country, userStatus } = this.mapDTOtoQueryParams(dto);

    return this.userRepository.getUsersByCountryAndStatus(country, userStatus);
  }

  private mapDTOtoQueryParams(dto: GetUserListByCountryDTO) {
    const country = Country.create(dto.country);
    const userStatus = UserStatus.create('ACTIVE');
    const dtoCombine = Result.combine([country, userStatus]);
    if (dtoCombine.isFailure) throw dtoCombine.getErrorValue();

    return {
      country: country.getValue(),
      userStatus: userStatus.getValue(),
    };
  }
}
