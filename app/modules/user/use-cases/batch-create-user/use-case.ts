import { UseCase } from '../../../../shared/core/use-case';
import { User } from '../../domain/User';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { BatchCreateUserDTO } from './dto';
import { CreateUserUseCase } from '../create-user/use-case';

export class BatchCreateUserUseCase
  implements UseCase<BatchCreateUserDTO, Promise<User[]>>
{
  constructor(private createUserUseCase: CreateUserUseCase) {}

  @LogUseCaseDTO
  public async execute(dto: BatchCreateUserDTO) {
    const userList = dto.map((user) => this.createUserUseCase.execute(user));
    const users: PromiseSettledResult<User>[] = await Promise.allSettled([
      ...userList,
    ]);
    users
      .filter(
        (result): result is PromiseRejectedResult =>
          result.status === 'rejected'
      )
      .forEach(({ reason }) => {
        // eslint-disable-next-line no-console
        console.log(`Error creating user: ${reason}`);
      });

    return users
      .filter(
        (result): result is PromiseFulfilledResult<User> =>
          result.status === 'fulfilled'
      )
      .map(({ value }) => value);
  }
}
