import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { BatchCreateUserUseCase } from './use-case';
import { UserRepository } from '../../repo/user-repository';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { BatchCreateUserDTO } from './dto';
import AWS from 'aws-sdk';
import { UserMap } from '../../mappers/user-map';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { CreateUserUseCase } from '../create-user/use-case';

class Lambda extends BaseHandler implements LambdaInterface {
  private batchCreateUserUseCase: BatchCreateUserUseCase;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const cognitoRepository = new CognitoRepository(
      new AWS.CognitoIdentityServiceProvider(),
      process.env.COGNITO_POOL || ''
    );
    const createUserUseCase = new CreateUserUseCase(
      cognitoRepository,
      userRepository
    );
    this.batchCreateUserUseCase = new BatchCreateUserUseCase(createUserUseCase);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.batchCreateUserUseCase.execute(dto);

      return this.ok(result.map(UserMap.toDTO));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): BatchCreateUserDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const userData = JSON.parse(event.body) as BatchCreateUserDTO;

    return userData;
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
