import { UseCase } from '../../../../shared/core/use-case';
import { UserRepository } from '../../repo/user-repository';
import { Id } from '../../domain/id';
import { AcceptTermsDTO } from './dto';
import { User } from '../../domain/User';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { Result } from '../../../../shared/core/Result';

export class AcceptTermsUseCase
  implements UseCase<AcceptTermsDTO, Promise<User>>
{
  constructor(private userRepository: UserRepository) {}

  @LogUseCaseDTO
  public async execute(dto: AcceptTermsDTO) {
    const { id } = this.mapDTOParam(dto);
    const existingUser = await this.getExistingUser(id);
    const updatedUser = this.acceptTerms(existingUser);
    const result = await this.updateUser(updatedUser);

    return result;
  }

  private mapDTOParam(dto: AcceptTermsDTO) {
    const id = Id.create(dto.userId);
    const dbCombined = Result.combine([id]);
    if (dbCombined.isFailure) {
      throw dbCombined.getErrorValue();
    }

    return { id: id.getValue() };
  }

  private async getExistingUser(id: Id): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private acceptTerms(user: User): User {
    user.acceptTerms();

    return user;
  }

  private async updateUser(user: User): Promise<User> {
    const result = await this.userRepository.updateUser(user);
    if (!result) throw 'Error Updating User';

    return result;
  }
}
