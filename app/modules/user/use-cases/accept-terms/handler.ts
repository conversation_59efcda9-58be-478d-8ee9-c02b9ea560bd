import AWS from 'aws-sdk';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { UserRepository } from '../../repo/user-repository';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { UserMap } from '../../mappers/user-map';
import { AcceptTermsUseCase } from './use-case';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { AcceptTermsDTO } from './dto';
import { HTTPRequestBodyValidator } from '../../../../shared/infra/http-request-body-validator';

class Lambda extends BaseHandler implements LambdaInterface {
  private acceptTermsUseCase: AcceptTermsUseCase;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.acceptTermsUseCase = new AcceptTermsUseCase(userRepository);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.acceptTermsUseCase.execute(dto);

      return this.ok(UserMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): AcceptTermsDTO {
    const BODY_SCHEMA = ['userId'];

    return HTTPRequestBodyValidator<AcceptTermsDTO>(BODY_SCHEMA, event.body);
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
