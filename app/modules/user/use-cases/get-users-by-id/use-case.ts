import { UseCase } from '../../../../shared/core/use-case';
import { User } from '../../domain/User';
import { UserRepository } from '../../repo/user-repository';
import { GetUserByIdDTO } from './dto';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';

export class GetUserByIdUseCase
  implements UseCase<GetUserByIdDTO, Promise<User>>
{
  constructor(private userRepository: UserRepository) {}

  @LogUseCaseDTO
  public async execute(dto: GetUserByIdDTO) {
    const id = this.mapDTOtoUserID(dto);
    const user = await this.getUser(id);

    return user;
  }

  private mapDTOtoUserID(dto: GetUserByIdDTO) {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private async getUser(id: UUIDValueObject) {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'USER_NOT_FOUND';

    return user;
  }
}
