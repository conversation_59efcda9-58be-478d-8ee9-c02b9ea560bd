import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { CreateUserUseCase } from './use-case';
import { UserRepository } from '../../repo/user-repository';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { CreateUserDTO } from './dto';
import AWS from 'aws-sdk';
import { UserMap } from '../../mappers/user-map';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private createUserUseCase: CreateUserUseCase;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const cognitoRepository = new CognitoRepository(
      new AWS.CognitoIdentityServiceProvider(),
      process.env.COGNITO_POOL || ''
    );
    this.createUserUseCase = new CreateUserUseCase(
      cognitoRepository,
      userRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.createUserUseCase.execute(dto);

      return this.ok(UserMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): CreateUserDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const userData = JSON.parse(event.body) as CreateUserDTO;

    return {
      ...userData,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
