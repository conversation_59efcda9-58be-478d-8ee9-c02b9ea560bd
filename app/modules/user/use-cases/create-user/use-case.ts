import { UserRepository } from '../../repo/user-repository';
import { CreateUserDTO } from './dto';
import { UseCase } from '../../../../shared/core/use-case';
import { User } from '../../domain/User';
import { UserMap } from '../../mappers/user-map';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { TextUtils } from '../../../../shared/utils/text-utils';
import { AuthUser } from './types';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

const DEFAULT_PROFILE_PICTURE = '../../assets/media/icons/blank.png';
export class CreateUserUseCase
  implements UseCase<CreateUserDTO, Promise<User>>
{
  constructor(
    private cognitoRepository: CognitoRepository,
    private userRepository: UserRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: CreateUserDTO) {
    const user = this.mapDTOtoUser(dto);
    await this.getExistingUser(user);
    const userCreated = await this.createUsers(user);
    const authUser = this.getAuthUser(user);
    await this.createAuthUser(authUser);

    return userCreated;
  }

  private mapDTOtoUser(dto: CreateUserDTO) {
    const user = UserMap.toDomain({
      name: dto.name,
      email: dto.email,
      phone: dto.phone || 0,
      identification: dto.identification,
      profilePicture: DEFAULT_PROFILE_PICTURE,
      country: dto.country,
      userStatus: 'ACTIVE',
      userType: dto.userType,
      height: dto.height,
      weight: dto.weight,
      birthDate: dto.birthDate,
    });

    return user;
  }

  private async getExistingUser(user: User) {
    const existingUser = await this.userRepository.getUserByEmail(user.email);
    if (existingUser.length > 0) throw 'User With That Email Already Exist';
  }

  private async createUsers(user: User): Promise<User> {
    const userCreated = await this.userRepository.createUser(user);
    if (!userCreated) throw 'Error Updating User';

    return userCreated;
  }

  private getAuthUser(user: User): AuthUser {
    const temporaryPassword = TextUtils.generateRandomPassword(8);
    const username = user.id.value;
    const email = user.email.value;

    return {
      username,
      temporaryPassword,
      email,
    };
  }

  private async createAuthUser(authUser: AuthUser) {
    await this.cognitoRepository.createUser(authUser);
  }
}
