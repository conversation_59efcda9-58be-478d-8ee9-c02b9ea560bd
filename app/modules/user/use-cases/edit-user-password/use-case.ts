import {
  AdminGetUserResponse,
  AdminSetUserPasswordResponse,
} from 'aws-sdk/clients/cognitoidentityserviceprovider';
import { Result } from '../../../../shared/core/Result';
import { UseCase } from '../../../../shared/core/use-case';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { Id } from '../../domain/id';
import { Password } from '../../domain/password';
import { EditUserPasswordDTO } from './dto';
import { ChangeUserPasswordProps } from './types';
import { User } from '../../domain/User';
import { UserRepository } from '../../repo/user-repository';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class EditUserPassword
  implements
    UseCase<EditUserPasswordDTO, Promise<AdminSetUserPasswordResponse>>
{
  constructor(
    private userRepository: UserRepository,
    private cognitoService: CognitoRepository
  ) {}

  @LogUseCaseDTO
  public async execute(dto: EditUserPasswordDTO) {
    const id = this.mapDTOToUserId(dto);
    await this.getExistingUser(id);
    await this.getExistingAuthorizerUser(id);
    const props = this.mapDTOtochangeUserPasswordProps(dto);
    const result = await this.changeUserAuthorizerPassword(props);

    return result;
  }

  private mapDTOToUserId(dto: EditUserPasswordDTO): Id {
    const id = Id.create(dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingUser(id: Id): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private async getExistingAuthorizerUser(id: Id) {
    const userOrError = await this.cognitoService.getUerById(id.value);
    if ('UserAttributes' in userOrError) {
      return userOrError as AdminGetUserResponse;
    } else {
      throw 'Authorizer User Not Found';
    }
  }

  private mapDTOtochangeUserPasswordProps(
    dto: EditUserPasswordDTO
  ): ChangeUserPasswordProps {
    const id = Id.create(dto.id);
    const password = Password.create(dto.password);
    const dtoCombine = Result.combine([id, password]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }

    return {
      username: id.getValue().value,
      password: password.getValue().value,
      isPermanent: true,
    };
  }

  private async changeUserAuthorizerPassword(props: ChangeUserPasswordProps) {
    const result = await this.cognitoService.changeUserPassword(props);
    if (result.$response.error) throw 'Error Updating User Password';

    return result;
  }
}
