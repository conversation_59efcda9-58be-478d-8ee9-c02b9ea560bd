import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { EditUserPassword } from './use-case';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { EditUserPasswordDTO } from './dto';
import AWS from 'aws-sdk';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { UserRepository } from '../../repo/user-repository';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private editUserPassword: EditUserPassword;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const cognitoService = new CognitoRepository(
      new AWS.CognitoIdentityServiceProvider(),
      process.env.COGNITO_POOL || ''
    );
    this.editUserPassword = new EditUserPassword(
      userRepository,
      cognitoService
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.editUserPassword.execute(dto);

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): EditUserPasswordDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const passwordChangeRequest = JSON.parse(event.body);

    return {
      id: passwordChangeRequest.userId,
      password: passwordChangeRequest.password,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
