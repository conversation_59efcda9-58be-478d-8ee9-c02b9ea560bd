import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ResetUserPassword } from './use-case';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { ResetUserPasswordDTO } from './dto';
import AWS from 'aws-sdk';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';
import { UserRepository } from '../../repo/user-repository';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private resetUserPassword: ResetUserPassword;

  private init() {
    const userRepository = new UserRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    const eventService = new EventService(
      process.env.SQS_URL || '',
      'create-auth-user'
    );
    const cognitoService = new CognitoRepository(
      new AWS.CognitoIdentityServiceProvider(),
      process.env.COGNITO_POOL || ''
    );
    this.resetUserPassword = new ResetUserPassword(
      userRepository,
      cognitoService,
      eventService
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.resetUserPassword.execute(dto);

      return this.ok(result);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): ResetUserPasswordDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const passwordChangeRequest = JSON.parse(event.body);

    return {
      id: passwordChangeRequest.id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
