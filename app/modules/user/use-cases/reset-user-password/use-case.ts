import { AdminGetUserResponse } from 'aws-sdk/clients/cognitoidentityserviceprovider';
import { Result } from '../../../../shared/core/Result';
import { UseCase } from '../../../../shared/core/use-case';
import { CognitoRepository } from '../../../../shared/repos/cognito-repository';
import { EventService } from '../../../../shared/services/publish-sqs-event-service';
import { TextUtils } from '../../../../shared/utils/text-utils';
import { Id } from '../../domain/id';
import { Password } from '../../domain/password';
import { User } from '../../domain/User';
import { UserRepository } from '../../repo/user-repository';
import { ResetUserPasswordDTO as ResetUserPasswordDTO } from './dto';
import { ChangeUserPasswordProps, ServiceResponse } from './types';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class ResetUserPassword
  implements UseCase<ResetUserPasswordDTO, Promise<ServiceResponse>>
{
  constructor(
    private userRepository: UserRepository,
    private cognitoService: CognitoRepository,
    private eventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute(dto: ResetUserPasswordDTO) {
    const id = this.mapDTOToUserId(dto);
    await this.getExistingUser(id);
    const existingAuthorizerUser = await this.getExistingAuthorizerUser(id);
    const userEmail = this.getUserEmail(existingAuthorizerUser);
    const props = this.mapDTOtoChangeUserPasswordProps(dto);
    await this.resetUserAuthorizerPassword(props);
    await this.notifyUser(userEmail, props);

    return { password: props.password };
  }

  private mapDTOToUserId(dto: ResetUserPasswordDTO): Id {
    const id = Id.create(dto.id);
    if (id.isFailure) {
      throw id.getErrorValue();
    }

    return id.getValue();
  }

  private async getExistingUser(id: Id): Promise<User> {
    const user = await this.userRepository.getUserById(id);
    if (!user) throw 'User Not Found';

    return user;
  }

  private async getExistingAuthorizerUser(id: Id) {
    const userOrError = await this.cognitoService.getUerById(id.value);
    if ('UserAttributes' in userOrError) {
      return userOrError as AdminGetUserResponse;
    } else {
      throw 'Authorizer User Not Found';
    }
  }

  private getUserEmail(existingUser: AdminGetUserResponse): string {
    const email = existingUser.UserAttributes?.find((e) => e.Name === 'email');
    if (!email || !email.Value) throw 'Missing Email';

    return email.Value;
  }

  private mapDTOtoChangeUserPasswordProps(
    dto: ResetUserPasswordDTO
  ): ChangeUserPasswordProps {
    const temporaryPassword = TextUtils.generateRandomPassword(10);
    const id = Id.create(dto.id);
    const password = Password.create(temporaryPassword);
    const dtoCombine = Result.combine([id, password]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }

    return {
      username: id.getValue().value,
      password: password.getValue().value,
      isPermanent: false,
    };
  }

  private async resetUserAuthorizerPassword(props: ChangeUserPasswordProps) {
    const result = await this.cognitoService.changeUserPassword(props);
    if (result.$response.error) throw 'Error Updating User Password';

    return result;
  }

  private async notifyUser(email: string, props: ChangeUserPasswordProps) {
    await this.eventService.publishEvent({
      body: this.getSQSBodyMessage(email, props),
    });
  }

  private getSQSBodyMessage(
    email: string,
    props: ChangeUserPasswordProps
  ): string {
    const userData = {
      email,
      subject: 'Cambio de Contraseña',
      body: this.getEmailBody(email, props.password),
    };

    return JSON.stringify(userData);
  }

  private getEmailBody(email: string, password: string): string {
    return `
      <p style="margin-top:10px">Se han restablecido sus las credenciales para ingresar asl sistema,</p>
  
      <p style="margin-top:20px">Usuario: ${email}</p>
      <p>Contraseña temporal: ${password}</p>
      <a style="margin-top:20px" href="${process.env.PLATFORM_URL}">BeBetter Health Monitor</a>
      `;
  }
}
