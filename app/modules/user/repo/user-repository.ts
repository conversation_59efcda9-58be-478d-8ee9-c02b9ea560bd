import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { Id } from '../domain/id';
import { UserStatus } from '../domain/user-status';
import { User } from '../domain/User';
import { UserMap } from '../mappers/user-map';
import { Email } from '../domain/email';
import { DynamoServices } from '../../../shared/services/dynamo-services';
import { Country } from '../../../shared/common-value-objects/country';

export class UserRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createUser(user: User): Promise<User> {
    const rawUser = UserMap.toPersistence(user);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(rawUser),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return user;
  }

  public async getUserById(userID: Id): Promise<User | null> {
    const id = userID.value;
    const params = {
      TableName: this.tableName,
      Key: {
        id: {
          S: id,
        },
      },
    };
    const result = await this.dynamodb.getItem(params).promise();
    if (!result.Item) return null;
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return UserMap.toDomain(this.unmarshallElement(result.Item));
  }

  public async getUserByEmail(userEmail: Email) {
    const email = userEmail.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'email-createdAt-index',
      KeyConditionExpression: 'email = :email',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':email': { S: email },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await this.dynamodb.query(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', result);
    const response: User[] = [];
    result.Items &&
      result.Items.forEach((item) => {
        const userDomain = UserMap.toDomain(this.unmarshallElement(item));
        userDomain && response.push(userDomain);
      });

    return response;
  }

  public async getUsersByStatus(userStatus: UserStatus): Promise<User[]> {
    const status = userStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'userStatus-createdAt-index',
      KeyConditionExpression: 'userStatus = :userStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => UserMap.toDomain(item));
  }

  public async getUsersByCountryAndStatus(
    userCountry: Country,
    userStatus: UserStatus
  ): Promise<User[]> {
    const country = userCountry.value;
    const status = userStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'country-userStatus-index',
      KeyConditionExpression: 'country = :country AND userStatus = :userStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':country': { S: country },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => UserMap.toDomain(item));
  }

  public async updateUser(user: User): Promise<User | null> {
    const id = user.id.value;
    const data = UserMap.toPersistence(user);
    delete data.id;
    const params = {
      TableName: this.tableName,
      Key: this.marshallElement({
        id,
      }),
      ReturnValues: 'ALL_NEW',
      UpdateExpression:
        'set ' +
        Object.keys(data)
          .map((k) => `#${k} = :${k}`)
          .join(', '),
      ExpressionAttributeNames: Object.entries(data).reduce(
        (acc, cur) => ({ ...acc, [`#${cur[0]}`]: cur[0] }),
        {}
      ),
      ExpressionAttributeValues: this.marshallElement(
        Object.entries(data).reduce(
          (acc, cur) => ({ ...acc, [`:${cur[0]}`]: cur[1] }),
          {}
        )
      ),
    };
    const result = await this.dynamodb.updateItem(params).promise();
    if (!result.Attributes) throw 'Query return values error';
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return UserMap.toDomain(this.unmarshallElement(result.Attributes));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
