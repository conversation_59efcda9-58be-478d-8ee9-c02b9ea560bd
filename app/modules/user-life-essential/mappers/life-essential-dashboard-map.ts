import { LifeEssential } from '../domain/LifeEssential';
import { LifeEssentialDashboardDTO } from '../dto/life-essential-dashboard-dto';

export class LifeEssentialDashboardMap {
  public static toDTO(lifeEssential: LifeEssential): LifeEssentialDashboardDTO {
    const getDietScore = (lifeEssential: LifeEssential): number => {
      const {
        oliveOil: { value: oliveOil },
        greenLeafyVegetables: { value: greenLeafyVegetables },
        otherVegetables: { value: otherVegetables },
        berries: { value: berries },
        otherFruit: { value: otherFruit },
        meat: { value: meat },
        fish: { value: fish },
        chicken: { value: chicken },
        cheese: { value: cheese },
        butter: { value: butter },
        beans: { value: beans },
        sweetsAndPastries: { value: sweetsAndPastries },
        nuts: { value: nuts },
        fastFood: { value: fastFood },
        alcohol: { value: alcohol },
      } = lifeEssential;
      let score = 0;
      score += oliveOil >= 2 ? 1 : 0;
      score += greenLeafyVegetables >= 7 ? 1 : 0;
      score += otherVegetables >= 2 ? 1 : 0;
      score += berries >= 2 ? 1 : 0;
      score += otherFruit >= 1 ? 1 : 0;
      score += meat <= 3 ? 1 : 0;
      score += fish >= 1 ? 1 : 0;
      score += chicken <= 5 ? 1 : 0;
      score += cheese <= 4 ? 1 : 0;
      score += butter <= 5 ? 1 : 0;
      score += beans >= 3 ? 1 : 0;
      score += sweetsAndPastries <= 4 ? 1 : 0;
      score += nuts >= 4 ? 1 : 0;
      score += fastFood <= 1 ? 1 : 0;
      score += alcohol <= 2 ? 1 : 0;
      switch (true) {
        case score >= 15:
          return 100;
        case score >= 12 && score <= 14:
          return 80;
        case score >= 8 && score <= 11:
          return 50;
        case score >= 4 && score <= 7:
          return 25;
        case score >= 0 && score <= 3:
          return 0;
        default:
          return 0;
      }
    };
    const getPhysicalActivityScore = (lifeEssential: LifeEssential): number => {
      const minutes = lifeEssential.physicalActivity.value;
      switch (true) {
        case minutes >= 150:
          return 100;
        case minutes >= 120 && minutes <= 149:
          return 90;
        case minutes >= 90 && minutes <= 119:
          return 80;
        case minutes >= 60 && minutes <= 89:
          return 60;
        case minutes >= 30 && minutes <= 59:
          return 40;
        case minutes >= 1 && minutes <= 29:
          return 20;
        default:
          return 0;
      }
    };
    const getNicotineExposureScore = (lifeEssential: LifeEssential): number => {
      const nicotineExposure = lifeEssential.nicotineExposure.value;
      switch (nicotineExposure) {
        case 'NEVER_SMOKE':
          return 100;
        case 'FORMER_SMOKER_MORE_THAN_5':
          return 75;
        case 'FORMER_SMOKER_BETWEEEN_1_AND_5':
          return 50;
        case 'FORMER_SMOKER_LESS_THAN_1':
          return 25;
        case 'CURRENT_SMOKER':
          return 0;
        default:
          return 0;
      }
    };
    const getSleepScore = (lifeEssential: LifeEssential): number => {
      const hours = lifeEssential.sleep.value;
      switch (true) {
        case hours >= 7 && hours < 9:
          return 100;
        case hours >= 9 && hours < 10:
          return 90;
        case hours >= 6 && hours < 7:
          return 70;
        case (hours >= 5 && hours < 9) || hours > 10:
          return 40;
        case hours > 4 && hours < 5:
          return 20;
        case hours < 4:
          return 0;
        default:
          return 0;
      }
    };
    const getBodyWeightScore = (lifeEssential: LifeEssential): number => {
      const heigh = lifeEssential.height.value;
      const weight = lifeEssential.bodyWeight.value;
      const BMI = weight / heigh;
      switch (true) {
        case BMI < 25:
          return 100;
        case BMI >= 25 && BMI < 30:
          return 70;
        case BMI >= 30 && BMI < 35:
          return 30;
        case BMI >= 35 && BMI < 40:
          return 15;
        case BMI >= 40:
          return 0;
        default:
          return 0;
      }
    };
    const getCholesterolScore = (lifeEssential: LifeEssential): number => {
      const cholesterol = lifeEssential.cholesterol.value;
      switch (true) {
        case cholesterol < 130:
          return 100;
        case cholesterol >= 130 && cholesterol < 160:
          return 60;
        case cholesterol >= 160 && cholesterol < 190:
          return 40;
        case cholesterol >= 190 && cholesterol < 220:
          return 20;
        case cholesterol >= 120:
          return 0;
        default:
          return 0;
      }
    };
    const getGlucoseScore = (lifeEssential: LifeEssential): number => {
      const glucose = lifeEssential.glucose.value;
      switch (true) {
        case glucose < 100:
          return 100;
        case glucose >= 100 && glucose < 125:
          return 60;
        default:
          return 0;
      }
    };
    const getBloodPreasureScore = (lifeEssential: LifeEssential): number => {
      const systolic = lifeEssential.systolic.value;
      const diastolic = lifeEssential.diastolic.value;
      switch (true) {
        case systolic < 120 && diastolic < 80:
          return 100;
        case systolic >= 120 && systolic < 130 && diastolic <= 80:
          return 75;
        case (systolic >= 130 && systolic < 140) ||
          (diastolic >= 80 && diastolic < 90):
          return 50;
        case (systolic >= 140 && systolic < 160) ||
          (diastolic >= 90 && diastolic < 100):
          return 25;
        case systolic >= 160 || diastolic >= 100:
          return 0;
        default:
          return 0;
      }
    };
    const dietScore = getDietScore(lifeEssential);
    const physicalActivityScore = getPhysicalActivityScore(lifeEssential);
    const nicotineExposureScore = getNicotineExposureScore(lifeEssential);
    const sleepScore = getSleepScore(lifeEssential);
    const bodyWeightScore = getBodyWeightScore(lifeEssential);
    const cholesterolScore = getCholesterolScore(lifeEssential);
    const glucoseScore = getGlucoseScore(lifeEssential);
    const bloodPreasureScore = getBloodPreasureScore(lifeEssential);
    const overallScore = Math.round(
      (dietScore +
        physicalActivityScore +
        nicotineExposureScore +
        sleepScore +
        bodyWeightScore +
        cholesterolScore +
        glucoseScore +
        bloodPreasureScore) /
        8
    );
    16;

    return {
      date: lifeEssential.createdAt.value,
      overallScore,
      dietScore,
      physicalActivityScore,
      nicotineExposureScore,
      sleepScore,
      bodyWeightScore,
      cholesterolScore,
      glucoseScore,
      bloodPreasureScore,
    };
  }
}
