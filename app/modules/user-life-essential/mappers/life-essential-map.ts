import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { LifeEssential } from '../domain/LifeEssential';
import { LifeEssentialStatus } from '../domain/life-essential-status';
import { NicotineExposure } from '../domain/nicotine-exposure';
import { LifeEssentialDTO } from '../dto/life-essential-dto';

export class LifeEssentialMap {
  public static toDTO(lifeEssential: LifeEssential): LifeEssentialDTO {
    return {
      id: lifeEssential.id.value,
      userId: lifeEssential.userId.value,
      oliveOil: lifeEssential.oliveOil.value,
      greenLeafyVegetables: lifeEssential.greenLeafyVegetables.value,
      otherVegetables: lifeEssential.otherVegetables.value,
      berries: lifeEssential.berries.value,
      otherFruit: lifeEssential.otherFruit.value,
      meat: lifeEssential.meat.value,
      fish: lifeEssential.fish.value,
      chicken: lifeEssential.chicken.value,
      cheese: lifeEssential.cheese.value,
      butter: lifeEssential.butter.value,
      beans: lifeEssential.beans.value,
      wholeGrains: lifeEssential.wholeGrains.value,
      sweetsAndPastries: lifeEssential.sweetsAndPastries.value,
      nuts: lifeEssential.nuts.value,
      fastFood: lifeEssential.fastFood.value,
      alcohol: lifeEssential.alcohol.value,
      physicalActivity: lifeEssential.physicalActivity.value,
      nicotineExposure: lifeEssential.nicotineExposure.value,
      sleep: lifeEssential.sleep.value,
      bodyWeight: lifeEssential.bodyWeight.value,
      height: lifeEssential.height.value,
      cholesterol: lifeEssential.cholesterol.value,
      glucose: lifeEssential.glucose.value,
      systolic: lifeEssential.systolic.value,
      diastolic: lifeEssential.diastolic.value,
      lifeEssentialStatus: lifeEssential.lifeEssentialStatus.value,
      createdAt: lifeEssential.createdAt.value,
      updatedAt: lifeEssential.updatedAt.value,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any) {
    const id = UUIDValueObject.create('id', raw.id);
    const userId = UUIDValueObject.create('userId', raw.userId);
    const oliveOil = NumberValueObject.create('oliveOil', raw.oliveOil);
    const greenLeafyVegetables = NumberValueObject.create(
      'greenLeafyVegetables',
      raw.greenLeafyVegetables
    );
    const otherVegetables = NumberValueObject.create(
      'otherVegetables',
      raw.otherVegetables
    );
    const berries = NumberValueObject.create('berries', raw.berries);
    const otherFruit = NumberValueObject.create('otherFruit', raw.otherFruit);
    const meat = NumberValueObject.create('meat', raw.meat);
    const fish = NumberValueObject.create('fish', raw.fish);
    const chicken = NumberValueObject.create('chicken', raw.chicken);
    const cheese = NumberValueObject.create('cheese', raw.cheese);
    const butter = NumberValueObject.create('butter', raw.butter);
    const beans = NumberValueObject.create('beans', raw.beans);
    const wholeGrains = NumberValueObject.create(
      'wholeGrains',
      raw.wholeGrains
    );
    const sweetsAndPastries = NumberValueObject.create(
      'sweetsAndPastries',
      raw.sweetsAndPastries
    );
    const nuts = NumberValueObject.create('nuts', raw.nuts);
    const fastFood = NumberValueObject.create('fastFood', raw.fastFood);
    const alcohol = NumberValueObject.create('alcohol', raw.alcohol);
    const physicalActivity = NumberValueObject.create(
      'physicalActivity',
      raw.physicalActivity
    );
    const nicotineExposure = NicotineExposure.create(raw.nicotineExposure);
    const sleep = NumberValueObject.create('sleep', raw.sleep);
    const bodyWeight = NumberValueObject.create('bodyWeight', raw.bodyWeight);
    const height = NumberValueObject.create('height', raw.height);
    const cholesterol = NumberValueObject.create(
      'cholesterol',
      raw.cholesterol
    );
    const glucose = NumberValueObject.create('glucose', raw.glucose);
    const systolic = NumberValueObject.create('systolic', raw.systolic);
    const diastolic = NumberValueObject.create('diastolic', raw.diastolic);
    const lifeEssentialStatus = LifeEssentialStatus.create(
      raw.lifeEssentialStatus
    );
    const createdAt = DateValueObject.create('createdAt', raw.createdAt);
    const updatedAt = DateValueObject.create('updatedAt', raw.updatedAt);
    const dbCombine = Result.combine([
      id,
      userId,
      oliveOil,
      greenLeafyVegetables,
      otherVegetables,
      berries,
      otherFruit,
      meat,
      fish,
      chicken,
      cheese,
      butter,
      beans,
      wholeGrains,
      sweetsAndPastries,
      nuts,
      fastFood,
      alcohol,
      physicalActivity,
      nicotineExposure,
      sleep,
      bodyWeight,
      height,
      cholesterol,
      glucose,
      systolic,
      diastolic,
      lifeEssentialStatus,
      createdAt,
      updatedAt,
    ]);
    if (dbCombine.isFailure) {
      throw dbCombine.getErrorValue();
    }
    const lifeEssential = LifeEssential.create({
      id: id.getValue(),
      userId: userId.getValue(),
      oliveOil: oliveOil.getValue(),
      greenLeafyVegetables: greenLeafyVegetables.getValue(),
      otherVegetables: otherVegetables.getValue(),
      berries: berries.getValue(),
      otherFruit: otherFruit.getValue(),
      meat: meat.getValue(),
      fish: fish.getValue(),
      chicken: chicken.getValue(),
      cheese: cheese.getValue(),
      butter: butter.getValue(),
      beans: beans.getValue(),
      wholeGrains: wholeGrains.getValue(),
      sweetsAndPastries: sweetsAndPastries.getValue(),
      nuts: nuts.getValue(),
      fastFood: fastFood.getValue(),
      alcohol: alcohol.getValue(),
      physicalActivity: physicalActivity.getValue(),
      nicotineExposure: nicotineExposure.getValue(),
      sleep: sleep.getValue(),
      bodyWeight: bodyWeight.getValue(),
      height: height.getValue(),
      cholesterol: cholesterol.getValue(),
      glucose: glucose.getValue(),
      systolic: systolic.getValue(),
      diastolic: diastolic.getValue(),
      lifeEssentialStatus: lifeEssentialStatus.getValue(),
      createdAt: createdAt.getValue(),
      updatedAt: updatedAt.getValue(),
    });
    if (lifeEssential.isFailure) {
      throw lifeEssential.getErrorValue();
    }

    return lifeEssential.getValue();
  }

  public static toPersistence(lifeEssential: LifeEssential) {
    return {
      id: lifeEssential.id.value,
      userId: lifeEssential.userId.value,
      oliveOil: lifeEssential.oliveOil.value,
      greenLeafyVegetables: lifeEssential.greenLeafyVegetables.value,
      otherVegetables: lifeEssential.otherVegetables.value,
      berries: lifeEssential.berries.value,
      otherFruit: lifeEssential.otherFruit.value,
      meat: lifeEssential.meat.value,
      fish: lifeEssential.fish.value,
      chicken: lifeEssential.chicken.value,
      cheese: lifeEssential.cheese.value,
      butter: lifeEssential.butter.value,
      beans: lifeEssential.beans.value,
      wholeGrains: lifeEssential.wholeGrains.value,
      sweetsAndPastries: lifeEssential.sweetsAndPastries.value,
      nuts: lifeEssential.nuts.value,
      fastFood: lifeEssential.fastFood.value,
      alcohol: lifeEssential.alcohol.value,
      physicalActivity: lifeEssential.physicalActivity.value,
      nicotineExposure: lifeEssential.nicotineExposure.value,
      sleep: lifeEssential.sleep.value,
      bodyWeight: lifeEssential.bodyWeight.value,
      height: lifeEssential.height.value,
      cholesterol: lifeEssential.cholesterol.value,
      glucose: lifeEssential.glucose.value,
      systolic: lifeEssential.systolic.value,
      diastolic: lifeEssential.diastolic.value,
      lifeEssentialStatus: lifeEssential.lifeEssentialStatus.value,
      createdAt: lifeEssential.createdAt.value,
      updatedAt: lifeEssential.updatedAt.value,
    };
  }
}
