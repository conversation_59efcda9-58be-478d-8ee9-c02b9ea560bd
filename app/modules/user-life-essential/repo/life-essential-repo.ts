import * as AWS from 'aws-sdk';
import {
  AttributeMap,
  PutItemInput,
  QueryInput,
} from 'aws-sdk/clients/dynamodb';
import { LifeEssential } from '../domain/LifeEssential';
import { LifeEssentialMap } from '../mappers/life-essential-map';
import { LifeEssentialStatus } from '../domain/life-essential-status';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { DynamoServices } from '../../../shared/services/dynamo-services';

export class LifeEssentialRepository {
  readonly dynamodb: AWS.DynamoDB;

  readonly tableName: string;

  constructor(dynamodb: AWS.DynamoDB, tableName: string) {
    this.dynamodb = dynamodb;
    this.tableName = tableName;
  }

  public async createLifeEssential(
    data: LifeEssential
  ): Promise<LifeEssential> {
    const rawLifeEssential = LifeEssentialMap.toPersistence(data);
    const params: PutItemInput = {
      TableName: this.tableName,
      Item: this.marshallElement(rawLifeEssential),
    };
    const result = await this.dynamodb.putItem(params).promise();
    // eslint-disable-next-line no-console
    console.log('Repository result', JSON.stringify(result));

    return data;
  }

  public async getLifeEssentialByUserId(
    userid: UUIDValueObject,
    lifeEssentialStatus: LifeEssentialStatus
  ): Promise<LifeEssential[]> {
    const id = userid.value;
    const status = lifeEssentialStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'userId-lifeEssentialStatus-index',
      KeyConditionExpression:
        'userId = :userId AND lifeEssentialStatus = :lifeEssentialStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':userId': { S: id },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':lifeEssentialStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => LifeEssentialMap.toDomain(item));
  }

  public async getLifeEssentialByStatus(
    lifeEssentialStatus: LifeEssentialStatus
  ): Promise<LifeEssential[]> {
    const status = lifeEssentialStatus.value;
    const params: QueryInput = {
      TableName: this.tableName,
      IndexName: 'lifeEssentialStatus-index',
      KeyConditionExpression: 'lifeEssentialStatus = :lifeEssentialStatus',
      ExpressionAttributeValues: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ':lifeEssentialStatus': { S: status },
      },
      Select: 'ALL_PROJECTED_ATTRIBUTES',
      ScanIndexForward: false,
    };
    const result = await DynamoServices.queryAllElements(params);

    return result.map((item) => LifeEssentialMap.toDomain(item));
  }

  private unmarshallElement(item: AttributeMap) {
    return AWS.DynamoDB.Converter.unmarshall(item);
  }

  private marshallElement(item: object) {
    return AWS.DynamoDB.Converter.marshall(item);
  }
}
