import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/handler';
import { QSLifeEssentialReportUseCase } from './use-case';
import AWS from 'aws-sdk';
import { LifeEssentialRepository } from '../../repo/life-essential-repo';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { S3Service } from '../../../../shared/repos/file-repository';

class Lambda extends BaseHandler implements LambdaInterface {
  private qsLifeEssentialReportUseCase: QSLifeEssentialReportUseCase;

  private init() {
    const s3Service = new S3Service(
      new AWS.S3(),
      process.env.BUCKET_NAME || ''
    );
    const lifeEssentialRepository = new LifeEssentialRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.qsLifeEssentialReportUseCase = new QSLifeEssentialReportUseCase(
      lifeEssentialRepository,
      s3Service
    );
  }

  @LogHandlerEvent
  public async handler() {
    try {
      this.init();
      await this.qsLifeEssentialReportUseCase.execute();

      return this.ok('success');
    } catch (error) {
      return this.fail(error as Error);
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
