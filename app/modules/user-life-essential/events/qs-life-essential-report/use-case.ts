import { UseCase } from '../../../../shared/core/use-case';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { S3Service } from '../../../../shared/repos/file-repository';
import { LifeEssential } from '../../domain/LifeEssential';
import { LifeEssentialStatus } from '../../domain/life-essential-status';
import { LifeEssentialDashboardDTO } from '../../dto/life-essential-dashboard-dto';
import { LifeEssentialDashboardMap } from '../../mappers/life-essential-dashboard-map';
import { LifeEssentialRepository } from '../../repo/life-essential-repo';

const DATA_KEY = 'quicksight-life-essential-data';
const COUNT_KEY = 'quicksight-life-essential-count';
const ACTIVE_STATUS = 'ACTIVE';
export class QSLifeEssentialReportUseCase
  implements UseCase<void, Promise<void>>
{
  constructor(
    private lifeEssentialRepository: LifeEssentialRepository,
    private s3Service: S3Service
  ) {}

  @LogUseCaseDTO
  public async execute() {
    const status = this.getLifeEssentialStatus();
    const lifeEssentialList =
      await this.lifeEssentialRepository.getLifeEssentialByStatus(status);
    const jsonData = lifeEssentialList.map((data) =>
      LifeEssentialDashboardMap.toDTO(data)
    );
    await this.storeJSONFileToS3({
      userCount: this.getUniqueUserCount(lifeEssentialList),
      data: jsonData,
    });
  }

  private getLifeEssentialStatus() {
    const lifeEssentialStatus = LifeEssentialStatus.create(ACTIVE_STATUS);
    if (lifeEssentialStatus.isFailure)
      throw lifeEssentialStatus.getErrorValue();

    return lifeEssentialStatus.getValue();
  }

  private getUniqueUserCount(lifeEssentials: LifeEssential[]) {
    const uniqueUserIds = new Set(
      lifeEssentials.map((item) => item.userId.value)
    );

    return uniqueUserIds.size;
  }

  private async storeJSONFileToS3({
    userCount,
    data,
  }: {
    userCount: number;
    data: LifeEssentialDashboardDTO[];
  }) {
    const countProps = {
      body: JSON.stringify(data),
      key: DATA_KEY,
    };
    const dataProps = {
      body: JSON.stringify({ userCount }),
      key: COUNT_KEY,
    };
    await this.s3Service.storePublicJSONFile(countProps);
    await this.s3Service.storePublicJSONFile(dataProps);
  }
}
