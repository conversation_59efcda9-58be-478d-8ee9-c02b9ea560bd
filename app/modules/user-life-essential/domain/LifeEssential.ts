import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { NumberValueObject } from '../../../shared/common-value-objects/number-value-object';
import { UUIDValueObject } from '../../../shared/common-value-objects/uuid-value-object';
import { Result } from '../../../shared/core/Result';
import { LifeEssentialStatus } from './life-essential-status';
import { NicotineExposure } from './nicotine-exposure';

export interface ILifeEsentialProps {
  id: UUIDValueObject;
  userId: UUIDValueObject;
  oliveOil: NumberValueObject;
  greenLeafyVegetables: NumberValueObject;
  otherVegetables: NumberValueObject;
  berries: NumberValueObject;
  otherFruit: NumberValueObject;
  meat: NumberValueObject;
  fish: NumberValueObject;
  chicken: NumberValueObject;
  cheese: NumberValueObject;
  butter: NumberValueObject;
  beans: NumberValueObject;
  wholeGrains: NumberValueObject;
  sweetsAndPastries: NumberValueObject;
  nuts: NumberValueObject;
  fastFood: NumberValueObject;
  alcohol: NumberValueObject;
  physicalActivity: NumberValueObject;
  nicotineExposure: NicotineExposure;
  sleep: NumberValueObject;
  bodyWeight: NumberValueObject;
  height: NumberValueObject;
  cholesterol: NumberValueObject;
  glucose: NumberValueObject;
  systolic: NumberValueObject;
  diastolic: NumberValueObject;
  lifeEssentialStatus: LifeEssentialStatus;
  createdAt: DateValueObject;
  updatedAt: DateValueObject;
}
export class LifeEssential {
  private props: ILifeEsentialProps;

  constructor(props: ILifeEsentialProps) {
    this.props = props;
  }

  get id(): UUIDValueObject {
    return this.props.id;
  }

  get userId(): UUIDValueObject {
    return this.props.userId;
  }

  get oliveOil(): NumberValueObject {
    return this.props.oliveOil;
  }

  get greenLeafyVegetables(): NumberValueObject {
    return this.props.greenLeafyVegetables;
  }

  get otherVegetables(): NumberValueObject {
    return this.props.otherVegetables;
  }

  get berries(): NumberValueObject {
    return this.props.berries;
  }

  get otherFruit(): NumberValueObject {
    return this.props.otherFruit;
  }

  get meat(): NumberValueObject {
    return this.props.meat;
  }

  get fish(): NumberValueObject {
    return this.props.fish;
  }

  get chicken(): NumberValueObject {
    return this.props.chicken;
  }

  get cheese(): NumberValueObject {
    return this.props.cheese;
  }

  get butter(): NumberValueObject {
    return this.props.butter;
  }

  get beans(): NumberValueObject {
    return this.props.beans;
  }

  get wholeGrains(): NumberValueObject {
    return this.props.wholeGrains;
  }

  get sweetsAndPastries(): NumberValueObject {
    return this.props.sweetsAndPastries;
  }

  get nuts(): NumberValueObject {
    return this.props.nuts;
  }

  get fastFood(): NumberValueObject {
    return this.props.fastFood;
  }

  get alcohol(): NumberValueObject {
    return this.props.alcohol;
  }

  get physicalActivity(): NumberValueObject {
    return this.props.physicalActivity;
  }

  get nicotineExposure(): NicotineExposure {
    return this.props.nicotineExposure;
  }

  get sleep(): NumberValueObject {
    return this.props.sleep;
  }

  get bodyWeight(): NumberValueObject {
    return this.props.bodyWeight;
  }

  get height(): NumberValueObject {
    return this.props.height;
  }

  get cholesterol(): NumberValueObject {
    return this.props.cholesterol;
  }

  get glucose(): NumberValueObject {
    return this.props.glucose;
  }

  get systolic(): NumberValueObject {
    return this.props.systolic;
  }

  get diastolic(): NumberValueObject {
    return this.props.diastolic;
  }

  get lifeEssentialStatus(): LifeEssentialStatus {
    return this.props.lifeEssentialStatus;
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  public static create(props: ILifeEsentialProps) {
    const lifeEssential = new LifeEssential({ ...props });

    return Result.ok<LifeEssential>(lifeEssential);
  }
}
