import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

type NicotineExposureOptions =
  | 'NEVER_SMOKE'
  | 'FORMER_SMOKER_MORE_THAN_5'
  | 'FORMER_SMOKER_BETWEEEN_1_AND_5'
  | 'FORMER_SMOKER_LESS_THAN_1'
  | 'CURRENT_SMOKER';
interface INicotineExposureProps {
  value: string;
}
export class NicotineExposure extends ValueObject<INicotineExposureProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: INicotineExposureProps) {
    super(props);
  }

  private static isStatusValid(nicotineExposure: string) {
    if (
      !nicotineExposure ||
      (nicotineExposure !== 'NEVER_SMOKE' &&
        nicotineExposure !== 'FORMER_SMOKER_MORE_THAN_5' &&
        nicotineExposure !== 'FORMER_SMOKER_BETWEEEN_1_AND_5' &&
        nicotineExposure !== 'FORMER_SMOKER_LESS_THAN_1' &&
        nicotineExposure !== 'CURRENT_SMOKER')
    ) {
      return false;
    }

    return true;
  }

  public static create(
    nicotineExposure: NicotineExposureOptions | string
  ): Result<NicotineExposure> {
    if (!this.isStatusValid(nicotineExposure)) {
      return Result.fail<NicotineExposure>('Invalid Nicotine Exposure Type');
    }

    return Result.ok<NicotineExposure>(
      new NicotineExposure({ value: nicotineExposure })
    );
  }
}
