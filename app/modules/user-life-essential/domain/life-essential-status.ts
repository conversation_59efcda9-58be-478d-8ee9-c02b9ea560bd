import { Result } from '../../../shared/core/Result';
import { ValueObject } from '../../../shared/domain/ValueObject';

type LifeEssentialStatusOptions = 'ACTIVE' | 'EXPIRED' | 'ARCHIVED';
interface ILifeEssentialStatusProps {
  value: string;
}
export class LifeEssentialStatus extends ValueObject<ILifeEssentialStatusProps> {
  get value(): string {
    return this.props.value;
  }

  private constructor(props: ILifeEssentialStatusProps) {
    super(props);
  }

  private static isStatusValid(lifeEssentialStatus: string) {
    if (
      !lifeEssentialStatus ||
      (lifeEssentialStatus !== 'ACTIVE' &&
        lifeEssentialStatus !== 'EXPIRED' &&
        lifeEssentialStatus !== 'ARCHIVED')
    ) {
      return false;
    }

    return true;
  }

  public static create(
    status: LifeEssentialStatusOptions | string
  ): Result<LifeEssentialStatus> {
    if (!this.isStatusValid(status)) {
      return Result.fail<LifeEssentialStatus>('Invalid Life Essential Status');
    }

    return Result.ok<LifeEssentialStatus>(
      new LifeEssentialStatus({ value: status })
    );
  }
}
