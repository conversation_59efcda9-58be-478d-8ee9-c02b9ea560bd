export type LifeEssentialDTO = {
  id: string;
  userId: string;
  //Diet
  oliveOil: number;
  greenLeafyVegetables: number;
  otherVegetables: number;
  berries: number;
  otherFruit: number;
  meat: number;
  fish: number;
  chicken: number;
  cheese: number;
  butter: number;
  beans: number;
  wholeGrains: number;
  sweetsAndPastries: number;
  nuts: number;
  fastFood: number;
  alcohol: number;
  //PA (in minutes)
  physicalActivity: number;
  //Nicotine
  nicotineExposure: string;
  //Sleep Health (hours per night)
  sleep: number;
  //BMI
  bodyWeight: number; //kilograms
  height: number; // meters
  //Blood Lipids
  cholesterol: number;
  //Blood glucose
  glucose: number; //HbA1c
  //Blood Preasure
  systolic: number;
  diastolic: number;
  //metadata
  lifeEssentialStatus: string;
  createdAt: string;
  updatedAt: string;
};
