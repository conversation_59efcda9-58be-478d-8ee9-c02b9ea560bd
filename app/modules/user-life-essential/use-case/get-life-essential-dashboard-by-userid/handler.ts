import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { <PERSON>Handler } from '../../../../shared/infra/handler';
import { GetLifeEssentialDashboardByUserIdUseCase } from './use-case';
import { GetLifeEssentialDashboardByUserIdDTO } from './dto';
import AWS from 'aws-sdk';
import { LifeEssentialRepository } from '../../repo/life-essential-repo';
import { LifeEssentialDashboardMap } from '../../mappers/life-essential-dashboard-map';
import { LifeEssential } from '../../domain/LifeEssential';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { TokenServices } from '../../../../shared/services/token-services';
import { TokenMiddleware } from '../../../../shared/middlewares/token-middleware';

class Lambda extends BaseHandler implements LambdaInterface {
  private tokenMiddleware: TokenMiddleware;

  private getLifeEssentialDashboardByUserIdUseCase: GetLifeEssentialDashboardByUserIdUseCase;

  private init() {
    const tokenServices = new TokenServices();
    this.tokenMiddleware = new TokenMiddleware(tokenServices);
    const lifeEssentialRepository = new LifeEssentialRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.getLifeEssentialDashboardByUserIdUseCase =
      new GetLifeEssentialDashboardByUserIdUseCase(lifeEssentialRepository);
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = await this.mapEventToDTO(event);
      const result =
        await this.getLifeEssentialDashboardByUserIdUseCase.execute(dto);

      return this.ok(
        result.map((lifeEssential: LifeEssential) =>
          LifeEssentialDashboardMap.toDTO(lifeEssential)
        )
      );
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async mapEventToDTO(
    event: APIGatewayProxyEvent
  ): Promise<GetLifeEssentialDashboardByUserIdDTO> {
    const auth = event.headers['Authorization'];
    if (!auth) throw 'Invalid Request Headers';
    const id = await this.tokenMiddleware.getUserIdFromToken(auth);

    return {
      id,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
