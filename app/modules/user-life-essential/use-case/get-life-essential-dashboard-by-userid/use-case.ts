import { UUIDValueObject } from '../../../../shared/common-value-objects/uuid-value-object';
import { UseCase } from '../../../../shared/core/use-case';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { LifeEssential } from '../../domain/LifeEssential';
import { LifeEssentialStatus } from '../../domain/life-essential-status';
import { LifeEssentialRepository } from '../../repo/life-essential-repo';

import { GetLifeEssentialDashboardByUserIdDTO } from './dto';

export class GetLifeEssentialDashboardByUserIdUseCase
  implements
    UseCase<GetLifeEssentialDashboardByUserIdDTO, Promise<LifeEssential[]>>
{
  constructor(private lifeEssentialRepository: LifeEssentialRepository) {}

  @LogUseCaseDTO
  public execute(dto: GetLifeEssentialDashboardByUserIdDTO) {
    const id = this.mapDTOtLifeEssentialId(dto);
    const status = this.getLifeEssentialStatus();

    return this.lifeEssentialRepository.getLifeEssentialByUserId(id, status);
  }

  private mapDTOtLifeEssentialId(dto: GetLifeEssentialDashboardByUserIdDTO) {
    const id = UUIDValueObject.create('id', dto.id);
    if (id.isFailure) throw id.getErrorValue();

    return id.getValue();
  }

  private getLifeEssentialStatus() {
    const lifeEssentialStatus = LifeEssentialStatus.create('ACTIVE');
    if (lifeEssentialStatus.isFailure)
      throw lifeEssentialStatus.getErrorValue();

    return lifeEssentialStatus.getValue();
  }
}
