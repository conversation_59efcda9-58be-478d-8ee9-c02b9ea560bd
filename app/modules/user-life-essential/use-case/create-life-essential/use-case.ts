import { CreateLifeEssentialDTO } from './dto';
import { UseCase } from '../../../../shared/core/use-case';
import { LifeEssential } from '../../domain/LifeEssential';
import { LifeEssentialRepository } from '../../repo/life-essential-repo';
import { LifeEssentialMap } from '../../mappers/life-essential-map';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';

export class CreateLifeEssentialUseCase
  implements UseCase<CreateLifeEssentialDTO, Promise<LifeEssential>>
{
  constructor(private lifeEssentialRepository: LifeEssentialRepository) {}

  @LogUseCaseDTO
  public async execute(dto: CreateLifeEssentialDTO) {
    const lifeEssential = this.mapDTOtoLifeEssential(dto);
    const lifeEssentialCreated = await this.createLifeEssential(lifeEssential);

    return lifeEssentialCreated;
  }

  private mapDTOtoLifeEssential(dto: CreateLifeEssentialDTO) {
    const lifeEssential = LifeEssentialMap.toDomain({
      userId: dto.userId,
      oliveOil: dto.oliveOil,
      greenLeafyVegetables: dto.greenLeafyVegetables,
      otherVegetables: dto.otherVegetables,
      berries: dto.berries,
      otherFruit: dto.otherFruit,
      meat: dto.meat,
      fish: dto.fish,
      chicken: dto.chicken,
      cheese: dto.cheese,
      butter: dto.butter,
      beans: dto.beans,
      wholeGrains: dto.wholeGrains,
      sweetsAndPastries: dto.sweetsAndPastries,
      nuts: dto.nuts,
      fastFood: dto.fastFood,
      alcohol: dto.alcohol,
      physicalActivity: dto.physicalActivity,
      nicotineExposure: dto.nicotineExposure,
      sleep: dto.sleep,
      bodyWeight: dto.bodyWeight,
      height: dto.height,
      cholesterol: dto.cholesterol,
      glucose: dto.glucose,
      systolic: dto.systolic,
      diastolic: dto.diastolic,
      lifeEssentialStatus: 'ACTIVE',
    });

    return lifeEssential;
  }

  private async createLifeEssential(
    lifeEssential: LifeEssential
  ): Promise<LifeEssential> {
    const lifeEssentialCreated =
      await this.lifeEssentialRepository.createLifeEssential(lifeEssential);
    if (!lifeEssentialCreated) throw 'Error Creating Life Essential';

    return lifeEssentialCreated;
  }
}
