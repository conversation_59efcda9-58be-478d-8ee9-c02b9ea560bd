import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { CreateLifeEssentialUseCase } from './use-case';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { <PERSON>Handler } from '../../../../shared/infra/handler';
import { CreateLifeEssentialDTO } from './dto';
import AWS from 'aws-sdk';
import { LifeEssentialMap } from '../../mappers/life-essential-map';
import { LifeEssentialRepository } from '../../repo/life-essential-repo';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';

class Lambda extends BaseHandler implements LambdaInterface {
  private createLifeEssentialUseCase: CreateLifeEssentialUseCase;

  private init() {
    const lifeEssentialRepository = new LifeEssentialRepository(
      new AWS.DynamoDB(),
      process.env.TABLE_NAME || ''
    );
    this.createLifeEssentialUseCase = new CreateLifeEssentialUseCase(
      lifeEssentialRepository
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.createLifeEssentialUseCase.execute(dto);

      return this.ok(LifeEssentialMap.toDTO(result));
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): CreateLifeEssentialDTO {
    if (!event.body) {
      throw 'Fail to parse request body';
    }
    const lifeEssentialData = JSON.parse(event.body) as CreateLifeEssentialDTO;

    return {
      ...lifeEssentialData,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
