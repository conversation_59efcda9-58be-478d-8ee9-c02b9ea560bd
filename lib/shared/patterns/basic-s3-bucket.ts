import {
  Bucket,
  BucketProps,
  HttpMethods,
  BucketEncryption,
} from 'aws-cdk-lib/aws-s3';
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';

interface IBasicS3PropsProps {
  id: string;
  name: string;
  bucketsProps?: BucketProps;
}
export class BasicS3Bucket {
  readonly bucket: Bucket;

  constructor(scope: Construct, props: IBasicS3PropsProps) {
    // 👇 create bucket
    this.bucket = new Bucket(scope, props.id, {
      bucketName: props.name,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: false,
      versioned: false,
      publicReadAccess: false,
      encryption: BucketEncryption.S3_MANAGED,
      cors: [
        {
          allowedMethods: [HttpMethods.GET, HttpMethods.POST, HttpMethods.PUT],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
        },
      ],
      ...props.bucketsProps,
    });
  }
}
