import { Duration } from 'aws-cdk-lib';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import {
  NodejsFunction,
  NodejsFunctionProps,
} from 'aws-cdk-lib/aws-lambda-nodejs';
import { Construct } from 'constructs';
import * as path from 'path';
import { ServiceConfig } from '../service-name-util';

interface IBasicLambdaFunctionProps {
  id: string;
  name: string;
  handler: string;
  functionPath: string;
  functionProps?: NodejsFunctionProps;
}
export function BasicLambdaFunction(
  scope: Construct,
  props: IBasicLambdaFunctionProps
) {
  const entry = path.join(__dirname, props.functionPath);
  const config = new ServiceConfig();

  return new NodejsFunction(scope, props.id, {
    functionName: props.name,
    memorySize: 1024,
    timeout: Duration.seconds(30),
    runtime: Runtime.NODEJS_16_X,
    bundling: {
      minify: false,
      externalModules: ['aws-sdk'],
    },
    entry,
    ...(props.functionProps && { ...props.functionProps }),
    environment: {
      CLIENT: config.client,
      PROJECT: config.projectName,
      STAGE: config.env,
      ...(props.functionProps?.environment && {
        ...props.functionProps.environment,
      }),
    },
  });
}
