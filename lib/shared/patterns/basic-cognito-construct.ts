/**
 * Name: BASIC COGNITO POOL
 */
import { Duration, RemovalPolicy } from 'aws-cdk-lib';
import {
  AccountRecovery,
  UserPool,
  UserPoolClient,
  UserPoolProps,
} from 'aws-cdk-lib/aws-cognito';
import { Construct } from 'constructs';

interface IBasicCognitoProps {
  id: string;
  name: string;
  cognitoPoolProps?: UserPoolProps;
}
export class BasicCognito extends Construct {
  public pool: UserPool;

  public clientId: UserPoolClient;

  constructor(scope: Construct, id: string, props: IBasicCognitoProps) {
    super(scope, id);
    this.pool = new UserPool(this, props.id, {
      userPoolName: props.name,
      selfSignUpEnabled: false,
      signInAliases: {
        email: true,
        username: true,
      },
      autoVerify: {
        email: true,
      },
      standardAttributes: {
        email: {
          required: true,
          mutable: true,
        },
      },
      passwordPolicy: {
        minLength: 8,
        requireLowercase: true,
        requireDigits: true,
        requireUppercase: true,
        requireSymbols: false,
        tempPasswordValidity: Duration.days(15),
      },
      accountRecovery: AccountRecovery.EMAIL_ONLY,
      removalPolicy: RemovalPolicy.RETAIN,
      ...props.cognitoPoolProps,
    });
    this.clientId = this.pool.addClient(`${id}-client`, {
      userPoolClientName: `${id}-client`,
      authFlows: {
        userSrp: true,
      },
      refreshTokenValidity: Duration.days(30),
      authSessionValidity: Duration.minutes(15),
      idTokenValidity: Duration.minutes(60),
      accessTokenValidity: Duration.minutes(60),
      preventUserExistenceErrors: true,
      enableTokenRevocation: true,
    });
  }
}
