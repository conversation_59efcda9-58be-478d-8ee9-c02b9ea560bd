import {
  Bucket,
  BucketProps,
  HttpMethods,
  BucketEncryption,
  StorageClass,
} from 'aws-cdk-lib/aws-s3';
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';

interface IBasicS3BucketTempPropsProps {
  id: string;
  name: string;
  bucketsProps?: BucketProps;
}
export class BasicS3BucketTemp {
  readonly bucket: Bucket;

  constructor(scope: Construct, props: IBasicS3BucketTempPropsProps) {
    // 👇 create bucket
    this.bucket = new Bucket(scope, props.id, {
      bucketName: props.name,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: true,
      versioned: false,
      publicReadAccess: false,
      encryption: BucketEncryption.S3_MANAGED,
      cors: [
        {
          allowedMethods: [HttpMethods.GET, HttpMethods.POST, HttpMethods.PUT],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
        },
      ],
      lifecycleRules: [
        {
          abortIncompleteMultipartUploadAfter: cdk.Duration.days(90),
          expiration: cdk.Duration.days(365),
          transitions: [
            {
              storageClass: StorageClass.INFREQUENT_ACCESS,
              transitionAfter: cdk.Duration.days(30),
            },
          ],
        },
      ],
      ...props.bucketsProps,
    });
  }
}
