import { Queue } from 'aws-cdk-lib/aws-sqs';
import { Construct } from 'constructs';
import { ServiceConfig } from '../service-name-util';

interface IBasicSQSQueueProps {
  eventName: string;
}
export function BasicSQSQueue(
  scope: Construct,
  props: IBasicSQSQueueProps
): Queue {
  const service = new ServiceConfig();
  const queue = new Queue(
    scope,
    service.NameGenerator(`${props.eventName}-sqs-event`)
    //TODO: Add name
    // {
    //   queueName: props.eventName,
    // }
  );

  return queue;
}
