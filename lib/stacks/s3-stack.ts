import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';
import { BasicS3Bucket } from '../shared/patterns/basic-s3-bucket';
import { ServiceConfig } from '../shared/service-name-util';
import { BasicLambdaFunction } from '../shared/patterns/basic-lambda-function';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3n from 'aws-cdk-lib/aws-s3-notifications';
import { DynamoDBStack } from './dynamodb-stack';
import { SQSEventStack } from './events/sqs-event-stack';

interface IS3StackProps extends cdk.StackProps {
  dynamodbStack: DynamoDBStack;
  eventStack: SQSEventStack;
}
export class S3Stack extends cdk.Stack {
  private readonly dynamodbStack: DynamoDBStack;

  private readonly eventStack: SQSEventStack;

  public systemFilesBucket: Bucket;

  public medicalFilesBucketCR: Bucket;

  public medicalFilesBucketSA: Bucket;

  public quickSightMedicalFilesBucket: Bucket;

  public feedFilesBucket: Bucket;

  constructor(scope: Construct, id: string, props: IS3StackProps) {
    super(scope, id, props);
    this.dynamodbStack = props.dynamodbStack;
    this.eventStack = props.eventStack;
    this.addFeedFileBucket();
    this.addSystemFileBucket();
    this.addQuickSightMedicalFilesBucket();
    this.addElSalvadorMedicalFilesBucket();
    this.addCostaRicaMedicalFilesBucket();
    this.addPanamaMedicalFilesBucket();
    this.addGuatemalaMedicalFilesBucket();
    this.addHondurasMedicalFilesBucket();
  }

  private addFeedFileBucket() {
    const bucketName = new ServiceConfig().NameGenerator('feed-file');
    const s3Bucket = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
      bucketsProps: {
        publicReadAccess: true,
        objectOwnership: s3.ObjectOwnership.OBJECT_WRITER,
        // When recreating in prod, need to create this, then destroy it
        // blockPublicAccess: s3.BlockPublicAccess.BLOCK_ACLS,
      },
    });
    this.feedFilesBucket = s3Bucket.bucket;
  }

  private addSystemFileBucket() {
    const bucketName = new ServiceConfig().NameGenerator('system-file');
    const s3Bucket = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
      bucketsProps: {
        publicReadAccess: true,
        objectOwnership: s3.ObjectOwnership.OBJECT_WRITER,
        // When recreating in prod, need to create this, then destroy it
        // blockPublicAccess: s3.BlockPublicAccess.BLOCK_ACLS,
      },
    });
    this.systemFilesBucket = s3Bucket.bucket;
  }

  private addQuickSightMedicalFilesBucket() {
    const bucketName = new ServiceConfig().NameGenerator(
      'quicksight-meidical-file'
    );
    const s3 = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
    });
    s3.bucket.grantRead(new iam.AccountRootPrincipal());
    this.quickSightMedicalFilesBucket = s3.bucket;
  }

  private addElSalvadorMedicalFilesBucket() {
    const bucketName = new ServiceConfig().NameGenerator('sa-medical-files');
    const s3 = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
      bucketsProps: {
        publicReadAccess: false,
      },
    });
    s3.bucket.grantRead(new iam.AccountRootPrincipal());
    this.medicalFilesBucketSA = s3.bucket;
    this.addTriggerForNewMedicalFile(
      'medical-data-el-salvador-update',
      'EL_SALVADOR',
      this.medicalFilesBucketSA
    );
  }

  private addCostaRicaMedicalFilesBucket() {
    const bucketName = new ServiceConfig().NameGenerator('cr-medical-files');
    const s3 = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
    });
    s3.bucket.grantRead(new iam.AccountRootPrincipal());
    this.medicalFilesBucketCR = s3.bucket;
    this.addTriggerForNewMedicalFile(
      'medical-data-costa-rica-update',
      'COSTA_RICA',
      this.medicalFilesBucketCR
    );
  }

  private addPanamaMedicalFilesBucket() {
    const bucketName = new ServiceConfig().NameGenerator('pa-medical-files');
    const s3 = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
    });
    s3.bucket.grantRead(new iam.AccountRootPrincipal());
    this.medicalFilesBucketCR = s3.bucket;
    this.addTriggerForNewMedicalFile(
      'medical-data-panama-update',
      'PANAMA',
      this.medicalFilesBucketCR
    );
  }

  private addGuatemalaMedicalFilesBucket() {
    const bucketName = new ServiceConfig().NameGenerator('gua-medical-files');
    const s3 = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
    });
    s3.bucket.grantRead(new iam.AccountRootPrincipal());
    this.medicalFilesBucketCR = s3.bucket;
    this.addTriggerForNewMedicalFile(
      'medical-data-guatemala-update',
      'GUATEMALA',
      this.medicalFilesBucketCR
    );
  }

  private addHondurasMedicalFilesBucket() {
    const bucketName = new ServiceConfig().NameGenerator('hon-medical-files');
    const s3 = new BasicS3Bucket(this, {
      id: bucketName,
      name: bucketName,
    });
    s3.bucket.grantRead(new iam.AccountRootPrincipal());
    this.medicalFilesBucketCR = s3.bucket;
    this.addTriggerForNewMedicalFile(
      'medical-data-honduras-update',
      'HONDURAS',
      this.medicalFilesBucketCR
    );
  }

  private addTriggerForNewMedicalFile(
    functionId: string,
    country: string,
    bucket: Bucket
  ) {
    const functionName = new ServiceConfig().NameGenerator(functionId);
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/events/update-data-from-file/handler.ts',
      functionProps: {
        environment: {
          COUNTRY: country,
          ANONYMOUS_MEDICAL_TABLE_NAME:
            this.dynamodbStack.anonymousMedicalData.tableName,
          USER_MEDICAL_TABLE_NAME: this.dynamodbStack.userMedicalData.tableName,
          USER_TABLE_NAME: this.dynamodbStack.userTable.tableName,
          SQS_URL: this.eventStack.postNotificationQueue.queueUrl,
        },
      },
    });
    this.dynamodbStack.anonymousMedicalData.grantWriteData(resolver);
    this.dynamodbStack.userMedicalData.grantWriteData(resolver);
    this.dynamodbStack.userTable.grantReadData(resolver);
    this.eventStack.postNotificationQueue.grantSendMessages(resolver);
    bucket.grantRead(resolver);
    bucket.addEventNotification(
      s3.EventType.OBJECT_CREATED,
      new s3n.LambdaDestination(resolver),
      // 👇 only invoke lambda if object matches the filter
      { suffix: '.xlsx' }
    );
  }
}
