import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { Methods } from '../types';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly apigw;

  private termsResource: Resource;

  private userTermsResource: Resource;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    const apiName = new ServiceConfig().NameGenerator('terms-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(this, 'main-user-pool', {
      cognitoUserPools: [
        this.cognitoStack.mainUserPool,
        this.cognitoStack.adminUserPool,
      ],
    });
    this.initUserResource();
  }

  private initUserResource() {
    this.termsResource = this.addResource('terms');
    this.userTermsResource = this.addResource('user-terms');
    this.addGetLatestTerms(this.termsResource);
    this.addPostUserTermsAcceptance(this.userTermsResource);
  }

  private addGetLatestTerms(termsResource: Resource) {
    const resource = termsResource.addResource('latest');
    const functionName = new ServiceConfig().NameGenerator('latest-terms-get');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/terms/use-cases/get-latest-terms/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.termsTable.tableName,
        },
      },
    });
    this.dynamodbStack.termsTable.grantReadData(resolver);
    this.addMethod(resource, 'GET', resolver);
  }

  private addPostUserTermsAcceptance(userTermsResource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'user-terms-acceptance-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/terms/use-cases/user-accept-terms/handler.ts',
      functionProps: {
        environment: {
          USER_TERMS_ACCEPTANCE_TABLE_NAME:
            this.dynamodbStack.userTermsAcceptanceTable.tableName,
          USER_TABLE_NAME: this.dynamodbStack.userTable.tableName,
        },
      },
    });
    this.dynamodbStack.userTermsAcceptanceTable.grantWriteData(resolver);
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.addMethod(userTermsResource, 'POST', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
