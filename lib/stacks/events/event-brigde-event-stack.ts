import * as cdk from 'aws-cdk-lib';
import { StackProps } from 'aws-cdk-lib';
import { DynamoDBStack } from '../dynamodb-stack';
import { Construct } from 'constructs';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';
import { S3Stack } from '../s3-stack';
import { SQSEventStack } from './sqs-event-stack';
import { env } from 'process';

interface IEventBridgeEventStackProps extends StackProps {
  dynamodbStack: DynamoDBStack;
  s3Stack: S3Stack;
  eventStack: SQSEventStack;
}
export class EventBridgeEventStack extends cdk.Stack {
  readonly dynamodbStack: DynamoDBStack;

  readonly s3Stack: S3Stack;

  private readonly eventStack: SQSEventStack;

  constructor(
    scope: Construct,
    id: string,
    props: IEventBridgeEventStackProps
  ) {
    super(scope, id, props);
    this.dynamodbStack = props.dynamodbStack;
    this.s3Stack = props.s3Stack;
    this.eventStack = props.eventStack;
    this.addRuleForQuickSightSourceDataUpdate();
    this.addRuleForCompleteChallengesOnEndDate();
    this.addRuleForUpdatesForChallenges();
    this.addRuleForQuickSightLifeEssentialDataUpdate();
  }

  private addRuleForQuickSightSourceDataUpdate() {
    const functionName = new ServiceConfig().NameGenerator(
      'quicksight-data-source-event-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/events/quicksight-medical-source-update/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.anonymousMedicalData.tableName,
          BUCKET_NAME: this.s3Stack.quickSightMedicalFilesBucket.bucketName,
        },
        memorySize: 3008,
        timeout: cdk.Duration.minutes(15),
      },
    });
    this.dynamodbStack.anonymousMedicalData.grantReadData(resolver);
    this.s3Stack.quickSightMedicalFilesBucket.grantPut(resolver);
    const schedule = new Rule(this, 'quicksight-medical-source-update', {
      schedule: Schedule.expression('cron(0 12,19 * * ? *)'),
      enabled: env.STAGE === 'prod' ? true : false,
    });
    schedule.addTarget(new LambdaFunction(resolver));
  }

  private addRuleForQuickSightLifeEssentialDataUpdate() {
    const functionName = new ServiceConfig().NameGenerator(
      'quicksight-life-essential-event-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user-life-essential/events/qs-life-essential-report/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.lifeEssentialTable.tableName,
          BUCKET_NAME: this.s3Stack.quickSightMedicalFilesBucket.bucketName,
        },
        memorySize: 3008,
        timeout: cdk.Duration.minutes(15),
      },
    });
    this.dynamodbStack.lifeEssentialTable.grantReadData(resolver);
    this.s3Stack.quickSightMedicalFilesBucket.grantPut(resolver);
    const schedule = new Rule(this, 'quicksight-life-essential-update', {
      schedule: Schedule.expression('cron(0 12,19 * * ? *)'),
      enabled: env.STAGE === 'prod' ? true : false,
    });
    schedule.addTarget(new LambdaFunction(resolver));
  }

  private addRuleForCompleteChallengesOnEndDate() {
    const functionName = new ServiceConfig().NameGenerator(
      'complete-challenges-event-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/events/complete-challenge-by-end-date/handler.ts',
      functionProps: {
        environment: {
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
          CHALLENGE_PROGRESS_TABLE_NAME:
            this.dynamodbStack.challengeProgressTable.tableName,
          //TODO: remove this dependency
          SQS_URL2: this.eventStack.createNotificationQueue.queueUrl,
          SQS_URL: this.eventStack.postNotificationQueue.queueUrl,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadWriteData(resolver);
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.dynamodbStack.challengeProgressTable.grantReadData(resolver);
    this.eventStack.createNotificationQueue.grantSendMessages(resolver);
    this.eventStack.postNotificationQueue.grantSendMessages(resolver);
    const schedule = new Rule(this, 'complete-challenges-update-schedule', {
      schedule: Schedule.expression('cron(0 7 * * ? *)'),
      enabled: env.STAGE === 'prod' ? true : false,
    });
    schedule.addTarget(new LambdaFunction(resolver));
  }

  private addRuleForUpdatesForChallenges() {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-notifications-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/events/challenge-notifications/handler.ts',
      functionProps: {
        environment: {
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
          SQS_URL: this.eventStack.postNotificationQueue.queueUrl,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadWriteData(resolver);
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.eventStack.postNotificationQueue.grantSendMessages(resolver);
    const schedule = new Rule(this, 'challenge-notifications-schedule', {
      schedule: Schedule.expression('cron(0 7 * * ? *)'),
      enabled: env.STAGE === 'prod' ? true : false,
    });
    schedule.addTarget(new LambdaFunction(resolver));
  }
}
