import * as cdk from 'aws-cdk-lib';
import { StackProps } from 'aws-cdk-lib';
import { PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import { Construct } from 'constructs';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { BasicSQSQueue } from '../../shared/patterns/basic-sqs-queue';
import { SendEmailPolicyStatement } from '../../shared/patterns/send-email-policy-statement';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { env } from '../../../bin/env';

interface ISQSEventStackProps extends StackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class SQSEventStack extends cdk.Stack {
  public sendEmailQueue: Queue;

  public createNotificationQueue: Queue;

  public createAuthUserQueue: Queue;

  public createUserQueue: Queue;

  public createSupplierQueue: Queue;

  public sendInAppNotificationQueue: Queue;

  public postNotificationQueue: Queue;

  readonly dynamodbStack: DynamoDBStack;

  readonly cognitoStack: CognitoStack;

  constructor(scope: Construct, id: string, props: ISQSEventStackProps) {
    super(scope, id, props);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    this.sendEmailSQSEvent(this);
    this.createNotificationSQSEvent(this);
    this.sendInAppNotificationSQSEvent(this);
    this.postNotificationSQSEvent(this);
  }

  private sendEmailSQSEvent(scope: Construct) {
    this.sendEmailQueue = BasicSQSQueue(scope, {
      eventName: 'send-email',
    });
    const functionName = new ServiceConfig().NameGenerator(
      'send-email-event-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath: '../../../app/shared/events/send-email/handler.ts',
      functionProps: {
        environment: {
          SOURCE_EMAIL: env.NOTIFICATION_EMAIL,
          PLATFORM_URL: env.PLATFORM_URL,
          SES_REGION: env.SES_AWS_REGION,
          EMAIL_NOTIFICATION_ENABLED: env.STAGE === 'prod' ? 'ON' : 'OFF',
        },
      },
    });
    resolver.addToRolePolicy(new PolicyStatement(SendEmailPolicyStatement()));
    resolver.addEventSource(
      new SqsEventSource(this.sendEmailQueue, {
        batchSize: 10,
      })
    );
  }

  private createNotificationSQSEvent(scope: Construct) {
    this.createNotificationQueue = BasicSQSQueue(scope, {
      eventName: 'create-notification',
    });
    const functionName = new ServiceConfig().NameGenerator(
      'create-notification-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/notifications/use-cases/create-notification/handler.ts',
      functionProps: {
        environment: {
          NOTIFICATION_TABLE_NAME:
            this.dynamodbStack.notificationsTable.tableName,
        },
      },
    });
    this.dynamodbStack.notificationsTable.grantFullAccess(resolver);
    resolver.addEventSource(
      new SqsEventSource(this.createNotificationQueue, {
        batchSize: 1,
      })
    );
  }

  private sendInAppNotificationSQSEvent(scope: Construct) {
    this.sendInAppNotificationQueue = BasicSQSQueue(scope, {
      eventName: 'send-notification',
    });
    const functionName = new ServiceConfig().NameGenerator(
      'send-notification-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/notifications/use-cases/create-notification/handler.ts',
      functionProps: {
        environment: {
          NOTIFICATION_TABLE_NAME:
            this.dynamodbStack.notificationsTable.tableName,
        },
      },
    });
    this.dynamodbStack.notificationsTable.grantFullAccess(resolver);
    resolver.addEventSource(
      new SqsEventSource(this.sendInAppNotificationQueue, {
        batchSize: 10,
      })
    );
  }

  private postNotificationSQSEvent(scope: Construct) {
    this.postNotificationQueue = BasicSQSQueue(scope, {
      eventName: 'post-notification',
    });
    const functionName = new ServiceConfig().NameGenerator(
      'post-notification-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath: '../../../app/shared/events/send-notification/handler.ts',
      functionProps: {
        environment: {
          USER_TABLE_NAME: this.dynamodbStack.userTable.tableName,
          EMAIL_SQS_URL: this.sendEmailQueue.queueUrl,
          IN_APP_SQS_URL: this.sendInAppNotificationQueue.queueUrl,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadData(resolver);
    this.sendEmailQueue.grantSendMessages(resolver);
    this.sendInAppNotificationQueue.grantSendMessages(resolver);
    resolver.addEventSource(
      new SqsEventSource(this.postNotificationQueue, {
        batchSize: 10,
      })
    );
  }
}
