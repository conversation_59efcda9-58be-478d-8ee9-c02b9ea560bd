import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { SQSEventStack } from '../events/sqs-event-stack';
import { S3Stack } from '../s3-stack';
import { Methods } from '../types';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  s3Stack: S3Stack;
  cognitoStack: CognitoStack;
  sQSEventStack: SQSEventStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly sQSEventStack: SQSEventStack;

  private readonly s3Stack;

  public sendEmailQueue: Queue;

  private readonly apigw;

  private contentArchiveResource: Resource;

  private contentCreateResource: Resource;

  private contentUpdateResource: Resource;

  private contentFeedResource: Resource;

  private contentResource: Resource;

  private updateLikeCountResource: Resource;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    this.sQSEventStack = props.sQSEventStack;
    this.s3Stack = props.s3Stack;
    const apiName = new ServiceConfig().NameGenerator('content-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(this, 'main-content-pool', {
      cognitoUserPools: [
        this.cognitoStack.mainUserPool,
        this.cognitoStack.adminUserPool,
      ],
    });
    this.initUserResource();
  }

  private initUserResource() {
    this.contentArchiveResource = this.addResource('content-archive');
    this.contentCreateResource = this.addResource('content-create');
    this.contentUpdateResource = this.addResource('content-update');
    this.contentFeedResource = this.addResource('content-feed');
    this.contentResource = this.addResource('content');
    this.updateLikeCountResource = this.addResource('update-like-count');
    this.updateLikeResource();
    this.addArchiveContentPost();
    this.addCreateContentPost();
    this.addUpdateContentPost();
    this.addFeedContentPost();
    this.addGetContentByIdGet();
  }

  private updateLikeResource() {
    const functionName = new ServiceConfig().NameGenerator(
      'update-like-count-put'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/content/use-cases/update-like-count/handler.ts',
      functionProps: {
        environment: {
          CONTENT_TABLE_NAME: this.dynamodbStack.contentTable.tableName,
        },
      },
    });
    this.dynamodbStack.contentTable.grantFullAccess(resolver);
    this.addMethod(this.updateLikeCountResource, 'PUT', resolver);
  }

  private addArchiveContentPost() {
    const functionName = new ServiceConfig().NameGenerator(
      'archive-content-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/content/use-cases/archive-content/handler.ts',
      functionProps: {
        environment: {
          CONTENT_TABLE_NAME: this.dynamodbStack.contentTable.tableName,
        },
      },
    });
    this.dynamodbStack.contentTable.grantFullAccess(resolver);
    this.addMethod(this.contentArchiveResource, 'POST', resolver);
  }

  private addCreateContentPost() {
    const functionName = new ServiceConfig().NameGenerator(
      'create-content-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/content/use-cases/create-content/handler.ts',
      functionProps: {
        environment: {
          CONTENT_TABLE_NAME: this.dynamodbStack.contentTable.tableName,
          BUCKET_NAME: this.s3Stack.feedFilesBucket.bucketName,
        },
      },
    });
    this.dynamodbStack.contentTable.grantFullAccess(resolver);
    this.s3Stack.feedFilesBucket.grantPut(resolver);
    this.s3Stack.feedFilesBucket.grantPutAcl(resolver);
    this.s3Stack.feedFilesBucket.grantWrite(resolver);
    this.s3Stack.feedFilesBucket.grantReadWrite(resolver);
    this.s3Stack.feedFilesBucket.grantRead(resolver);
    this.addMethod(this.contentCreateResource, 'POST', resolver);
  }

  private addUpdateContentPost() {
    const functionName = new ServiceConfig().NameGenerator(
      'update-content-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/content/use-cases/update-content/handler.ts',
      functionProps: {
        environment: {
          CONTENT_TABLE_NAME: this.dynamodbStack.contentTable.tableName,
        },
      },
    });
    this.dynamodbStack.contentTable.grantFullAccess(resolver);
    this.addMethod(this.contentUpdateResource, 'POST', resolver);
  }

  private addFeedContentPost() {
    const functionName = new ServiceConfig().NameGenerator('feed-content-post');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/content/use-cases/get-latest-content/handler.ts',
      functionProps: {
        environment: {
          CONTENT_TABLE_NAME: this.dynamodbStack.contentTable.tableName,
        },
      },
    });
    this.dynamodbStack.contentTable.grantFullAccess(resolver);
    this.addMethod(this.contentFeedResource, 'POST', resolver);
  }

  private addGetContentByIdGet() {
    const functionName = new ServiceConfig().NameGenerator('get-content-by-id');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/content/use-cases/get-content-by-id/handler.ts',
      functionProps: {
        environment: {
          CONTENT_TABLE_NAME: this.dynamodbStack.contentTable.tableName,
        },
      },
    });
    this.dynamodbStack.contentTable.grantReadData(resolver);
    this.addUnsecuredMethod(this.contentResource, 'GET', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addUnsecuredMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        ...options,
      }
    );
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
