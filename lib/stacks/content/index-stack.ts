import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { S3Stack } from '../s3-stack';
import { ApiGWLambdaConstruct } from './apigw-lambda-construct';
import { DynamoDBStack } from '../dynamodb-stack';
import { SQSEventStack } from '../events/sqs-event-stack';

interface IContentStackProps extends StackProps {
  cognitoStack: CognitoStack;
  dynamodbStack: DynamoDBStack;
  s3Stack: S3Stack;
  eventStack: SQSEventStack;
}
export class ContentStack extends Stack {
  private readonly dynamodbStack: DynamoDBStack;

  private readonly cognitoStack: CognitoStack;

  private readonly s3Stack: S3Stack;

  private readonly sQSEventStack: SQSEventStack;

  constructor(scope: Construct, id: string, props: IContentStackProps) {
    super(scope, id, props);
    this.cognitoStack = props.cognitoStack;
    this.dynamodbStack = props.dynamodbStack;
    this.dynamodbStack = props.dynamodbStack;
    this.s3Stack = props.s3Stack;
    this.sQSEventStack = props.eventStack;
    const service = new ServiceConfig();
    new ApiGWLambdaConstruct(
      this,
      service.StackNameGenerator('content-apigw-lambda'),
      {
        dynamodbStack: this.dynamodbStack,
        s3Stack: this.s3Stack,
        cognitoStack: this.cognitoStack,
        sQSEventStack: this.sQSEventStack,
      }
    );
  }
}
