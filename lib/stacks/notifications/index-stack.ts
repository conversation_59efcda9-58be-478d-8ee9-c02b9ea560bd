import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { ApiGWLambdaConstruct } from './apigw-lambda-construct';
import { DynamoDBStack } from '../dynamodb-stack';

interface INotificationsStackProps extends StackProps {
  cognitoStack: CognitoStack;
  dynamodbStack: DynamoDBStack;
}
export class NotificationsStack extends Stack {
  private readonly dynamodbStack: DynamoDBStack;

  private readonly cognitoStack: CognitoStack;

  constructor(scope: Construct, id: string, props: INotificationsStackProps) {
    super(scope, id, props);
    this.cognitoStack = props.cognitoStack;
    this.dynamodbStack = props.dynamodbStack;
    this.dynamodbStack = props.dynamodbStack;
    const service = new ServiceConfig();
    new ApiGWLambdaConstruct(
      this,
      service.StackNameGenerator('notifications-apigw-lambda'),
      {
        dynamodbStack: this.dynamodbStack,
        cognitoStack: this.cognitoStack,
      }
    );
  }
}
