import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { Methods } from '../types';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  public sendEmailQueue: Queue;

  private readonly apigw;

  private notificationResource: Resource;

  private notificationsGetResource: Resource;

  private notificationsReadResource: Resource;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    const apiName = new ServiceConfig().NameGenerator('notifications-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(
      this,
      'main-notifications-pool',
      {
        cognitoUserPools: [
          this.cognitoStack.mainUserPool,
          this.cognitoStack.adminUserPool,
        ],
      }
    );
    this.initUserResource();
  }

  private initUserResource() {
    this.notificationResource = this.addResource('notification');
    this.addNotificationDelete();
    this.notificationsGetResource = this.addResource('notifications-get');
    this.notificationsReadResource = this.addResource('notifications-read');
    this.addNotificationGetByUserId();
    this.addNotificationMarkAsRead();
  }

  private addNotificationDelete() {
    const functionName = new ServiceConfig().NameGenerator(
      'archive-notification-delete'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/notifications/use-cases/archive-notification/handler.ts',
      functionProps: {
        environment: {
          NOTIFICATION_TABLE_NAME:
            this.dynamodbStack.notificationsTable.tableName,
        },
      },
    });
    this.dynamodbStack.notificationsTable.grantFullAccess(resolver);
    this.addMethod(this.notificationResource, 'DELETE', resolver);
  }

  private addNotificationGetByUserId() {
    const functionName = new ServiceConfig().NameGenerator(
      'get-notifications-by-user-id'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/notifications/use-cases/get-notifications-by-user-id/handler.ts',
      functionProps: {
        environment: {
          NOTIFICATION_TABLE_NAME:
            this.dynamodbStack.notificationsTable.tableName,
        },
      },
    });
    this.dynamodbStack.notificationsTable.grantReadData(resolver);
    this.addMethod(this.notificationsGetResource, 'POST', resolver);
  }

  private addNotificationMarkAsRead() {
    const functionName = new ServiceConfig().NameGenerator(
      'mark-notification-as-readed'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/notifications/use-cases/mark-notification-as-readed/handler.ts',
      functionProps: {
        environment: {
          NOTIFICATION_TABLE_NAME:
            this.dynamodbStack.notificationsTable.tableName,
        },
      },
    });
    this.dynamodbStack.notificationsTable.grantFullAccess(resolver);
    this.addMethod(this.notificationsReadResource, 'POST', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
