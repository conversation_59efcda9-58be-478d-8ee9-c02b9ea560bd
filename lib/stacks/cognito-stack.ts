import { Stack, StackProps } from 'aws-cdk-lib';
import {
  CfnUserPool,
  Mfa,
  UserPool,
  UserPoolClient,
} from 'aws-cdk-lib/aws-cognito';
import { Construct } from 'constructs';
import { BasicCognito } from '../shared/patterns/basic-cognito-construct';
import { ServiceConfig } from '../shared/service-name-util';
import { env } from '../../bin/env';

export class CognitoStack extends Stack {
  public mainUserPool: UserPool;

  public mainUserPoolClientId: UserPoolClient;

  public adminUserPool: UserPool;

  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id, props);
    const cognitoUserPoolName = new ServiceConfig().NameGenerator('user-pool');
    const user = new BasicCognito(this, cognitoUserPoolName, {
      id: cognitoUserPoolName,
      name: cognitoUser<PERSON>oolName,
      cognitoPoolProps: {
        userInvitation: {
          emailSubject: 'Bienvenido a BeBetter!',
          emailBody: `<PERSON><PERSON>, Bienvenido a BeBetter! 

          Tu contraseña temporal es: {####}
          
          Ingresa a la plataforma: ${env.PLATFORM_URL}
                    
                    
          {username}`,
          smsMessage: `Tu contraseña temporal es: {####}
          Ingresa a la plataforma: ${env.PLATFORM_URL}
          {username}`,
        },
        userVerification: {
          emailSubject: 'BeBetter Health Monitor | Restablecer tu contraseña',
          emailBody: `Tu código para restablecer tu contraseña es: {####}`,
          smsMessage: `Tu código para restablecer tu contraseña: {####}`,
        },
      },
    });
    this.mainUserPool = user.pool;
    this.mainUserPoolClientId = user.clientId;
    const cfnUserPool = this.mainUserPool.node.defaultChild as CfnUserPool;
    cfnUserPool.addOverride('Properties.EmailConfiguration', {
      EmailSendingAccount: 'DEVELOPER',
      ReplyToEmailAddress: env.NOTIFICATION_EMAIL,
      SourceArn: env.NOTIFICATION_EMAIL_ARN,
    });
    const cognitoAdminPoolName = new ServiceConfig().NameGenerator(
      'admin-user-pool'
    );
    const admin = new BasicCognito(this, cognitoAdminPoolName, {
      id: cognitoAdminPoolName,
      name: cognitoAdminPoolName,
      cognitoPoolProps: {
        mfa: Mfa.REQUIRED,
        mfaSecondFactor: {
          sms: true,
          otp: false,
        },
      },
    });
    this.adminUserPool = admin.pool;
  }
}
