import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { SQSEventStack } from '../events/sqs-event-stack';
import { S3Stack } from '../s3-stack';
import { Methods } from '../types';
import { env } from '../../../bin/env';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  s3Stack: S3Stack;
  cognitoStack: CognitoStack;
  sQSEventStack: SQSEventStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly sQSEventStack: SQSEventStack;

  private readonly s3Stack;

  public sendEmailQueue: Queue;

  private readonly apigw;

  private userResource: Resource;

  private userTermsResource: Resource;

  private activateUserResource: Resource;

  private userProfilePictureResource: Resource;

  private userPasswordResource: Resource;

  private userListByStatus: Resource;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    this.sQSEventStack = props.sQSEventStack;
    this.s3Stack = props.s3Stack;
    const apiName = new ServiceConfig().NameGenerator('user-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(this, 'main-user-pool', {
      cognitoUserPools: [
        this.cognitoStack.mainUserPool,
        this.cognitoStack.adminUserPool,
      ],
    });
    this.initUserResource();
    this.initUserProfilePictureResource();
    this.initUserPasswordResource();
    this.initUserListResource();
  }

  private initUserResource() {
    this.userResource = this.addResource('user');
    this.activateUserResource = this.addResource('activate-use');
    this.addUserDetailsGet();
    this.addUpdateUser();
    this.addDeleteUser();
    this.addCreateUser();
    this.addBatchCreateUser();
    this.addActivateUser();
  }

  private initUserProfilePictureResource() {
    this.userProfilePictureResource = this.addResource('user-profile-picture');
    this.addUpdateUserProfilePicture();
  }

  private initUserPasswordResource() {
    this.userPasswordResource = this.addResource('user-password');
    this.addUpdateUserPassword();
    this.addResteUserPassword();
  }

  private initUserListResource() {
    this.userListByStatus = this.addResource('user-list-by-status');
    this.addGetUserListByStatus();
  }

  private addUserDetailsGet() {
    const functionName = new ServiceConfig().NameGenerator('user-detail-get');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/get-user-data/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadData(resolver);
    this.addMethod(this.userResource, 'GET', resolver);
  }

  private addUpdateUser() {
    const functionName = new ServiceConfig().NameGenerator('user-update');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/edit-user-data/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.addMethod(this.userResource, 'PUT', resolver);
  }

  private addCreateUser() {
    const functionName = new ServiceConfig().NameGenerator('user-create');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/create-user/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.cognitoStack.adminUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.userResource, 'POST', resolver);
  }

  private addBatchCreateUser() {
    const resource = this.userResource.addResource('batch');
    const functionName = new ServiceConfig().NameGenerator('batch-user-create');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/batch-create-user/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.cognitoStack.adminUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(resource, 'POST', resolver);
  }

  private addDeleteUser() {
    const functionName = new ServiceConfig().NameGenerator('user-delete');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/archive-user/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
          COGNITO_ADMIN_POOL: this.cognitoStack.adminUserPool.userPoolId,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.cognitoStack.adminUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.userResource, 'DELETE', resolver);
  }

  private addActivateUser() {
    const functionName = new ServiceConfig().NameGenerator('user-activate');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/activate-user/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
          COGNITO_ADMIN_POOL: this.cognitoStack.adminUserPool.userPoolId,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.cognitoStack.adminUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.activateUserResource, 'PUT', resolver);
  }

  private addGetUserListByStatus() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-list-by-status-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/get-users-by-status/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.addMethod(this.userListByStatus, 'GET', resolver);
  }

  private addUpdateUserProfilePicture() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-profile-picture-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/edit-user-profile-picture/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
          BUCKET_NAME: this.s3Stack.systemFilesBucket.bucketName,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.s3Stack.systemFilesBucket.grantPut(resolver);
    this.s3Stack.systemFilesBucket.grantPutAcl(resolver);
    this.addMethod(this.userProfilePictureResource, 'PUT', resolver);
  }

  private addUpdateUserPassword() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-password-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/edit-user-password/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
          COGNITO_ADMIN_POOL: this.cognitoStack.adminUserPool.userPoolId,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.cognitoStack.adminUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.userPasswordResource, 'PUT', resolver);
  }

  private addResteUserPassword() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-password-reset-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user/use-cases/reset-user-password/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userTable.tableName,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
          COGNITO_ADMIN_POOL: this.cognitoStack.adminUserPool.userPoolId,
          SQS_URL: this.sQSEventStack.sendEmailQueue.queueUrl,
          PLATFORM_URL: env.PLATFORM_URL,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadWriteData(resolver);
    this.sQSEventStack.sendEmailQueue.grantSendMessages(resolver);
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.cognitoStack.adminUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.userPasswordResource, 'POST', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
