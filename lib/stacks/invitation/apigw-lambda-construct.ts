import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { Methods } from '../types';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly apigw;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    const apiName = new ServiceConfig().NameGenerator('participant-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(this, 'main-challenge-pool', {
      cognitoUserPools: [
        this.cognitoStack.mainUserPool,
        this.cognitoStack.adminUserPool,
      ],
    });
    this.initInvitationResource();
  }

  private initInvitationResource() {
    const invitationResource = this.addResource('invitation');
    this.addAcceptChallengeInvitation(invitationResource);
    this.addRejectChallengeInvitation(invitationResource);
  }

  private addAcceptChallengeInvitation(resource: Resource) {
    const accent = resource.addResource('accept');
    const functionName = new ServiceConfig().NameGenerator(
      'accept-invitation-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/participant/post-accept-invitation/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.participantTable.tableName,
        },
      },
    });
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.addMethod(accent, 'PUT', resolver);
  }

  private addRejectChallengeInvitation(resource: Resource) {
    const reject = resource.addResource('reject');
    const functionName = new ServiceConfig().NameGenerator(
      'reject-invitation-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/participant/post-reject-invitation/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.participantTable.tableName,
        },
      },
    });
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.addMethod(reject, 'PUT', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
