import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { ApiGWLambdaConstruct } from './apigw-lambda-construct';
import { DynamoDBStack } from '../dynamodb-stack';
import { CognitoStack } from '../cognito-stack';
import { S3Stack } from '../s3-stack';
import { SQSEventStack } from '../events/sqs-event-stack';

interface IChallengeStackProps extends StackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
  s3Stack: S3Stack;
  eventStack: SQSEventStack;
}
export class ChallengeStack extends Stack {
  private readonly dynamodbStack: DynamoDBStack;

  private readonly cognitoStack: CognitoStack;

  private readonly s3Stack: S3Stack;

  private readonly sQSEventStack: SQSEventStack;

  constructor(scope: Construct, id: string, props: IChallengeStackProps) {
    super(scope, id, props);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    this.s3Stack = props.s3Stack;
    this.sQSEventStack = props.eventStack;
    const service = new ServiceConfig();
    new ApiGWLambdaConstruct(
      this,
      service.StackNameGenerator('challengel-apigw-lambda'),
      {
        dynamodbStack: this.dynamodbStack,
        cognitoStack: this.cognitoStack,
        s3Stack: this.s3Stack,
        sQSEventStack: this.sQSEventStack,
      }
    );
  }
}
