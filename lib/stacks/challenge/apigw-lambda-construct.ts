import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { Methods } from '../types';
import { S3Stack } from '../s3-stack';
import { SQSEventStack } from '../events/sqs-event-stack';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
  s3Stack: S3Stack;
  sQSEventStack: SQSEventStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly s3Stack;

  private readonly sQSEventStack: SQSEventStack;

  private readonly apigw;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    this.s3Stack = props.s3Stack;
    this.sQSEventStack = props.sQSEventStack;
    const apiName = new ServiceConfig().NameGenerator('challenge-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(this, 'main-challenge-pool', {
      cognitoUserPools: [
        this.cognitoStack.mainUserPool,
        this.cognitoStack.adminUserPool,
      ],
    });
    this.initChallengeResource();
    this.initUserChallengeList();
    this.initChallengeProgressResource();
    this.initLeaderBoardResource();
    this.initParticipantResource();
  }

  private initChallengeResource() {
    const challengeResource = this.addResource('challenge');
    this.addGetChallenge(challengeResource);
    this.addCreateChallenge(challengeResource);
    this.addUpdateChallenge(challengeResource);
    this.addDeleteChallenge(challengeResource);
    this.completeChallengeResource(challengeResource);
  }

  private initParticipantResource() {
    const participantResource = this.addResource('participant');
    this.addUpdateParticipantWinnerChallenge(participantResource);
    this.addUpdateParticipantInitialValue(participantResource);
  }

  private addUpdateParticipantWinnerChallenge(resource: Resource) {
    const winnerResource = resource.addResource('winner');
    const functionName = new ServiceConfig().NameGenerator(
      'participant-set-winner-put'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/participant/set-winner/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.participantTable.tableName,
        },
      },
    });
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.addMethod(winnerResource, 'PUT', resolver);
  }

  private addUpdateParticipantInitialValue(resource: Resource) {
    const initialValueResource = resource.addResource('initial-value');
    const functionName = new ServiceConfig().NameGenerator(
      'participant-initial-value-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/participant/post-participant-initial-value/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.participantTable.tableName,
        },
      },
    });
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.addMethod(initialValueResource, 'POST', resolver);
  }

  private completeChallengeResource(resource: Resource) {
    const challengeResource = resource.addResource('complete');
    this.addCompleteChallenge(challengeResource);
  }

  private addCompleteChallenge(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-complete'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge/complete/handler.ts',
      functionProps: {
        environment: {
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
          CHALLENGE_PROGRESS_TABLE_NAME:
            this.dynamodbStack.challengeProgressTable.tableName,
          SQS_URL: this.sQSEventStack.postNotificationQueue.queueUrl,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadWriteData(resolver);
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.dynamodbStack.challengeProgressTable.grantReadData(resolver);
    this.sQSEventStack.postNotificationQueue.grantSendMessages(resolver);
    this.addMethod(resource, 'POST', resolver);
  }

  private initChallengeProgressResource() {
    const challengeProgressResource = this.addResource('challenge-progress');
    this.addCreateChallengeProgress(challengeProgressResource);
    this.addDeleteChallengeProgress(challengeProgressResource);
    this.addGetChallengeProgressByChallengeId(challengeProgressResource);
    this.addGetChallengeProgressByChallengeIdAndUserId(
      challengeProgressResource
    );
    this.addBatchCreateChallengeProgress(challengeProgressResource);
  }

  private initLeaderBoardResource() {
    const leaderBoardResource = this.addResource('leader-board');
    this.addGetLeaderBoardByChallengeId(leaderBoardResource);
  }

  private initUserChallengeList() {
    const userChallengeListResource = this.addResource('user-challenge-list');
    this.addGetChallengeListByStatus(userChallengeListResource);
  }

  private addGetChallenge(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator('challenge-get');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge/get-by-id/handler.ts',
      functionProps: {
        environment: {
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadData(resolver);
    this.dynamodbStack.participantTable.grantReadData(resolver);
    this.addMethod(resource, 'GET', resolver);
  }

  private addCreateChallenge(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-create-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge/create/handler.ts',
      functionProps: {
        environment: {
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
          SQS_URL: this.sQSEventStack.postNotificationQueue.queueUrl,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadWriteData(resolver);
    this.dynamodbStack.participantTable.grantWriteData(resolver);
    this.sQSEventStack.postNotificationQueue.grantSendMessages(resolver);
    this.addMethod(resource, 'POST', resolver);
  }

  private addUpdateChallenge(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-create-put'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge/update/handler.ts',
      functionProps: {
        environment: {
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadWriteData(resolver);
    this.dynamodbStack.participantTable.grantReadWriteData(resolver);
    this.addMethod(resource, 'PUT', resolver);
  }

  private addDeleteChallenge(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator('challenge-delete');
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge/archive/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadWriteData(resolver);
    this.addMethod(resource, 'DELETE', resolver);
  }

  private addCreateChallengeProgress(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-progress-create-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge-progress/create/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.challengeProgressTable.tableName,
          BUCKET_NAME: this.s3Stack.systemFilesBucket.bucketName,
        },
      },
    });
    this.dynamodbStack.challengeProgressTable.grantReadWriteData(resolver);
    this.s3Stack.systemFilesBucket.grantPut(resolver);
    this.s3Stack.systemFilesBucket.grantPutAcl(resolver);
    this.addMethod(resource, 'POST', resolver);
  }

  private addBatchCreateChallengeProgress(resource: Resource) {
    const batchChallengeProgressResource = resource.addResource(
      'batch-challenge-progress'
    );
    const functionName = new ServiceConfig().NameGenerator(
      'batch-challenge-progress-create-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge-progress/batch-create/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.challengeProgressTable.tableName,
          BUCKET_NAME: this.s3Stack.systemFilesBucket.bucketName,
        },
      },
    });
    this.dynamodbStack.challengeProgressTable.grantReadWriteData(resolver);
    this.s3Stack.systemFilesBucket.grantPut(resolver);
    this.s3Stack.systemFilesBucket.grantPutAcl(resolver);
    this.addMethod(batchChallengeProgressResource, 'POST', resolver);
  }

  private addDeleteChallengeProgress(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-progress-delete'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge-progress/archive/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.challengeProgressTable.tableName,
        },
      },
    });
    this.dynamodbStack.challengeProgressTable.grantReadWriteData(resolver);
    this.addMethod(resource, 'DELETE', resolver);
  }

  private addGetLeaderBoardByChallengeId(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'leader-board-by-challenge-id-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/leader-board/get-leader-board-by-challenge-id/handler.ts',
      functionProps: {
        environment: {
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          CHALLENGE_PROGRESS_TABLE_NAME:
            this.dynamodbStack.challengeProgressTable.tableName,
        },
      },
    });
    this.dynamodbStack.participantTable.grantReadData(resolver);
    this.dynamodbStack.challengeTable.grantReadData(resolver);
    this.dynamodbStack.challengeProgressTable.grantReadData(resolver);
    this.addMethod(resource, 'GET', resolver);
  }

  private addGetChallengeProgressByChallengeId(resource: Resource) {
    const challengeProgressResource = resource.addResource('user-progress');
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-progress-list-by-user-id-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge-progress/get-challenge-progress-by-user-id/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.challengeProgressTable.tableName,
        },
      },
    });
    this.dynamodbStack.challengeProgressTable.grantReadData(resolver);
    this.addMethod(challengeProgressResource, 'GET', resolver);
  }

  private addGetChallengeProgressByChallengeIdAndUserId(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-progress-list-by-challenge-id-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge-progress/get-challenge-progress-by-id/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.challengeProgressTable.tableName,
        },
      },
    });
    this.dynamodbStack.challengeProgressTable.grantReadData(resolver);
    this.addMethod(resource, 'GET', resolver);
  }

  private addGetChallengeListByStatus(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'challenge-list-by-userId-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/challenge/use-case/challenge/get-user-list/handler.ts',
      functionProps: {
        environment: {
          CHALLENGE_TABLE_NAME: this.dynamodbStack.challengeTable.tableName,
          PARTICIPANT_TABLE_NAME: this.dynamodbStack.participantTable.tableName,
        },
      },
    });
    this.dynamodbStack.challengeTable.grantReadData(resolver);
    this.dynamodbStack.participantTable.grantReadData(resolver);
    this.addMethod(resource, 'GET', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
