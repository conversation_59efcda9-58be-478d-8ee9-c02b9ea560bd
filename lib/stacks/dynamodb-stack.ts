import * as cdk from 'aws-cdk-lib';
import { AttributeType, Table } from 'aws-cdk-lib/aws-dynamodb';
import { Construct } from 'constructs';
import { BasicDynamoDBTable } from '../shared/patterns/basic-dynamodb-table';
import { ServiceConfig } from '../shared/service-name-util';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';

export class DynamoDBStack extends cdk.Stack {
  public userTable: Table;

  public notificationsTable: Table;

  public contentTable: Table;

  public anonymousMedicalData: Table;

  public userMedicalData: Table;

  public lifeEssentialTable: Table;

  public userLabExamnResultTable: Table;

  public challengeTable: Table;

  public participantTable: Table;

  public challengeProgressTable: Table;

  public termsTable: Table;

  public userTermsAcceptanceTable: Table;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);
    this.addUserResources();
    this.addAnonymousMedicalDataResources();
    this.addUserMedicalDataResources();
    this.addULifeEssentialResources();
    this.addUserLabExamResultResources();
    this.addChallengeResources();
    this.addChallengeProgressResources();
    this.addParticipantResources();
    this.addNotificationsResources();
    this.addContentResources();
    this.addTermsResources();
    this.addUserTermsAcceptanceResources();
  }

  private addTermsResources() {
    const tableName = new ServiceConfig().NameGenerator('terms-table');
    this.termsTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.termsTable.addGlobalSecondaryIndex({
      indexName: 'termsStatus-createdAt-index',
      partitionKey: {
        name: 'termsStatus',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addUserTermsAcceptanceResources() {
    const tableName = new ServiceConfig().NameGenerator(
      'user-terms-acceptance-table'
    );
    this.userTermsAcceptanceTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'userId', type: AttributeType.STRING },
      dynamoDBProps: {
        sortKey: {
          name: 'acceptedAt',
          type: dynamodb.AttributeType.STRING,
        },
      },
    });
    this.userTermsAcceptanceTable.addGlobalSecondaryIndex({
      indexName: 'userTermsAcceptanceStatus-createdAt-index',
      partitionKey: {
        name: 'userTermsAcceptanceStatus',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addContentResources() {
    const tableName = new ServiceConfig().NameGenerator('content-table');
    this.contentTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.contentTable.addGlobalSecondaryIndex({
      indexName: 'createdAt-index',
      partitionKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.contentTable.addGlobalSecondaryIndex({
      indexName: 'contentStatus-createdAt-index',
      partitionKey: {
        name: 'contentStatus',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.contentTable.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addNotificationsResources() {
    const tableName = new ServiceConfig().NameGenerator('notifications-table');
    this.notificationsTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.notificationsTable.addGlobalSecondaryIndex({
      indexName: 'userId-notificationStatus-index',
      partitionKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'notificationStatus',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addUserResources() {
    const tableName = new ServiceConfig().NameGenerator('user-table');
    this.userTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.userTable.addGlobalSecondaryIndex({
      indexName: 'userStatus-createdAt-index',
      partitionKey: {
        name: 'userStatus',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: 'createdAt', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.userTable.addGlobalSecondaryIndex({
      indexName: 'email-createdAt-index',
      partitionKey: {
        name: 'email',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: 'createdAt', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.userTable.addGlobalSecondaryIndex({
      indexName: 'country-userStatus-index',
      partitionKey: {
        name: 'country',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: 'userStatus', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addAnonymousMedicalDataResources() {
    const tableName = new ServiceConfig().NameGenerator(
      'anonymous-medical-data-table'
    );
    this.anonymousMedicalData = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
  }

  private addUserMedicalDataResources() {
    const tableName = new ServiceConfig().NameGenerator(
      'user-medical-data-table'
    );
    this.userMedicalData = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.userMedicalData.addGlobalSecondaryIndex({
      indexName: 'documentId-createdAt-index',
      partitionKey: {
        name: 'documentId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: 'createdAt', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addULifeEssentialResources() {
    const tableName = new ServiceConfig().NameGenerator('life-essential-table');
    this.lifeEssentialTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.lifeEssentialTable.addGlobalSecondaryIndex({
      indexName: 'userId-lifeEssentialStatus-index',
      partitionKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'lifeEssentialStatus',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.lifeEssentialTable.addGlobalSecondaryIndex({
      indexName: 'lifeEssentialStatus-index',
      partitionKey: {
        name: 'lifeEssentialStatus',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addUserLabExamResultResources() {
    const tableName = new ServiceConfig().NameGenerator(
      'user-lab-examn-result-table'
    );
    this.userLabExamnResultTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.userLabExamnResultTable.addGlobalSecondaryIndex({
      indexName: 'userId-userLabExamnResultStatus-index',
      partitionKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'userLabExamnResultStatus',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addChallengeResources() {
    const tableName = new ServiceConfig().NameGenerator('challenge-table');
    this.challengeTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.challengeTable.addGlobalSecondaryIndex({
      indexName: 'challengeStatus-createdAt-index',
      partitionKey: {
        name: 'challengeStatus',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: 'createdAt', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.challengeTable.addGlobalSecondaryIndex({
      indexName: 'challengeStatus-endDate-index',
      partitionKey: {
        name: 'challengeStatus',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: 'endDate', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.challengeTable.addGlobalSecondaryIndex({
      indexName: 'userId-challengeStatus-index',
      partitionKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: 'challengeStatus', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addChallengeProgressResources() {
    const tableName = new ServiceConfig().NameGenerator(
      'challenge-progress-table'
    );
    this.challengeProgressTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.challengeProgressTable.addGlobalSecondaryIndex({
      indexName: 'challengeId-challengeProgressStatus-index',
      partitionKey: {
        name: 'challengeId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'challengeProgressStatus',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.challengeProgressTable.addGlobalSecondaryIndex({
      indexName: 'userId-challengeProgressStatus-index',
      partitionKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'challengeProgressStatus',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.challengeProgressTable.addGlobalSecondaryIndex({
      indexName: 'challengeId-userId-index',
      partitionKey: {
        name: 'challengeId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }

  private addParticipantResources() {
    const tableName = new ServiceConfig().NameGenerator('participant-table');
    this.participantTable = BasicDynamoDBTable(this, {
      id: tableName,
      name: tableName,
      partitionKey: { name: 'id', type: AttributeType.STRING },
    });
    this.participantTable.addGlobalSecondaryIndex({
      indexName: 'challengeId-participantStatus-index',
      partitionKey: {
        name: 'challengeId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'participantStatus',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.participantTable.addGlobalSecondaryIndex({
      indexName: 'challengeId-index',
      partitionKey: {
        name: 'challengeId',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
    this.participantTable.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: {
        name: 'userId',
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }
}
