import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { ApiGWLambdaConstruct } from './apigw-lambda-construct';
import { DynamoDBStack } from '../dynamodb-stack';
import { CognitoStack } from '../cognito-stack';

interface ILifeEssentialStackProps extends StackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class LifeEssentialStack extends Stack {
  private readonly dynamodbStack: DynamoDBStack;

  private readonly cognitoStack: CognitoStack;

  constructor(scope: Construct, id: string, props: ILifeEssentialStackProps) {
    super(scope, id, props);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    const service = new ServiceConfig();
    new ApiGWLambdaConstruct(
      this,
      service.StackNameGenerator('life-essential-apigw-lambda'),
      {
        dynamodbStack: this.dynamodbStack,
        cognitoStack: this.cognitoStack,
      }
    );
  }
}
