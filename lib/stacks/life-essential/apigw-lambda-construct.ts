import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { DynamoDBStack } from '../dynamodb-stack';
import { Methods } from '../types';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly apigw;

  private lifeEssentialResource: Resource;

  private lifeEssentialDashboardResource: Resource;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    const apiName = new ServiceConfig().NameGenerator('life-essential-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(
      this,
      'main-life-eesential-pool',
      {
        cognitoUserPools: [
          this.cognitoStack.mainUserPool,
          this.cognitoStack.adminUserPool,
        ],
      }
    );
    this.initUserResource();
    this.initUserProfilePictureResource();
  }

  private initUserResource() {
    this.lifeEssentialResource = this.addResource('life-essential');
    this.addCreateUser();
  }

  private initUserProfilePictureResource() {
    this.lifeEssentialDashboardResource = this.addResource(
      'life-essential-dashboard'
    );
    this.addGetUserListByStatus();
  }

  private addCreateUser() {
    const functionName = new ServiceConfig().NameGenerator(
      'life-essential-create-post'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user-life-essential/use-case/create-life-essential/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.lifeEssentialTable.tableName,
        },
      },
    });
    this.dynamodbStack.lifeEssentialTable.grantReadWriteData(resolver);
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.lifeEssentialResource, 'POST', resolver);
  }

  private addGetUserListByStatus() {
    const functionName = new ServiceConfig().NameGenerator(
      'life-essential-dashboard-by-userid-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/user-life-essential/use-case/get-life-essential-dashboard-by-userid/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.lifeEssentialTable.tableName,
        },
      },
    });
    this.dynamodbStack.lifeEssentialTable.grantReadData(resolver);
    this.addMethod(this.lifeEssentialDashboardResource, 'GET', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
