import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { CognitoStack } from '../cognito-stack';
import { ApiGW<PERSON>ambdaConstruct as MedicalDataConstruct } from './medical-data/apigw-lambda-construct';
import { ApiGWLambdaConstruct as UserLabExamResultConstruct } from './user-lab-exam-result/apigw-lambda-construct';
import { DynamoDBStack } from '../dynamodb-stack';

interface IMedicalDataStackProps extends StackProps {
  cognitoStack: CognitoStack;
  dynamodbStack: DynamoDBStack;
}
export class MedicalDataStack extends Stack {
  private readonly dynamodbStack: DynamoDBStack;

  private readonly cognitoStack: CognitoStack;

  constructor(scope: Construct, id: string, props: IMedicalDataStackProps) {
    super(scope, id, props);
    this.cognitoStack = props.cognitoStack;
    this.dynamodbStack = props.dynamodbStack;
    const service = new ServiceConfig();
    new MedicalDataConstruct(
      this,
      service.StackNameGenerator('medical-data-apigw-lambda'),
      {
        dynamodbStack: this.dynamodbStack,
        cognitoStack: this.cognitoStack,
      }
    );
    new UserLabExamResultConstruct(
      this,
      service.StackNameGenerator('user-lab-examn-result-apigw-lambda'),
      {
        dynamodbStack: this.dynamodbStack,
        cognitoStack: this.cognitoStack,
      }
    );
  }
}
