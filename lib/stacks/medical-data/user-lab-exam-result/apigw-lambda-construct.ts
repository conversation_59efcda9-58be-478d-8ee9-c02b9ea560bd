import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../../shared/service-name-util';
import { CognitoStack } from '../../cognito-stack';
import { DynamoDBStack } from '../../dynamodb-stack';
import { Methods } from '../../types';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly apigw;

  private userLabExamResultResource: Resource;

  private userLabExamResultListByStatus: Resource;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    const apiName = new ServiceConfig().NameGenerator(
      'user-lab-examn-result-api'
    );
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(
      this,
      'main-user-lab-examn-result-pool',
      {
        cognitoUserPools: [this.cognitoStack.mainUserPool],
      }
    );
    this.initUserLabExamResultResource();
    this.initUserLabExamResultListResource();
  }

  private initUserLabExamResultResource() {
    this.userLabExamResultResource = this.addResource('user-lab-examn-result');
    this.addCreateUserLabExamResult();
    this.addUpdateUserLabExamResult();
    this.addDeleteUserLabExamResult();
  }

  private initUserLabExamResultListResource() {
    this.userLabExamResultListByStatus = this.addResource(
      'user-lab-examn-result-list-by-status'
    );
    this.addGetUserLabExamResultListByStatus();
  }

  private addUpdateUserLabExamResult() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-lab-examn-result-update'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/user-lab-exam-result/edit-user-lab-exam-result/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userLabExamnResultTable.tableName,
        },
      },
    });
    this.dynamodbStack.userLabExamnResultTable.grantReadWriteData(resolver);
    this.addMethod(this.userLabExamResultResource, 'PUT', resolver);
  }

  private addCreateUserLabExamResult() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-lab-examn-result-create'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/user-lab-exam-result/create-user-lab-exam-result/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userLabExamnResultTable.tableName,
          USER_TABLE_NAME: this.dynamodbStack.userTable.tableName,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadData(resolver);
    this.dynamodbStack.userLabExamnResultTable.grantReadWriteData(resolver);
    this.addMethod(this.userLabExamResultResource, 'POST', resolver);
  }

  private addDeleteUserLabExamResult() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-lab-examn-result-delete'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/user-lab-exam-result/archive-user-lab-exam-result/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userLabExamnResultTable.tableName,
        },
      },
    });
    this.dynamodbStack.userLabExamnResultTable.grantReadWriteData(resolver);
    this.addMethod(this.userLabExamResultResource, 'DELETE', resolver);
  }

  private addGetUserLabExamResultListByStatus() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-lab-examn-result-list-by-status-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/user-lab-exam-result/get-user-lab-exam-result-by-status/handler.ts',
      functionProps: {
        environment: {
          TABLE_NAME: this.dynamodbStack.userLabExamnResultTable.tableName,
        },
      },
    });
    this.dynamodbStack.userLabExamnResultTable.grantReadWriteData(resolver);
    this.addMethod(this.userLabExamResultListByStatus, 'POST', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
