import {
  Resource,
  LambdaIntegration,
  MethodOptions,
  CognitoUserPoolsAuthorizer,
} from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { BasicAPIGWEndpoint } from '../../../shared/patterns/basic-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../../../shared/service-name-util';
import { CognitoStack } from '../../cognito-stack';
import { DynamoDBStack } from '../../dynamodb-stack';
import { Methods } from '../../types';
import { PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { env } from '../../../../bin/env';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  cognitoStack: CognitoStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly cognitoStack;

  private readonly apigw;

  private quickSightStudiesResource: Resource;

  private quickSightExamsResource: Resource;

  private quickSightProgressResource: Resource;

  private userMedicalDataResource: Resource;

  private readonly auth;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.cognitoStack = props.cognitoStack;
    const apiName = new ServiceConfig().NameGenerator('medical-data-api');
    this.apigw = new BasicAPIGWEndpoint(this, apiName, {
      id: apiName,
      name: apiName,
    });
    this.auth = new CognitoUserPoolsAuthorizer(this, 'main-user-pool', {
      cognitoUserPools: [
        this.cognitoStack.mainUserPool,
        this.cognitoStack.adminUserPool,
      ],
    });
    this.initQuickSightStudiesResource();
    this.initQuickSightProgressResource();
    this.initQuickSightExamsResource();
    this.initUserMedicalDataResource();
    this.initUserMedicalRecordResource();
  }

  private initQuickSightStudiesResource() {
    this.quickSightStudiesResource = this.addResource(
      'quicksight-studies-embed-dashboard-url'
    );
    this.addGetStudiesDashboardEmbedURL();
  }

  private initQuickSightProgressResource() {
    this.quickSightProgressResource = this.addResource(
      'quicksight-progress-embed-dashboard-url'
    );
    this.addGetProgressDashboardEmbedURL();
  }

  private initQuickSightExamsResource() {
    this.quickSightExamsResource = this.addResource(
      'quicksight-exams-embed-dashboard-url'
    );
    this.addGetExamsDashboardEmbedURL();
  }

  private initUserMedicalDataResource() {
    this.userMedicalDataResource = this.addResource('user-medical-data');
    this.addGetUserMedicalData();
  }

  private addGetUserMedicalData() {
    const functionName = new ServiceConfig().NameGenerator(
      'user-medical-data-by-identification-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/medical-data/get-user-health-report/handler.ts',
      functionProps: {
        environment: {
          USER_MEDICAL_TABLE_NAME: this.dynamodbStack.userMedicalData.tableName,
          USER_TABLE_NAME: this.dynamodbStack.userTable.tableName,
          USER_LAB_EXAM_RESULT_TABLE_NAME:
            this.dynamodbStack.userLabExamnResultTable.tableName,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadData(resolver);
    this.dynamodbStack.userLabExamnResultTable.grantReadData(resolver);
    this.dynamodbStack.userMedicalData.grantReadData(resolver);
    this.addMethod(this.userMedicalDataResource, 'POST', resolver);
  }

  private addGetStudiesDashboardEmbedURL() {
    const functionName = new ServiceConfig().NameGenerator(
      'quicksight-studies-dashboard-embed-url-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/medical-data/get-quicksight-embed-url/handler.ts',
      functionProps: {
        environment: {
          REGION: env.AWS_REGION,
          ACCOUNT_ID: env.AWS_ACCOUNT_ID,
          DASHBOARD_ID: env.QS_STUDIES_DASHBOARD_ID,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
          CLIENT_ID: this.cognitoStack.mainUserPoolClientId.userPoolClientId,
        },
      },
    });
    // Add permissions to call GetDashboardEmbedUrl action in QuickSight
    resolver.addToRolePolicy(
      new PolicyStatement({
        actions: [
          'quicksight:GenerateEmbedUrlForRegisteredUser',
          'quicksight:ListUsers',
        ],
        resources: [`*`], // Replace with specific ARN of the QuickSight dashboard, if needed
      })
    );
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.quickSightStudiesResource, 'GET', resolver);
  }

  private addGetExamsDashboardEmbedURL() {
    const functionName = new ServiceConfig().NameGenerator(
      'quicksight-examns-dashboard-embed-url-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/medical-data/get-quicksight-embed-url/handler.ts',
      functionProps: {
        environment: {
          REGION: env.AWS_REGION,
          ACCOUNT_ID: env.AWS_ACCOUNT_ID,
          DASHBOARD_ID: env.QS_EXAMNS_DASHBOARD_ID,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
          CLIENT_ID: this.cognitoStack.mainUserPoolClientId.userPoolClientId,
        },
      },
    });
    // Add permissions to call GetDashboardEmbedUrl action in QuickSight
    resolver.addToRolePolicy(
      new PolicyStatement({
        actions: [
          'quicksight:GenerateEmbedUrlForRegisteredUser',
          'quicksight:ListUsers',
        ],
        resources: [`*`], // Replace with specific ARN of the QuickSight dashboard, if needed
      })
    );
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.quickSightExamsResource, 'GET', resolver);
  }

  private addGetProgressDashboardEmbedURL() {
    const functionName = new ServiceConfig().NameGenerator(
      'quicksight-progress-dashboard-embed-url-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/medical-data/get-quicksight-embed-url/handler.ts',
      functionProps: {
        environment: {
          REGION: env.AWS_REGION,
          ACCOUNT_ID: env.AWS_ACCOUNT_ID,
          DASHBOARD_ID: env.QS_PROGRESS_DASHBOARD_ID,
          COGNITO_POOL: this.cognitoStack.mainUserPool.userPoolId,
          CLIENT_ID: this.cognitoStack.mainUserPoolClientId.userPoolClientId,
        },
      },
    });
    // Add permissions to call GetDashboardEmbedUrl action in QuickSight
    resolver.addToRolePolicy(
      new PolicyStatement({
        actions: [
          'quicksight:GenerateEmbedUrlForRegisteredUser',
          'quicksight:ListUsers',
        ],
        resources: [`*`], // Replace with specific ARN of the QuickSight dashboard, if needed
      })
    );
    this.cognitoStack.mainUserPool.grant(resolver, ...['cognito-idp:*']);
    this.addMethod(this.quickSightProgressResource, 'GET', resolver);
  }

  private initUserMedicalRecordResource() {
    const medicalRecordResource = this.addResource('medical-record');
    this.addGetUserMedicalRecord(medicalRecordResource);
  }

  private addGetUserMedicalRecord(resource: Resource) {
    const functionName = new ServiceConfig().NameGenerator(
      'user-medical-record-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/medical-data/use-cases/medical-data/get-user-medical-record/handler.ts',
      functionProps: {
        environment: {
          USER_MEDICAL_TABLE_NAME: this.dynamodbStack.userMedicalData.tableName,
          USER_TABLE_NAME: this.dynamodbStack.userTable.tableName,
          USER_LAB_EXAM_RESULT_TABLE_NAME:
            this.dynamodbStack.userLabExamnResultTable.tableName,
        },
      },
    });
    this.dynamodbStack.userTable.grantReadData(resolver);
    this.dynamodbStack.userLabExamnResultTable.grantReadData(resolver);
    this.dynamodbStack.userMedicalData.grantReadData(resolver);
    this.addMethod(resource, 'GET', resolver);
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }

  private addMethod(
    resource: Resource,
    method: Methods,
    resolver: IFunction,
    options?: MethodOptions
  ) {
    resource.addMethod(
      method,
      new LambdaIntegration(resolver, { proxy: true }),
      {
        authorizer: this.auth,
        ...options,
      }
    );
  }
}
