name: Run CD<PERSON> checks

on:
  pull_request:
    branches:
      - dev
jobs:
  build:
    runs-on: ubuntu-latest
    env:
      AWS_ACCOUNT_ID: ${{ secrets.DEV_AWS_ACCESS_KEY_ID }}
      AWS_REGION: ${{ secrets.DEV_AWS_REGION }}
      CLIENT: ${{ secrets.CLIENT }}
      PROJECT: ${{ secrets.PROJECT }}
      STAGE: ${{ secrets.DEV_STAGE }}
      QS_PROGRESS_DASHBOARD_ID: ${{ secrets.QS_PROGRESS_DASHBOARD_ID }}
      QS_STUDIES_DASHBOARD_ID: ${{ secrets.QS_STUDIES_DASHBOARD_ID }}
      QS_EXAMNS_DASHBOARD_ID: ${{ secrets.QS_EXAMNS_DASHBOARD_ID }} 
      SES_AWS_REGION: ${{ secrets.SES_AWS_REGION }}
      PLATFORM_URL: ${{ secrets.DEV_PLATFORM_URL }}
      NOTIFICATION_EMAIL: ${{ secrets.NOTIFICATION_EMAIL }}
      NOTIFICATION_EMAIL_ARN: ${{ secrets.NOTIFICATION_EMAIL_ARN }}
    steps:
      - name: Checkout CDK Repo
        uses: actions/checkout@v3
      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: "18"
      - name: Install modules
        run: npm ci
      - name: Install esbuild
        run: npm install esbuild --global
      - name: Install CDK
        run: npm install cdk --global
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@master
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.DEV_AWS_REGION }}
      - name: Typecheck
        run: npm run typecheck
      - name: Lint Code
        run: npm run lint
      - name: Run CDK Synth
        run: cdk synth
      - name: Run CDK Diff
        run: cdk diff
