import * as dotenv from 'dotenv';
import { createEnv } from '@t3-oss/env-core';
import { z } from 'zod';

dotenv.config();
export const env = createEnv({
  server: {
    AWS_ACCOUNT_ID: z.string().min(10),
    AWS_REGION: z.string().min(1),
    CLIENT: z.string().min(1),
    PROJECT: z.string().min(1),
    STAGE: z.string().min(1),
    QS_PROGRESS_DASHBOARD_ID: z.string().min(1),
    QS_STUDIES_DASHBOARD_ID: z.string().min(1),
    QS_EXAMNS_DASHBOARD_ID: z.string().min(1),
    NOTIFICATION_EMAIL: z.string().email(),
    NOTIFICATION_EMAIL_ARN: z.string().min(10),
    PLATFORM_URL: z.string().url(),
    SES_AWS_REGION: z.string().min(6),
  },

  /**
   * The prefix that client-side variables must have. This is enforced both at
   * a type-level and at runtime.
   */
  clientPrefix: 'PUBLIC_',

  client: {},

  /**
   * What object holds the environment variables at runtime. This is usually
   * `process.env` or `import.meta.env`.
   */
  runtimeEnv: {
    AWS_ACCOUNT_ID: process.env.AWS_ACCOUNT_ID,
    AWS_REGION: process.env.AWS_REGION,
    CLIENT: process.env.CLIENT,
    PROJECT: process.env.PROJECT,
    STAGE: process.env.STAGE,
    QS_PROGRESS_DASHBOARD_ID: process.env.QS_PROGRESS_DASHBOARD_ID,
    QS_STUDIES_DASHBOARD_ID: process.env.QS_STUDIES_DASHBOARD_ID,
    QS_EXAMNS_DASHBOARD_ID: process.env.QS_EXAMNS_DASHBOARD_ID,
    NOTIFICATION_EMAIL: process.env.NOTIFICATION_EMAIL,
    NOTIFICATION_EMAIL_ARN: process.env.NOTIFICATION_EMAIL_ARN,
    PLATFORM_URL: process.env.PLATFORM_URL,
    SES_AWS_REGION: process.env.SES_AWS_REGION,
  },

  /**
   * By default, this library will feed the environment variables directly to
   * the Zod validator.
   *
   * This means that if you have an empty string for a value that is supposed
   * to be a number (e.g. `PORT=` in a ".env" file), Zod will incorrectly flag
   * it as a type mismatch violation. Additionally, if you have an empty string
   * for a value that is supposed to be a string with a default value (e.g.
   * `DOMAIN=` in an ".env" file), the default value will never be applied.
   *
   * In order to solve these issues, we recommend that all new projects
   * explicitly specify this option as true.
   */
  emptyStringAsUndefined: true,
});
