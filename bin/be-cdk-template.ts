import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { CognitoStack } from '../lib/stacks/cognito-stack';
import { ServiceConfig } from '../lib/shared/service-name-util';
import { UserStack } from '../lib/stacks/user/index-stack';
import { DynamoDBStack } from '../lib/stacks/dynamodb-stack';
import { S3Stack } from '../lib/stacks/s3-stack';
import { SQSEventStack } from '../lib/stacks/events/sqs-event-stack';
import { EventBridgeEventStack } from '../lib/stacks/events/event-brigde-event-stack';
import { MedicalDataStack } from '../lib/stacks/medical-data/index-stack';
import { LifeEssentialStack } from '../lib/stacks/life-essential/index-stack';
import { ChallengeStack } from '../lib/stacks/challenge/index-stack';
import { ContentStack } from '../lib/stacks/content/index-stack';
import { NotificationsStack } from '../lib/stacks/notifications/index-stack';
import { env } from './env';
import { InvitationStack } from '../lib/stacks/invitation/index-stack';
import { TermsStack } from '../lib/stacks/terms/index-stack';

const app = new cdk.App();
if (env.AWS_ACCOUNT_ID && env.AWS_REGION) {
  const service = new ServiceConfig();
  const cognitoStack = new CognitoStack(
    app,
    service.StackNameGenerator('cognito')
  );
  const dynamodbStack = new DynamoDBStack(
    app,
    service.StackNameGenerator('dynamodb')
  );
  const eventStack = new SQSEventStack(app, service.StackNameGenerator('sqs'), {
    cognitoStack,
    dynamodbStack,
  });
  const s3Stack = new S3Stack(app, service.StackNameGenerator('s3'), {
    dynamodbStack,
    eventStack,
  });
  new EventBridgeEventStack(app, service.StackNameGenerator('event-bridge'), {
    dynamodbStack,
    s3Stack,
    eventStack,
  });
  new UserStack(app, service.StackNameGenerator('user'), {
    cognitoStack,
    dynamodbStack,
    s3Stack,
    eventStack,
  });
  new MedicalDataStack(app, service.StackNameGenerator('medical-data'), {
    cognitoStack,
    dynamodbStack,
  });
  new LifeEssentialStack(app, service.StackNameGenerator('life-essential'), {
    cognitoStack,
    dynamodbStack,
  });
  new ChallengeStack(app, service.StackNameGenerator('challenge'), {
    cognitoStack,
    dynamodbStack,
    s3Stack,
    eventStack,
  });
  new ContentStack(app, service.StackNameGenerator('content'), {
    cognitoStack,
    dynamodbStack,
    s3Stack,
    eventStack,
  });
  new NotificationsStack(app, service.StackNameGenerator('notification'), {
    cognitoStack,
    dynamodbStack,
  });
  new InvitationStack(app, service.StackNameGenerator('Invitation'), {
    cognitoStack,
    dynamodbStack,
  });
  new TermsStack(app, service.StackNameGenerator('terms'), {
    cognitoStack,
    dynamodbStack,
  });
} else {
  // eslint-disable-next-line no-console
  console.log('Missing env variables');
}
