{"name": "be-cdk-template", "version": "0.1.0", "bin": {"be-cdk-template": "bin/be-cdk-template.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "lint": "npx eslint -c .eslintrc.js ./ --fix", "typecheck": "tsc", "prepare": "husky install"}, "devDependencies": {"@types/dompurify": "^2.4.0", "@types/jest": "^29.4.0", "@types/jsdom": "^21.1.0", "@types/jsonwebtoken": "^9.0.2", "@types/lodash": "^4.14.183", "@types/node": "18.14.6", "@types/prettier": "2.6.0", "@types/uuid": "^9.0.0", "@types/validator": "^13.7.12", "@typescript-eslint/eslint-plugin": "^5.45.1", "@typescript-eslint/parser": "^5.45.1", "aws-cdk": "^2.37.1", "esbuild": "^0.19.5", "eslint": "^8.29.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.2", "jest": "^29.5.0", "prettier": "^2.8.3", "ts-interface-builder": "^0.3.3", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "~4.9.5"}, "dependencies": {"@aws-cdk/aws-codestar-alpha": "^2.38.0-alpha.0", "@aws-cdk/aws-iot": "^1.177.0", "@aws-cdk/aws-iotwireless": "^1.177.0", "@aws-cdk/aws-lambda-event-sources": "^1.203.0", "@aws-lambda-powertools/commons": "^1.5.1", "@aws-lambda-powertools/logger": "^1.5.1", "@aws-lambda-powertools/metrics": "^1.5.1", "@aws-lambda-powertools/tracer": "^1.5.1", "@aws-sdk/client-quicksight": "^3.370.0", "@aws-sdk/client-sqs": "^3.282.0", "@t3-oss/env-core": "^0.7.1", "@types/aws-lambda": "^8.10.102", "aws-cdk-lib": "^2.38.0", "aws-jwt-verify": "^4.0.0", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1396.0", "axios": "^0.27.2", "constructs": "^10.0.0", "date-fns": "^4.1.0", "dompurify": "^2.4.3", "dotenv": "^16.0.3", "i": "^0.3.7", "jsonwebtoken": "^8.5.1", "jwk-to-pem": "^2.0.5", "lodash": "^4.17.21", "nanoid": "^4.0.0", "npm": "^8.17.0", "path": "^0.12.7", "source-map-support": "^0.5.21", "ts-interface-checker": "^1.0.2", "typescript": "^4.9.3", "uuid": "^9.0.0", "validator": "^13.9.0", "xlsx": "^0.18.5", "yup": "^1.3.2", "zod": "^3.22.4"}}